import * as React from 'react'
import { cn } from '@/lib/utils'

const Input = React.forwardRef<HTMLInputElement, React.ComponentProps<'input'>>(
  (
      { className, type, ...props }, ref
  ) => {
    return (
        <input
            type={type}
            className={cn(
                'flex w-full placeholder:font-normal placeholder:text-base placeholder:text-grayTwo',
                className
            )}
            ref={ref}
            {...props}
        />
    )
  }
)
Input.displayName = 'Input'

export { Input }
