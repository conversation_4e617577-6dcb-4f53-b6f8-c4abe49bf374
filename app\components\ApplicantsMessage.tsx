import Image from 'next/image';
// import dynamic from 'next/dynamic';
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { ApplicationMessageProps } from '@/types';
import reply from '@/app/assets/svg/reply.svg';
import Reply from '@/app/assets/svg/reply.svg';
import Enrollee from '@/app/assets/img/enrollee.png';
import CopyToClipboard from '@/app/assets/svg/copy-to-clipboard.svg';
import TipTapDemo from './TipTapDemo';
// const TipTapDemo = dynamic(() => import('./TipTapDemo'), { ssr: false })


const ApplicantsMessage: React.FC<ApplicationMessageProps> = ({ 
    title, 
    message,
    senderName, 
    senderImage, 
    messagetime, 
    // replyContent,
    // onReplyClick
}) => {
    const [isCollapsed, setIsCollapsed] = useState(true);
     const [replyContent, setReplyContent] = useState('');

    const toggleCollapse = () => {
        setIsCollapsed(!isCollapsed);
        // onReplyClick(); // Call the callback function when the reply button is clicked
    };

    return (
        <div className=' border border-primaryColor/30 rounded-[10px] bg-white '>
            <h1 className={`text-base leading-5 font-semibold text-graySix p-4  border-tertiary/20 ${isCollapsed ? 'border-b': ''}`}>{title}</h1>
            <div className='py-4 pl-2.5 flex gap-2.5 border-b border-tertiary/20'>
                    {senderImage}
                <div className='pt-2.5 w-full'>
                    <div className='flex flex-col md:flex-row justify-between gap-2.5'>
                        <span className='text-sm leading-[17px] text-graySix font-semibold'>{senderName}</span>
                        <div className='flex flex-col md:flex-row justify-between md:px-2.5 px-0'>
                            <div className='flex items-center gap-5'>
                                <span className='text-xs leading-[14px] text-grayFour font-normal'>
                                    {messagetime}
                                </span>
                                <Image 
                                    src={CopyToClipboard}
                                    alt='copy to clipboard'
                                />
                                <Image 
                                    src={Reply}
                                    alt='reply'
                                />
                            </div>
                        </div>
                    </div>
                    <div className='pt-[18px] pr-4 pb-4 text-grayFive text-xs space-y-9'>
                        <p>
                            {message}
                        </p>
                        <Button onClick={toggleCollapse} className='bg-white border border-grayFive rounded-full shadow-none text-grayFive text-[10px] leading-3 font-medium hover:bg-grayOne'>
                            <Image
                                src={reply}
                                alt='reply icon'
                                width={12}
                                height={12}
                            />
                            Reply
                        </Button>
                    </div>
                </div>
            </div>
            {isCollapsed && (
                <div className='py-8 px-4'>
                    <div className='w-full'>
                        <TipTapDemo onSend={setReplyContent} />
                    </div>
                    {replyContent && (
                        <div className='flex gap-3.5 pt-4 w-full pl-2.5 pr-4'>
                            <Image
                                src={Enrollee}
                                alt='User Profile'
                                className='w-9 h-9 rounded-full object-cover'
                            />
                            <div 
                                className="text-grayFive text-sm leading-[19px] font-normal prose max-w-none" 
                                dangerouslySetInnerHTML={{ __html: replyContent }} 
                            />
                        </div>
                    )}
                </div>
            )}
            
        </div>
    )
}

export default ApplicantsMessage
