'use client';

import { useSelector } from 'react-redux';
import { RootState } from '../lib/redux/store';
import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(...inputs));
}

export const usePermissions = () => {
  const userActions = useSelector((state: RootState) => state.auth.user?.actions || []);
  const actionNames = userActions.map((action) => action.name);

  const hasPermission = (actionName: string): boolean => actionNames.includes(actionName);

  const hasFeatureAccess = (featureName: string): boolean =>
    userActions.some((action) => action.feature === featureName);

  const getActionsByFeature = (featureName: string): string[] =>
    userActions
      .filter((action) => action.feature === featureName)
      .map((action) => action.name);

  const hasAnyPermission = (actionsToCheck: string[]): boolean =>
    actionsToCheck.some((actionName) => hasPermission(actionName));

  const hasAllPermissions = (actionsToCheck: string[]): boolean =>
    actionsToCheck.every((actionName) => hasPermission(actionName));

  return {
    userActions,
    actionNames,
    hasPermission,
    hasFeatureAccess,
    getActionsByFeature,
    hasAnyPermission,
    hasAllPermissions,
  };
};
