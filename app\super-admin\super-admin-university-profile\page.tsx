import React from 'react';
import Heading from '@/app/components/Heading';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import TheUniversityOfChicago from '@/app/assets/svg/TheUniversityOfChicago';

const page = () => {
    return (
        <DashboardLayout>
            <Heading level='h1'>
                University Information
            </Heading>
            <div className='rounded-[16px] border-[0.5px] border-grayOne p-8 bg-white drop-shadow-[0_1px_2px_rgba(0,0,0,0.15)]'>
                <Heading className='font-semibold text-xl leading-none text-graySix' level='h2'>
                    UID # 567
                </Heading>
                <div className='flex gap-5 py-[30px]'>
                    <div>
                        <TheUniversityOfChicago />
                    </div>
                    <div className='flex flex-col'>
                        <Heading level='h3'>
                            The University of Chicago
                        </Heading>
                        <div className='flex gap-20 mt-3.5'>
                            <div className='flex flex-col gap-1.5'>
                                <span className='font-normal text-sm leading-none text-grayFour'>Website</span>
                                <span className='font-semibold text-sm leading-none underline text-primaryColor'>www.uchicago.edu</span>
                            </div>
                            <div className='flex flex-col gap-1.5'>
                                <span className='font-normal text-sm leading-none text-grayFour'>Email</span>
                                <span className='font-semibold text-sm leading-none underline text-primaryColor'><EMAIL></span>
                            </div>
                            <div className='flex flex-col gap-1.5'>
                                <span className='font-normal text-sm leading-none text-grayFour'>Location</span>
                                <span className='font-semibold text-sm leading-none text-grayFour'>5801 S Ellis Ave, Chicago, IL 60637, United States</span>
                            </div>
                            <div className='flex flex-col gap-1.5'>
                                <span className='font-normal text-sm leading-none text-grayFour'>Join Date</span>
                                <span className='font-semibold text-sm leading-none text-grayFour'>22 Oct 2014</span>
                            </div>
                            <div className='flex flex-col gap-1.5'>
                                <span className='font-normal text-sm leading-none text-grayFour'>World’s University Ranking</span>
                                <span className='font-semibold text-sm leading-none text-grayFour'>18</span>
                            </div>
                            <div className='flex flex-col gap-1.5'>
                                <span className='font-normal text-sm leading-none text-grayFour'>Preffered Applications</span>
                                <span className='font-semibold text-sm leading-none text-grayFour'>1254</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </DashboardLayout>
    )
}

export default page