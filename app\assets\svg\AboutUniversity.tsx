import React from 'react'

const AboutUniversity = () => {
    return (
            <svg width='100%' height='100%' viewBox='0 0 17 17' fill='none' xmlns='http://www.w3.org/2000/svg'>
                <mask id='mask0_2560_26944' style={{maskType : 'alpha'}} maskUnits='userSpaceOnUse' x='0' y='0' width='17' height='17'>
                <rect x='0.880859' y='0.290039' width='16' height='16' fill='#D9D9D9'/>
                </mask>
                <g mask='url(#mask0_2560_26944)'>
                <path d='M4.21484 10.9564V7.62305C4.21484 7.43416 4.27873 7.27583 4.40651 7.14805C4.53429 7.02027 4.69262 6.95638 4.88151 6.95638C5.0704 6.95638 5.22873 7.02027 5.35651 7.14805C5.48429 7.27583 5.54818 7.43416 5.54818 7.62305V10.9564C5.54818 11.1453 5.48429 11.3036 5.35651 11.4314C5.22873 11.5592 5.0704 11.623 4.88151 11.623C4.69262 11.623 4.53429 11.5592 4.40651 11.4314C4.27873 11.3036 4.21484 11.1453 4.21484 10.9564ZM8.21484 10.9564V7.62305C8.21484 7.43416 8.27873 7.27583 8.40651 7.14805C8.53429 7.02027 8.69262 6.95638 8.88151 6.95638C9.0704 6.95638 9.22873 7.02027 9.35651 7.14805C9.48429 7.27583 9.54818 7.43416 9.54818 7.62305V10.9564C9.54818 11.1453 9.48429 11.3036 9.35651 11.4314C9.22873 11.5592 9.0704 11.623 8.88151 11.623C8.69262 11.623 8.53429 11.5592 8.40651 11.4314C8.27873 11.3036 8.21484 11.1453 8.21484 10.9564ZM2.88151 14.2897C2.69262 14.2897 2.53429 14.2258 2.40651 14.098C2.27873 13.9703 2.21484 13.8119 2.21484 13.623C2.21484 13.4342 2.27873 13.2758 2.40651 13.148C2.53429 13.0203 2.69262 12.9564 2.88151 12.9564H14.8815C15.0704 12.9564 15.2287 13.0203 15.3565 13.148C15.4843 13.2758 15.5482 13.4342 15.5482 13.623C15.5482 13.8119 15.4843 13.9703 15.3565 14.098C15.2287 14.2258 15.0704 14.2897 14.8815 14.2897H2.88151ZM12.2148 10.9564V7.62305C12.2148 7.43416 12.2787 7.27583 12.4065 7.14805C12.5343 7.02027 12.6926 6.95638 12.8815 6.95638C13.0704 6.95638 13.2287 7.02027 13.3565 7.14805C13.4843 7.27583 13.5482 7.43416 13.5482 7.62305V10.9564C13.5482 11.1453 13.4843 11.3036 13.3565 11.4314C13.2287 11.5592 13.0704 11.623 12.8815 11.623C12.6926 11.623 12.5343 11.5592 12.4065 11.4314C12.2787 11.3036 12.2148 11.1453 12.2148 10.9564ZM14.8815 5.62305H2.81484C2.64818 5.62305 2.50651 5.56471 2.38984 5.44805C2.27318 5.33138 2.21484 5.18971 2.21484 5.02305V4.65638C2.21484 4.53416 2.2454 4.4286 2.30651 4.33971C2.36762 4.25082 2.44818 4.1786 2.54818 4.12305L8.28151 1.25638C8.4704 1.16749 8.6704 1.12305 8.88151 1.12305C9.09262 1.12305 9.29262 1.16749 9.48151 1.25638L15.1815 4.10638C15.3037 4.16194 15.3954 4.24527 15.4565 4.35638C15.5176 4.46749 15.5482 4.58416 15.5482 4.70638V4.95638C15.5482 5.14527 15.4843 5.3036 15.3565 5.43138C15.2287 5.55916 15.0704 5.62305 14.8815 5.62305ZM5.18151 4.28971H12.5815L8.88151 2.45638L5.18151 4.28971Z' fill='currentColor'/>
                </g>
            </svg>
    )
}

export default AboutUniversity;