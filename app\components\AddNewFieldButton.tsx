import React from 'react';
import PlusIcon from '@/app/assets/svg/plus';
import { AddNewFieldButtonProps } from '@/types';

const AddNewFieldButton: React.FC<AddNewFieldButtonProps> = ({ 
    onClick
 }) => {
    return (
        <div className='mt-[30px]'>
            <button 
                className='shadow-[0px_1px_2px_rgba(16, 24, 40, 0.05)] py-4 rounded-lg bg-primaryOne justify-center w-full flex gap-2 items-center font-semibold text-sm leading-5 text-primaryColor'
                onClick={onClick}
            >
                <PlusIcon />
                Add New Field
            </button>
        </div>
    )
}

export default AddNewFieldButton