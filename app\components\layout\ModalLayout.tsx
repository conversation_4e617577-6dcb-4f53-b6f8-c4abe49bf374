import React from 'react';
import ReactDOM from 'react-dom';
interface ModalLayoutProps {
    open: boolean;
    onClose: () => void;
    children: React.ReactNode;
}

const ModalLayout = ({ open, onClose, children }: ModalLayoutProps) => {
    if (!open) return null;

    if (typeof document === 'undefined') {
        return null;
    }

    return ReactDOM.createPortal(
        <div 
            className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-30 z-50 pointer-events-auto"
            onClick={onClose}
        >
            <div 
                className="bg-white rounded-[12px] p-6 w-auto min-w-[400px] shadow-lg"
                onClick={(e) => e.stopPropagation()}
            >
                {children}
            </div>
        </div>,
        document.body
    );
}

export default ModalLayout