import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import {
  LoginInput,
  RegisterInput,
  SendOTPInput,
  VerifyOTPInput,
  SSOLoginProps,
  OTPResponse,
  LoginResponse,
} from '@/types';

const AUTH_BASE_URL = process.env.NEXT_PUBLIC_AUTH_BASE_URL;

export const authApi = createApi({
  reducerPath: 'authApi',
  baseQuery: fetchBaseQuery({ baseUrl: `${AUTH_BASE_URL}/auth` }),
  endpoints: (builder) => ({
    login: builder.mutation<LoginResponse, LoginInput>({
      query: (credentials) => ({
        url: '/login',
        method: 'POST',
        body: credentials,
      }),
    }),
    register: builder.mutation<any, RegisterInput>({
      query: (data) => ({
        url: '/register',
        method: 'POST',
        body: data,
      }),
    }),
    sendOTP: builder.mutation<OTPResponse, SendOTPInput>({
      query: (data) => ({
        url: '/generate-otp',
        method: 'POST',
        body: data,
      }),
    }),
    verifyOTP: builder.mutation<OTPResponse, VerifyOTPInput>({
      query: (data) => ({
        url: '/verify-otp',
        method: 'POST',
        body: data,
      }),
    }),
    googleSSO: builder.mutation<any, SSOLoginProps>({
      query: (data) => ({
        url: '/sso',
        method: 'POST',
        body: data,
      }),
    }),
    logout: builder.mutation<boolean, string>({
      query: (token) => ({
        url: '/logout',
        method: 'POST',
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }),
    }),
  }),
});

export const {
  useLoginMutation,
  useRegisterMutation,
  useSendOTPMutation,
  useVerifyOTPMutation,
  useGoogleSSOMutation,
  useLogoutMutation,
} = authApi;
