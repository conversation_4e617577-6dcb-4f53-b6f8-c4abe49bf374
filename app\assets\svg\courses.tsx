import React from 'react';
import { IconProps } from '@/types';

const Courses: React.FC<IconProps> = ( {className} ) => {
    return (
        <div>
            <svg className={className} width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_2875_4969" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="25">
            <rect y="0.5" width="24" height="24" fill="#D9D9D9"/>
            </mask>
            <g mask="url(#mask0_2875_4969)">
            <path d="M6.75 22.4996C6 22.4996 5.35417 22.2454 4.8125 21.7371C4.27083 21.2288 4 20.5996 4 19.8496V5.89961C4 5.26628 4.19583 4.69961 4.5875 4.19961C4.97917 3.69961 5.49167 3.38294 6.125 3.24961L13.625 1.77461C14.2417 1.64128 14.7917 1.77461 15.275 2.17461C15.7583 2.57461 16 3.09128 16 3.72461V15.6496C16 16.1329 15.85 16.5621 15.55 16.9371C15.25 17.3121 14.8667 17.5413 14.4 17.6246L6.525 19.1996C6.375 19.2329 6.25 19.3121 6.15 19.4371C6.05 19.5621 6 19.6996 6 19.8496C6 20.0329 6.075 20.1871 6.225 20.3121C6.375 20.4371 6.55 20.4996 6.75 20.4996H18V5.49961C18 5.21628 18.0958 4.97878 18.2875 4.78711C18.4792 4.59544 18.7167 4.49961 19 4.49961C19.2833 4.49961 19.5208 4.59544 19.7125 4.78711C19.9042 4.97878 20 5.21628 20 5.49961V20.4996C20 21.0496 19.8042 21.5204 19.4125 21.9121C19.0208 22.3038 18.55 22.4996 18 22.4996H6.75ZM9 16.6746L14 15.6996V3.74961L9 4.72461V16.6746ZM7 17.0746V5.12461L6.625 5.19961C6.44167 5.23294 6.29167 5.31211 6.175 5.43711C6.05833 5.56211 6 5.71628 6 5.89961V17.3246C6.08333 17.2913 6.17083 17.2621 6.2625 17.2371C6.35417 17.2121 6.44167 17.1913 6.525 17.1746L7 17.0746Z" fill="currentColor"/>
            </g>
            </svg>

        </div>
    );
};

export default Courses;
