import clsx from 'clsx';
import React from 'react';
import { HeadingProps } from '@/types';

const Heading: React.FC<HeadingProps> = ({ 
    level, 
    children, 
    className 
}) => {
    const Tag = level;

    return (
        <Tag className={`text-graySix ${className || getHeadingStyles(level)}`}>
            {children}
        </Tag>
    )
}

const getHeadingStyles = (level: HeadingProps['level']) => {
    switch (level) {
        case 'h1':
            return 'text-[28px] font-bold leading-8';
        case 'h2':
            return 'text-2xl font-semibold leading-7';
        case 'h3':
            return 'text-[22px] font-semibold leading-6';
        case 'h4':
            return 'text-xl font-semibold leading-6';
        case 'h5':
            return 'text-lg font-medium leading-5';
        case 'h6':
            return 'text-base font-medium leading-5';
        default:
            return '';
    }
};

export default Heading