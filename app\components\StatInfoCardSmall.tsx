import Link from 'next/link';
import Image from 'next/image';
import { StatInfoCardSmallProps } from '@/types'
import ArrowDown from '@/app/assets/svg/arrow_downward';

const StatInfoCardSmall: React.FC<StatInfoCardSmallProps> = ({
    link,
    number,
    imageSrc,
    increase,
    decrease,
    description,
}) => {

    const formatNumber = (num: number) => {
        if (num >= 1000000) {
            return `${(num / 1000000).toFixed(1).replace(/\.0$/, '')}M`;
        } else if (num >= 1000) {
            return `${(num / 1000).toFixed(1).replace(/\.0$/, '')}K`;
        }
        return num.toString();
    };

    const formattedNumber = formatNumber(number);
    
    const content = (
        <div className='flex p-4 justify-between'>
            <div className='flex gap-4 items-center'>
                <Image
                    src={imageSrc}
                    alt='add icon'
                    height={44}
                    width={44}
                />
                <div className='flex flex-col gap-1.5'>
                    <span className='text-graySix font-bold text-[28px] leading-[34px]'>
                    {formattedNumber}
                    </span>
                    <span className='text-grayThree font-light text-sm leading-[17px]'>
                        {description}
                    </span>
                </div>
            </div>
            {decrease !== undefined && (
                <div className='h-fit w-fit flex items-center gap-[2px] text-[#FF3B30] bg-[#FF3B301A] py-0.5 px-1 rounded'>
                    <ArrowDown className='mt-[2px]' />
                    <span className='text-[10px] leading-3 font-semibold'>
                        {decrease}
                    </span>
                </div>
            )}
            {increase !== undefined && (
                <div className='h-fit w-fit flex items-center gap-[2px] text-secondaryColor bg-primaryOne py-0.5 px-1 rounded'>
                    <ArrowDown className=' rotate-180' />
                    <span className='text-[10px] leading-3 font-semibold'>
                        {increase}
                    </span>
                </div>
            )}
        </div>
    )

    return link ? (
            <Link href={link}>
                {content}
            </Link>
        ) : (
            <div>
                {content}
            </div>
        );
};

export default StatInfoCardSmall;
