import React from 'react';
import Tick from '../assets/svg/Tick';
import { BadgeStatusProps } from '@/types';

const BadgeStatus: React.FC<BadgeStatusProps> = ({ StatusProps }) => {

    const lastAchievedIndex = StatusProps.map(
        (step) => step.achieved
    ).lastIndexOf(true);

    return (
        <ol className="overflow-hidden space-y-8">
            {StatusProps.map((step, index) => {
                const isLatestAchieved = index === lastAchievedIndex;

                return (
                    <li
                        key={index}
                        className="relative flex-1 h-full after:bg-primaryColor"
                    >
                        <div className="flex w-full">
                            <div className="w-[5%] grid grid-flow-row">
                                <div className="relative flex flex-col items-center justify-center">
                                    <div
                                        className={`w-5 h-5 flex justify-center items-center rounded-full border ${
                                            step.achieved
                                                ? 'bg-primaryColor border-primaryColor'
                                                : 'bg-white border-grayTwo'
                                        }`}
                                    >
                                        {step.achieved ? (
                                            <div className="w-[8px] h-[8px] rounded-full bg-white"></div>
                                        ) : (
                                            <div className="w-[8px] h-[8px] rounded-full bg-grayThree"></div>
                                        )}
                                    </div>
                                    {index !== StatusProps.length - 1 && (
                                        <div
                                            className={`absolute top-[94px] w-[2px] h-full ${
                                                step.achieved
                                                    ? 'bg-primaryColor'
                                                    : 'bg-grayThree'
                                            }`}
                                        ></div>
                                    )}
                                </div>
                            </div>

                            <div className="w-[95%] min-h-[156px]">
                                <div
                                    className={`h-full p-4 rounded-xl flex items-center gap-[26px] border ${
                                        isLatestAchieved
                                            ? 'bg-primaryOne border-primaryColor' 
                                            : 'bg-white border-[#CFE0FFCC]'
                                    }`}
                                >
                                    <step.icon />
                                    <div className="flex flex-col gap-2.5">
                                        {isLatestAchieved && (
                                            <p className='font-medium text-grayFour'>
                                                <span className='text-primaryColor'>You are here Now! </span>
                                                Submit More 65 unique applicants to advance to Platinum
                                            </p>
                                            
                                        )}
                                        <h3 className="text-lg font-normal text-primaryColor flex justify-between">
                                            {step.heading}
                                        </h3>
                                        <p className="text-base font-medium text-grayThree">
                                            {step.description}
                                        </p>
                                        <div className="flex gap-2.5 text-sm text-grayFour">
                                            {step.attributes.map(
                                                (attribute: string, index) => (
                                                    <span
                                                        key={index}
                                                        className="flex items-center gap-2.5"
                                                    >
                                                        <Tick /> {attribute}
                                                    </span>
                                                )
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                );
            })}
        </ol>
    );
};

export default BadgeStatus;
