import React from 'react'
import { xIconProps } from '@/types';

const Cross: React.FC<xIconProps> = ({ className }) => {
    return (
        <svg className={className} width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_4220_22049" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="18" height="18">
                <rect width="18" height="18" fill="currentColor"/>
            </mask>
            <g mask="url(#mask0_4220_22049)">
                <path d="M8.99961 10.0501L5.32461 13.7251C5.18711 13.8626 5.01211 13.9313 4.79961 13.9313C4.58711 13.9313 4.41211 13.8626 4.27461 13.7251C4.13711 13.5876 4.06836 13.4126 4.06836 13.2001C4.06836 12.9876 4.13711 12.8126 4.27461 12.6751L7.94961 9.0001L4.27461 5.3251C4.13711 5.1876 4.06836 5.0126 4.06836 4.8001C4.06836 4.5876 4.13711 4.4126 4.27461 4.2751C4.41211 4.1376 4.58711 4.06885 4.79961 4.06885C5.01211 4.06885 5.18711 4.1376 5.32461 4.2751L8.99961 7.9501L12.6746 4.2751C12.8121 4.1376 12.9871 4.06885 13.1996 4.06885C13.4121 4.06885 13.5871 4.1376 13.7246 4.2751C13.8621 4.4126 13.9309 4.5876 13.9309 4.8001C13.9309 5.0126 13.8621 5.1876 13.7246 5.3251L10.0496 9.0001L13.7246 12.6751C13.8621 12.8126 13.9309 12.9876 13.9309 13.2001C13.9309 13.4126 13.8621 13.5876 13.7246 13.7251C13.5871 13.8626 13.4121 13.9313 13.1996 13.9313C12.9871 13.9313 12.8121 13.8626 12.6746 13.7251L8.99961 10.0501Z" fill="currentColor"/>
            </g>
        </svg>
    )
}

export default Cross