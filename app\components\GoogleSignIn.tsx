'use client';

import { usePathname, useRouter } from 'next/navigation';
import GoogleIcon from '../assets/svg/GoogleIcon';
import { useGoogleSSOMutation } from '@/lib/redux/api/authApi';
import { useEffect } from 'react';

const GoogleSignIn = () => {
  const router = useRouter();
  const pathname = usePathname();
  const [googleSSO, { isLoading, data, isSuccess }] = useGoogleSSOMutation();

  const handleGoogleLogin = async () => {
    try {
      const res = await googleSSO({
          provider: 'google',
          idToken: '',
          email: '',
          name: ''
      }).unwrap();

      if (res?.redirectUrl) {
        window.location.href = res.redirectUrl;
      } else {
        console.error('No redirect URL returned from backend.');
      }
    } catch (error) {
      console.error('Google SSO Error:', error);
    }
  };

  return (
    <button
      type="button"
      onClick={handleGoogleLogin}
      disabled={isLoading}
      className="flex justify-center gap-3 py-2.5 rounded-[8px] bg-white hover:bg-grayOne border border-tertiary border-opacity-20 text-graySix w-full px-2"
    >
      <span className="w-6 h-6">
        <GoogleIcon />
      </span>
      {isLoading ? 'Redirecting...' : `Sign ${pathname.includes('login') ? 'in' : 'up'} with Google`}
    </button>
  );
};

export default GoogleSignIn;
