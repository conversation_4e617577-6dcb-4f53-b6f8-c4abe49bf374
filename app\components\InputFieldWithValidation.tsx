import React, {useState} from 'react'

interface InputFieldWithValidationProps {
    label: string;
    type: 'text' | 'email' | 'password' | 'number';
    name: string;
    value: string;
    onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}
  
const InputFieldWithValidation: React.FC<InputFieldWithValidationProps> = ({ 
    type, 
    name, 
    label, 
    value, 
    onChange 
}) => {
    const [error, setError] = useState('');

    const validateInput = (value: string): string => {
        let regex;
        switch (type) {
        case 'text':
            regex = /^[A-Za-z\s]+$/; 
            return regex.test(value) || value === '' ? '' : 'Only letters and spaces allowed';

        case 'email':
            regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return regex.test(value) || value === '' ? '' : 'Invalid email format';

        case 'password':
            regex = /^(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$/;

            return regex.test(value) || value === '' ? ''
            : 'Password must be 8+ chars, 1 uppercase & 1 number';

        case 'number':
            regex = /^[0-9]+$/;
            return regex.test(value) || value === '' ? '' : 'Only numbers allowed';

        default:
            return '';
        }
    };

    const handleChange = (e:any) => {
        const { value } = e.target;
        onChange(e); 
        setError(validateInput(value));
    };
    
    return (
        <div className='mb-4'>
            <label className='block text-gray-700'>{label}:</label>
            <input
                type={type}
                name={name}
                value={value}
                onChange={handleChange}
                className='w-full p-2 border rounded mt-1'
            />

            {error && <p className='text-red-500 text-sm'>{error}</p>}
      </div>
  
    )
}

export default InputFieldWithValidation