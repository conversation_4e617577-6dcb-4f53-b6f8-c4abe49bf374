// components/HorizontalChart.tsx
import React, { ElementType } from 'react';
import Link from 'next/link';
import ArrowRight from '@/app/assets/svg/ArrowRight';
import ArrowForward from '@/app/assets/svg/arrow_forward';
import ChevronForward from '@/app/assets/svg/chevron_forward';
import { 
    Card, 
    CardContent, 
    CardHeader, 
    CardTitle 
} from '@/components/ui/card';

type DataItem = {
    label?: string;
    value: number;
    Icon?: ElementType;
    // maxValue: number;
};

type HorizontalChartProps = {
    data: DataItem[];
    title?: string;
};

const HorizontalChart: React.FC<HorizontalChartProps> = ({ 
    data , 
    title = 'Agencies Students',
}) => {
    // Calculate the raw maximum value from the data
    const rawMaxValue = Math.max(...data.map((item) => item.value));

    // Round up to the next "nice" value (e.g., 100, 1000, etc.)
    const maxValue = Math.ceil(rawMaxValue / 100) * 100;
    return (
        <Card className="drop-shadow-none shadow-none border-none space-y-[30px]">
            <CardHeader className='pb-0'>
                <CardTitle className=" flex md:flex-row flex-col gap-4 justify-between">
                    <div className="flex items-center gap-2.5 md:text-xl text-base md:leading-[29px] leading-5 text-grayFive font-bold">
                        <h2>{title}</h2>
                    </div>
                    <Link href={' '} className='flex items-center gap-2 font-medium text-sm bg-primaryOne py-2 px-3 leading-[17px] rounded-[22px] text-secondaryColor'>
                        View All
                        <ArrowForward className='w-[10px] mt-0.5' />
                    </Link>
                </CardTitle>
            </CardHeader>
            <CardContent>
                <div className="space-y-[18px]">
                    {data.map((item, index) => {
                        const percentage = (item.value / maxValue) * 100; // Calculate percentage based on the rounded maxValue
                        return (
                            <div className={` ${item.Icon ? 'flex gap-5':''}`} key={index}>
                                {item.Icon && (
                                    <item.Icon /> 
                                )}
                                {item.label && (
                                    <div className="mb-2.5 text-sm font-medium text-graySix">
                                        {item.label}
                                    </div>
                                )}
                                <div className="relative w-full h-6 bg-primaryOne rounded-xl">
                                    <div
                                        className="absolute top-0 left-0 h-6 bg-primaryColor rounded-xl flex items-center justify-end pr-3 text-white text-sm"
                                        style={{ width: `${percentage}%` }}
                                    >
                                        {item.value}
                                    </div>
                                </div>
                            </div>
                        );
                    })}
                </div>
            </CardContent>
        </Card>
    );
};

export default HorizontalChart;
