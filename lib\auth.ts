import NextAuth from "next-auth";
import { loginUser, verifyOTP, googleSS<PERSON>ogin } from "./api";
import Google from "next-auth/providers/google";
import Credentials from "next-auth/providers/credentials";

export const { 
    auth, 
    signIn,
    signOut,
    handlers: { GET, POST } 
} = NextAuth({
    providers: [
        Google({
            clientId: process.env.GOOGLE_CLIENT_ID,
            clientSecret: process.env.GOOGLE_CLIENT_SECRET,
            authorization: {
                params: {
                    prompt: "consent",
                    access_type: "offline",
                    response_type: "code",
                },
            },
        }),
        Credentials({
            name: 'Credentials',
            credentials: {
                email: { label: "Email", type: "email" },
                password: { label: "Password", type: "password" },
            },
            async authorize(credentials) {
                if (!credentials?.email || !credentials?.password) return null;

                try {
                    const user = await loginUser({
                        email: credentials.email as string,
                        password: credentials.password as string,
                    })

                    if(!user.success || !user.user || !user.user.email || !user.user.id) {
                        return null;
                    }

                    const roleNemes = user.user?.roles?.map((role) => role.name || '') || [];
                    const actions = user.user?.roles?.flatMap((role) => 
                        role.actions?.map((action) => ({
                            name: action.name,
                            feature: action.feature.name
                    })) || []) || [];
                    
                    return {
                        id: String(user.user?.id),
                        email: user.user?.email,
                        name: user.user?.name || user.user?.email.split('@')[0],
                        roles: roleNemes,
                        actions: actions,
                        accessToken: user.accessToken,
                        refreshToken: user.refreshToken,
                        userData: user.user
                    }
                } catch (error) {
                    console.error("Auth error:", error);
                    return null;
                }
            }
        }),
        Credentials({
            name: 'OTP Verification',
            credentials: {
                email: { label: "Email", type: "email" },
                otp: { label: "OTP", type: "text" },
                type: { label: "Type", type: "text" }, 
            },
            async authorize(credentials) {
                if (!credentials?.email || !credentials?.otp) {
                    return null;
                }
                
                try {
                    const result = await verifyOTP({
                        email: credentials.email as string,
                        otp: credentials.otp as string,
                        type: (credentials.type as any) || 'login',
                    });

                    if (result.success && result.user) {
                        return {
                            id: result.user.id,
                            email: result.user.email,
                            name: result.user.name,
                            accessToken: result.accessToken,
                            refreshToken: result.refreshToken,
                        };
                    }
                    return null;
                } catch (error) {
                    console.error("OTP verification error:", error);
                    return null;
                }
            }
        })
    ],
    callbacks: {
        async signIn({ user, account, profile }) {
            if(account?.provider === 'google' && account?.id_token) {
                try {
                    const response = await googleSSOLogin({
                        provider: account.provider,
                        token: account.id_token,
                        email: user?.email || '',
                        name: user?.name || '',
                    });
                    
                    if (!response.success) {
                        console.error('Google SSO login failed:', response);
                        return false;
                    }
                    
                    // Store the token in the user object for later use
                    (user as any).idToken = account.id_token;
                    return true;
                } catch (error) {
                    console.error('Google SSO login error:', error);
                    return false;
                }
            }
            return true; // Allow other sign-in methods
        },
        async jwt({ token, user, account }) {
            if (user) {
                token.id = user.id;
                token.email = user.email;
                token.name = user.name;
                token.roles = user.roles;
                token.actions = user.actions;
                token.accessToken = (user as any).accessToken;
                token.refreshToken = (user as any).refreshToken;
                token.userData = user.userData;
                token.idToken = (user as any).idToken; // Store the ID token
            }
            
            return token;
        },
        async session({ session, token }) {
            if (session.user) {
                session.user.id = token.id as string;
                session.user.email = token.email as string;
                session.user.name = token.name as string;
                session.user.roles = token.roles as string[];
                session.user.actions = token.actions as any[];
                session.user.userData = token.userData as any;
            }
            (session as any).accessToken = token.accessToken;
            (session as any).refreshToken = token.refreshToken;
            (session as any).idToken = token.idToken; // Include the ID token in the session

            return session;
        },
    },
    session: {
        strategy: "jwt",
    },
    pages: {
        signIn: "/login",
    },
    secret: process.env.NEXTAUTH_SECRET,
});