'use client'

import React, { useState, useEffect } from 'react';
import { MultiSelectWithGroupProps } from '@/types';
import { Checkbox } from '@/components/ui/checkbox';
import {
    Select,
    SelectItem,
    SelectGroup,
    SelectLabel,
    SelectValue,
    SelectTrigger,
    SelectContent,
} from '@/components/ui/select';

const MultiSelectWithGroup: React.FC<MultiSelectWithGroupProps> = ({
    selectedLists,
    label = 'Program',
    placeholder = 'Select program',
    onChange,
    initialValues = []
}) => {
    const [selectedValues, setSelectedValues] = useState<string[]>(initialValues);
    
    // When selected values change, trigger onChange callback
    useEffect(() => {
        if (onChange) {
            onChange(selectedValues);
        }
    }, [selectedValues, onChange]);

    // Display the number of selected items in the trigger
    const displayValue = selectedValues.length > 0 
        ? `${selectedValues.length} selected` 
        : placeholder;

    return (
        // <Select>
        //     <SelectTrigger className='w-[280px]'>
        //         <SelectValue placeholder='Select a timezone' />
        //     </SelectTrigger>
        //     {/* <SelectContent className='px-2.5'>
        //         {selectedLists.map((list, index) => (
        //             <SelectGroup key={index}>
        //                 <SelectLabel className='px-1 font-medium text-[10px] leading-none text-primaryColor border-b border-primaryColor'>{list.label}</SelectLabel>
        //                 {list.selectItem.map((item, index) => (
        //                     <SelectItem 
        //                         key={index} 
        //                         value='est'
        //                         className='font-normal text-xs leading-none text-grayFive cursor-pointer'
        //                     >
        //                         {item}
        //                     </SelectItem>
        //                 ))}
        //             </SelectGroup>
        //         ))}
        //     </SelectContent> */}
        //     <SelectContent className='bg-white p-0 max-h-80'>
        //             {selectedLists.map((list, index) => (
        //                 <SelectGroup key={index} className='py-2'>
        //                     <SelectLabel className='text-blue-500 text-sm font-medium border-b border-blue-100 px-4 pb-1 mb-2'>
        //                         {list.label}
        //                     </SelectLabel>
        //                     {list.selectItem.map((item, itemIndex) => (
        //                         <div 
        //                             key={itemIndex}
        //                             className='px-4 py-2 flex items-center space-x-2 hover:bg-gray-50'
        //                         >
        //                             <Checkbox
        //                                 id={`${list.label}-${itemIndex}`}
        //                                 checked={selectedValues.includes(item)}
        //                                 onCheckedChange={(checked) => {
        //                                     if (checked) {
        //                                         setSelectedValues(prev => [...prev, item]);
        //                                     } else {
        //                                         setSelectedValues(prev => prev.filter(val => val !== item));
        //                                     }
        //                                     if (onChange) {
        //                                         onChange(selectedValues);
        //                                     }
        //                                 }}
        //                                 onClick={(e) => e.stopPropagation()}
        //                                 className='rounded-sm border-gray-300 h-4 w-4'
        //                             />
        //                             <label 
        //                                 htmlFor={`${list.label}-${itemIndex}`}
        //                                 className='text-sm text-gray-700 cursor-pointer flex-1'
        //                                 onClick={(e) => e.stopPropagation()}
        //                             >
        //                                 {item}
        //                             </label>
        //                         </div>
        //                     ))}
        //                     {index < selectedLists.length - 1 && (
        //                         <div className='border-t border-gray-100 my-1'></div>
        //                     )}
        //                 </SelectGroup>
        //             ))}
        //         </SelectContent>
        // </Select>
        <div className="w-full">
            <label className="block text-xs font-medium text-gray-700 mb-1.5">{label}</label>
            <Select>
                <SelectTrigger className="w-full bg-white">
                    <SelectValue placeholder={placeholder}>
                        {displayValue}
                    </SelectValue>
                </SelectTrigger>
                <SelectContent className="bg-white p-0 max-h-80">
                    {selectedLists.map((list, index) => (
                        <SelectGroup key={index} className="py-2">
                            <SelectLabel className="text-blue-500 text-sm font-medium border-b border-blue-100 px-4 pb-1 mb-2">
                                {list.label}
                            </SelectLabel>
                            {list.selectItem.map((item, itemIndex) => (
                                <div 
                                    key={itemIndex}
                                    className="px-4 py-2 flex items-center space-x-2 hover:bg-gray-50"
                                >
                                    <Checkbox
                                        id={`${list.label}-${itemIndex}`}
                                        checked={selectedValues.includes(item)}
                                        onCheckedChange={(checked) => {
                                            if (checked) {
                                                setSelectedValues(prev => [...prev, item]);
                                            } else {
                                                setSelectedValues(prev => prev.filter(val => val !== item));
                                            }
                                        }}
                                        onClick={(e) => e.stopPropagation()}
                                        className="rounded-sm border-gray-300 h-4 w-4"
                                    />
                                    <label 
                                        htmlFor={`${list.label}-${itemIndex}`}
                                        className="text-sm text-gray-700 cursor-pointer flex-1"
                                        onClick={(e) => e.stopPropagation()}
                                    >
                                        {item}
                                    </label>
                                </div>
                            ))}
                            {index < selectedLists.length - 1 && (
                                <div className="border-t border-gray-100 my-1"></div>
                            )}
                        </SelectGroup>
                    ))}
                </SelectContent>
            </Select>
        </div>
    )
}

export default MultiSelectWithGroup