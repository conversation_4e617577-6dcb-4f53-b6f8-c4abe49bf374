'use client';

import { z } from 'zod';
import Link from 'next/link';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { useRouter } from 'next/navigation';
import { signUpRecruitmentPartnerFormSchema } from '@/schema';
import { Button } from '@/components/ui/button';
import EyeIcon from '@/app/assets/svg/visibility';
import { zodResolver } from '@hookform/resolvers/zod';
import VisibilityOff from '@/app/assets/svg/visibility_off';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { useRegisterMutation } from '@/lib/redux/api/authApi';

const RecruitementPartnerSignup = () => {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const togglePasswordVisibility = () => setShowPassword((prev) => !prev);
  const toggleConfirmPasswordVisibility = () =>
    setShowConfirmPassword((prev) => !prev);

  type SignUpFormValues = z.infer<typeof signUpRecruitmentPartnerFormSchema>;

  const form = useForm<SignUpFormValues>({
    resolver: zodResolver(signUpRecruitmentPartnerFormSchema),
    defaultValues: {
      name: '',
      email: '',
      organizationName: '',
      password: '',
      confirmPassword: '',
    },
  });

  const [registerUser, { isLoading, isError, error }] = useRegisterMutation();

  const onSubmit = async (values: SignUpFormValues) => {
    try {
      const res = await registerUser({ ...values, roleName: 'Agency' }).unwrap();

      if (!res.success || res.message?.includes('already')) {
        form.setError('email', {
          type: 'manual',
          message: 'Existing account found. Choose another email.',
        });
        return;
      }

      sessionStorage.setItem('verificationEmail', values.email);
      form.reset();
      router.push('/verify-otp');
    } catch (err: any) {
      console.error('Registration error:', err?.data?.message || err.message);
    }
  };

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          {/* Name */}
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem className="space-y-1.5">
                <FormLabel className="text-sm font-medium text-graySix">Name*</FormLabel>
                <FormControl>
                  <input
                    type="text"
                    placeholder="Enter your name"
                    className="flex w-full rounded-[8px] outline-none border border-tertiary border-opacity-20 py-2.5 px-4 placeholder:text-grayThree"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Email */}
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem className="space-y-1.5 mt-5">
                <FormLabel className="text-sm font-medium text-graySix">Email*</FormLabel>
                <FormControl>
                  <input
                    type="email"
                    placeholder="Enter your email"
                    className="flex w-full rounded-[8px] outline-none border border-tertiary border-opacity-20 py-2.5 px-4 placeholder:text-grayThree"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Organization Name */}
          <FormField
            control={form.control}
            name="organizationName"
            render={({ field }) => (
              <FormItem className="space-y-1.5 mt-5">
                <FormLabel className="text-sm font-medium text-graySix">Organization Name*</FormLabel>
                <FormControl>
                  <input
                    type="text"
                    placeholder="Enter your organization name"
                    className="flex w-full rounded-[8px] outline-none border border-tertiary border-opacity-20 py-2.5 px-4 placeholder:text-grayThree"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Password */}
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem className="space-y-1.5 mt-5">
                <FormLabel className="text-sm font-medium text-graySix">Password*</FormLabel>
                <FormControl>
                  <div className="relative">
                    <input
                      type={showPassword ? 'text' : 'password'}
                      placeholder="Enter your password"
                      className="flex w-full rounded-[8px] outline-none border border-tertiary border-opacity-20 py-2.5 px-4 placeholder:text-grayThree"
                      {...field}
                    />
                    <button
                      type="button"
                      onClick={togglePasswordVisibility}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2"
                    >
                      {showPassword ? <VisibilityOff /> : <EyeIcon className="text-grayThree" />}
                    </button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Confirm Password */}
          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem className="space-y-1.5 mt-5">
                <FormLabel className="text-sm font-medium text-graySix">Confirm Password*</FormLabel>
                <FormControl>
                  <div className="relative">
                    <input
                      type={showConfirmPassword ? 'text' : 'password'}
                      placeholder="Confirm your password"
                      className="flex w-full rounded-[8px] outline-none border border-tertiary border-opacity-20 py-2.5 px-4 placeholder:text-grayThree"
                      {...field}
                    />
                    <button
                      type="button"
                      onClick={toggleConfirmPasswordVisibility}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2"
                    >
                      {showConfirmPassword ? (
                        <VisibilityOff />
                      ) : (
                        <EyeIcon className="text-grayThree" />
                      )}
                    </button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Error Message */}
          {isError && (
            <p className="text-red-500 text-sm mt-2">
              {(error as any)?.data?.message || 'Registration failed.'}
            </p>
          )}

          {/* Submit */}
          <Button
            type="submit"
            disabled={isLoading}
            className="w-full bg-primaryColor rounded-[8px] py-2.5 mt-6 mb-5 hover:bg-tertiary text-white text-base font-semibold"
          >
            {isLoading ? 'Creating account...' : 'Create account'}
          </Button>
        </form>
      </Form>

      <div className="flex justify-center gap-1 mt-8">
        <span className="text-sm font-normal text-grayFive">Already have an account?</span>
        <Link className="text-sm font-semibold text-primaryColor" href="/login">
          Log in
        </Link>
      </div>
    </div>
  );
};

export default RecruitementPartnerSignup;
