import React from 'react';

const attach_money = () => {
    return (
        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_3698_18317" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="12" height="12">
            <rect width="12" height="12" fill="#D9D9D9"/>
            </mask>
            <g mask="url(#mask0_3698_18317)">
            <path d="M6.01334 10.5C5.87167 10.5 5.75292 10.4521 5.65709 10.3562C5.56126 10.2604 5.51334 10.1417 5.51334 10V9.425C5.13834 9.34167 4.80917 9.19583 4.52584 8.9875C4.24251 8.77917 4.01334 8.4875 3.83834 8.1125C3.78001 7.99583 3.77792 7.87292 3.83209 7.74375C3.88626 7.61458 3.98417 7.52083 4.12584 7.4625C4.24251 7.4125 4.36334 7.41458 4.48834 7.46875C4.61334 7.52292 4.70917 7.6125 4.77584 7.7375C4.91751 7.9875 5.09667 8.17708 5.31334 8.30625C5.53001 8.43542 5.79667 8.5 6.11334 8.5C6.45501 8.5 6.74459 8.42292 6.98209 8.26875C7.21959 8.11458 7.33834 7.875 7.33834 7.55C7.33834 7.25833 7.24667 7.02708 7.06334 6.85625C6.88001 6.68542 6.45501 6.49167 5.78834 6.275C5.07167 6.05 4.58001 5.78125 4.31334 5.46875C4.04667 5.15625 3.91334 4.775 3.91334 4.325C3.91334 3.78333 4.08834 3.3625 4.43834 3.0625C4.78834 2.7625 5.14667 2.59167 5.51334 2.55V2C5.51334 1.85833 5.56126 1.73958 5.65709 1.64375C5.75292 1.54792 5.87167 1.5 6.01334 1.5C6.15501 1.5 6.27376 1.54792 6.36959 1.64375C6.46542 1.73958 6.51334 1.85833 6.51334 2V2.55C6.83001 2.6 7.10501 2.70208 7.33834 2.85625C7.57167 3.01042 7.76334 3.2 7.91334 3.425C7.98834 3.53333 8.00292 3.65417 7.95709 3.7875C7.91126 3.92083 7.81751 4.01667 7.67584 4.075C7.55917 4.125 7.43834 4.12708 7.31334 4.08125C7.18834 4.03542 7.07167 3.95417 6.96334 3.8375C6.85501 3.72083 6.72792 3.63125 6.58209 3.56875C6.43626 3.50625 6.25501 3.475 6.03834 3.475C5.67167 3.475 5.39251 3.55625 5.20084 3.71875C5.00917 3.88125 4.91334 4.08333 4.91334 4.325C4.91334 4.6 5.03834 4.81667 5.28834 4.975C5.53834 5.13333 5.97167 5.3 6.58834 5.475C7.16334 5.64167 7.59876 5.90625 7.89459 6.26875C8.19042 6.63125 8.33834 7.05 8.33834 7.525C8.33834 8.11667 8.16334 8.56667 7.81334 8.875C7.46334 9.18333 7.03001 9.375 6.51334 9.45V10C6.51334 10.1417 6.46542 10.2604 6.36959 10.3562C6.27376 10.4521 6.15501 10.5 6.01334 10.5Z" fill="#7A7B82"/>
            </g>
        </svg>
    )
}

export default attach_money