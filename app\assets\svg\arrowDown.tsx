import React from 'react'

interface ArrowDownProps {
    className?: string
}
const arrowDown:React.FC<ArrowDownProps> = ({ className }) => {
    return (
        <svg className={className} width="20" height="20" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_648_3457" style={{maskType:'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="25">
            <rect x="0.873047" y="0.102539" width="24" height="24" fill="#D9D9D9"/>
            </mask>
            <g mask="url(#mask0_648_3457)">
            <path d="M12.8729 15.0773C12.7395 15.0773 12.6145 15.0565 12.4979 15.0148C12.3812 14.9732 12.2729 14.9023 12.1729 14.8023L7.57285 10.2023C7.38952 10.019 7.29785 9.78568 7.29785 9.50234C7.29785 9.21901 7.38952 8.98568 7.57285 8.80234C7.75618 8.61901 7.98952 8.52734 8.27285 8.52734C8.55618 8.52734 8.78952 8.61901 8.97285 8.80234L12.8729 12.7023L16.7729 8.80234C16.9562 8.61901 17.1895 8.52734 17.4729 8.52734C17.7562 8.52734 17.9895 8.61901 18.1729 8.80234C18.3562 8.98568 18.4479 9.21901 18.4479 9.50234C18.4479 9.78568 18.3562 10.019 18.1729 10.2023L13.5729 14.8023C13.4729 14.9023 13.3645 14.9732 13.2479 15.0148C13.1312 15.0565 13.0062 15.0773 12.8729 15.0773Z" fill="currentColor"/>
            </g>
        </svg>
    )
}

export default arrowDown