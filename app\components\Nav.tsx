import React from 'react'
import NavItem from './NavItem';
import { NavProps } from '@/types';
import { navItems } from '@/common';

const Nav:React.FC<NavProps> = ({
    isExpanded, 
    toggleSidebar
}) => {
    return (
        <nav className={`mt-4 ${isExpanded || toggleSidebar ? 'w-64' : 'w-20'} flex flex-col justify-between h-[80%]`}>
            <div className='space-y-4 mx-4'>
                {navItems.slice(0,6).map((item, index) => (
                    <NavItem
                        key={index}
                        isExpanded={isExpanded}
                        icon={item.icon}
                        label={item.label}
                        toggleSidebar={toggleSidebar}
                        href={item.href} 
                    />
                ))}
            </div>
            <div className='space-y-4 mx-4'>
                {navItems.slice(6,10).map((item, index) => (
                    <NavItem
                        key={index}
                        isExpanded={isExpanded}
                        icon={item.icon}
                        label={item.label}
                        toggleSidebar={toggleSidebar}
                        href={item.href} 
                    />
                ))}
            </div>
        </nav>
    )
}

export default Nav