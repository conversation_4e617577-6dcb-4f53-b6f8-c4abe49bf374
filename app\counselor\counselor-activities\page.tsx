'use client';

import React, { useState } from 'react';
import Pagination from '@/app/components/Pagination';
import { CounselorActivitiesData } from '@/common';
import ActivitiesSection from '@/app/components/ActivitiesSection';
import DashboardLayout from '@/app/components/layout/DashboardLayout';

const itemsPerPage = 6;
const Page = () => {
    const [currentPage, setCurrentPage] = useState(1);

    // Flatten all entries with section date
    const allEntries = CounselorActivitiesData.sections.flatMap((section) =>
        section.entries.map((entry) => ({ ...entry, date: section.date }))
    );

    const totalPages = Math.ceil(allEntries.length / itemsPerPage);

    // Paginate the flattened entries
    const paginatedEntries = allEntries.slice(
        (currentPage - 1) * itemsPerPage,
        currentPage * itemsPerPage
    );

    // Group paginated entries by date
    const groupedEntries = paginatedEntries.reduce<
        Record<string, typeof paginatedEntries>
    >((acc, entry) => {
        const { date } = entry;
        if (!acc[date]) acc[date] = [];
        acc[date].push(entry);
        return acc;
    }, {});

    const groupedSections = Object.entries(groupedEntries).map(
        ([date, entries]) => ({
            date,
            count: entries.length,
            entries,
        })
    );

    const goToPage = (page: number) => {
        setCurrentPage(page);
    };

    return (
        <DashboardLayout>
            <div className="p-5 bg-white rounded-[20px] drop-shadow-5xl">
                {groupedSections.map((section, index) => (
                    <ActivitiesSection key={index} section={section} />
                ))}
            </div>
            <div className='pt-[35px] pb-24'>
                <Pagination
                    currentPage={currentPage}
                    onPageChange={goToPage}
                    totalPages={totalPages}
                />
            </div>

        </DashboardLayout>
    );
};

export default Page;
