import React from 'react'
import { EmailInputProps } from '@/types';
import { Label } from '@/components/ui/label';

const InputFieldWithIcon: React.FC<EmailInputProps> = ({
    id,
    icon,
    label,
    type,
    register,
    className,
    inputClass,
    placeholder = 'Enter your ...'
}) => {
    return (
        <div>
            <Label 
                htmlFor={id} 
                className='font-medium text-sm text-grayFive mb-1.5'>
                {label}
            </Label>
            <div className={`flex ${className} items-center rounded-lg border border-tertiary border-opacity-20 py-2.5 px-3.5 focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60`}>
                <div className='w-5 h-5 mr-2'>{icon}</div>

                <input
                    id={id}
                    type={type}
                    {...(register || {})}
                    placeholder={placeholder}
                    className={`${inputClass} w-full border-none outline-none placeholder:font-normal placeholder:text-base placeholder:text-grayTwo`}
                />
            </div>
        </div>
    )
}

export default InputFieldWithIcon