import React from 'react';
import Link from 'next/link';
import NoDepartment from '@/app/assets/svg/NoDepartment';
import Heading from '../components/Heading';
import PlusIcon from '@/app/assets/svg/plus';
import TreeCardView from '../components/ui/TreeCardView';
import TeamMembersCard from '../components/ui/TeamMembersCard';
import DashboardLayout from '../components/layout/DashboardLayout';

// TypeScript interfaces for company structure
interface Employee {
    name: string;
    designation: string;
    id: number;
    joinedDate: string;
    workType: string;
    workLocation: string;
    workHours: string;
}

interface Team {
    teamName: string;
    name: string;
    designation: string;
    employList: Employee[];
}

interface Department {
    departmentName: string;
    name: string;
    designation: string;
    employList: Employee[];
    defaultOpen: boolean;
    children?: Team[];
}

interface Agency {
    agencyName: string;
    name: string;
    designation: string;
    employList: Employee[];
    defaultOpen: boolean;
    children?: Department[];
}

const page = () => {
    const companyStructure: Agency[] = [
        {
            agencyName: 'Ficc',
            name: '<PERSON><PERSON>',
            designation: 'CEO',
            employList: [],
            defaultOpen: false,
            children: [
                {
                    departmentName: 'Counselor Department',
                    name: '<PERSON>ia Korsgaard',
                    designation: 'Department Head',
                    employList: [],
                    defaultOpen: false,
                    children: [
                        {
                            teamName: 'Career Counselors',
                            name: 'Giana Donin',
                            designation: 'Lead Counselor',
                            employList: [
                                {
                                    name: 'Brandon Ekstrom Bothman',
                                    designation: 'Counselor',
                                    id: 1,
                                    joinedDate: '2025-01-01',
                                    workType: 'Full-time',
                                    workLocation: 'Onsite',
                                    workHours: '40 hours',
                                }
                            ],
                        },
                        {
                            teamName: 'Immigration Specialists',
                            name: 'Jaylon Mango',
                            designation: 'Lead Immigration',
                            employList: [
                                {
                                    name: 'Brandon Ekstrom Bothman',
                                    designation: 'Counselor',
                                    id: 1,
                                    joinedDate: '2025-01-01',
                                    workType: 'Full-time',
                                    workLocation: 'Onsite',
                                    workHours: '40 hours',
                                }
                            ],
                        },
                        {
                            teamName: 'Immigration Specialists',
                            name: 'Jaylon Mango',
                            designation: 'Lead Immigration',
                            employList: [
                                {
                                    name: 'Brandon Ekstrom Bothman',
                                    designation: 'Counselor',
                                    id: 1,
                                    joinedDate: '2025-01-01',
                                    workType: 'Full-time',
                                    workLocation: 'Onsite',
                                    workHours: '40 hours',
                                }
                            ],
                        },
                    ],
                },
                {
                    departmentName: 'Counselor Department',
                    name: 'Livia Korsgaard',
                    designation: 'Department Head',
                    employList: [],
                    defaultOpen: false,
                    // children: [
                    //     {
                    //         teamName: 'Career Counselors',
                    //         name: 'Giana Donin',
                    //         designation: 'Lead Counselor',
                    //         employList: [
                    //             {
                    //                 name: 'Brandon Ekstrom Bothman',
                    //                 designation: 'Counselor',
                    //                 id: 1,
                    //                 joinedDate: '2025-01-01',
                    //                 workType: 'Full-time',
                    //                 workLocation: 'Onsite',
                    //                 workHours: '40 hours',
                    //             }
                    //         ],
                    //     },
                    //     {
                    //         teamName: 'Immigration Specialists',
                    //         name: 'Jaylon Mango',
                    //         designation: 'Lead Immigration',
                    //         employList: [
                    //             {
                    //                 name: 'Brandon Ekstrom Bothman',
                    //                 designation: 'Counselor',
                    //                 id: 1,
                    //                 joinedDate: '2025-01-01',
                    //                 workType: 'Full-time',
                    //                 workLocation: 'Onsite',
                    //                 workHours: '40 hours',
                    //             }
                    //         ],
                    //     },
                    //     {
                    //         teamName: 'Immigration Specialists',
                    //         name: 'Jaylon Mango',
                    //         designation: 'Lead Immigration',
                    //         employList: [
                    //             {
                    //                 name: 'Brandon Ekstrom Bothman',
                    //                 designation: 'Counselor',
                    //                 id: 1,
                    //                 joinedDate: '2025-01-01',
                    //                 workType: 'Full-time',
                    //                 workLocation: 'Onsite',
                    //                 workHours: '40 hours',
                    //             }
                    //         ],
                    //     },
                    // ],
                },
            ],
        },
        {
            agencyName: 'Ficc',
            name: 'Zain Lubin',
            designation: 'CEO',
            employList: [],
            defaultOpen: false,
            // children: [
            //     {
            //         departmentName: 'Counselor Department',
            //         name: 'Livia Korsgaard',
            //         designation: 'Department Head',
            //         employList: [],
            //         defaultOpen: false,
            //         children: [
            //             {
            //                 teamName: 'Career Counselors',
            //                 name: 'Giana Donin',
            //                 designation: 'LeadCounselor',
            //                 employList: [
            //                     {
            //                         name: 'Brandon Ekstrom Bothman',
            //                         designation: 'Counselor',
            //                         id: 1,
            //                         joinedDate: '2025-01-01',
            //                         workType: 'Full-time',
            //                         workLocation: 'Onsite',
            //                         workHours: '40 hours',
            //                     }
            //                 ],
            //             },
            //             {
            //                 teamName: 'Career Counselors',
            //                 name: 'Giana Donin',
            //                 designation: 'LeadCounselor',
            //                 employList: [
            //                     {
            //                         name: 'Brandon Ekstrom Bothman',
            //                         designation: 'Counselor',
            //                         id: 1,
            //                         joinedDate: '2025-01-01',
            //                         workType: 'Full-time',
            //                         workLocation: 'Onsite',
            //                         workHours: '40 hours',
            //                     }
            //                 ],
            //             },
            //         ],
            //     },
            // ],
        },
    ];

    return (
        <DashboardLayout>
            <div className='flex justify-between items-center'>
                <Heading level='h1'>
                    Company Structure
                </Heading>
                <div>
                    <Link className='rounded-[50px] py-2.5 px-5 bg-primaryColor flex items-center gap-2 text-white' href={'/add-department'}>
                        <PlusIcon />
                        Add Department/Team
                    </Link>
                </div>
            </div>
            {companyStructure.length > 0 ? (
                 <div className='flex gap-11 pb-20 mt-5 h-[calc(100vh-250px)]'>
                    <div className='w-[28%] space-y-4'>
                        {companyStructure.map((agency, index) => (
                            <TreeCardView
                                key={index}
                                {...agency}
                            />
                        ))}
                    </div>
                    <div className='w-[72%]'>
                        <div className='space-y-6 overflow-y-auto max-h-[calc(100vh-200px)]'>
                            <Heading level='h2'>
                                Counselor Department
                            </Heading>
                            <Heading className='font-semibold text-lg leading-8 text-graySix mt-0.5' level='h3'>
                                Head of Counselor Team
                            </Heading>
                            <TeamMembersCard />
                            <div>
                                <Heading className='font-semibold text-lg leading-8 text-graySix mt-0.5' level='h3'>
                                    Head of Counselor Team
                                </Heading>
                                <div className='grid grid-cols-4 gap-6 mt-6'>
                                    <TeamMembersCard />
                                    <TeamMembersCard />
                                    <TeamMembersCard />
                                    <TeamMembersCard />
                                    <TeamMembersCard />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            ): (
                <div className='flex justify-center items-center h-[calc(100vh-250px)] bg-white rounded-[20px] mt-5'>
                    <div className='flex flex-col items-center justify-center gap-2'>
                        <NoDepartment />
                        <p className='text-[22px] leading-8 text-secondaryColor font-medium py-5'>No Department/team created</p>
                    </div>
                </div>
            )}
           
        </DashboardLayout>
    )
}

export default page
