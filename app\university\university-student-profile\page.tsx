import React from 'react';
import MarineBiology from '@/app/assets/svg/MarineBiology';
import ArrowOutward from '@/app/assets/svg/ArrowOutward';
import InformationBox from '@/app/components/InformationBox';
import StudentProfile from '@/app/components/StudentProfile';
import MachineLearning from '@/app/assets/svg/MachineLearning';
import VerticalTabLayout from '@/app/components/VerticalTabLayout';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import { 
    socialLinks, 
    Proficiency, 
    AcademicData, 
    personalInfo, 
} from '@/common';

const page = () => {
    const personal = () => {
        return (
            <div className='bg-white rounded-[20px] p-5 mt-5'>
                <h3 className='pb-6 font-semibold text-xl leading-6 text-graySix'>Personal  Information</h3>

                <div className='rounded-lg border p-5 border-tertiary border-opacity-20'>
                    <div className='grid grid-cols-1 sm:grid-cols-2 gap-y-4'>
                        {personalInfo.map((info, index) => (
                            <div key={index} className='flex'>
                                <span className='font-normal text-base leading-5 text-graySix w-1/3'>{info.label}</span>
                                <span className='font-normal text-base leading-5 text-graySix w-2/3'>: 
                                    <span className='ml-3'>{info.value}</span>
                                </span>
                            </div>
                        ))}
                    </div>
                </div>
                <h3 className='font-semibold text-xl leading-6 text-graySix pt-[30px] pb-6'>Social Links</h3>

                <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-6'>
                    {socialLinks.map((social, index) => (
                        <a
                            key={index}
                            href={social.link}
                            target='_blank'
                            rel='noopener noreferrer'
                            className='flex border border-tertiary border-opacity-20 items-center px-5 py-3 rounded-lg transition'
                        >
                            <social.icon />
                            <span className='ml-3.5 flex-1 text-gray-800 font-medium'>{social.username}</span>
                            <ArrowOutward />
                            
                        </a>
                    ))}
                </div>
            </div>
        )
    };

    const education = () => {
        return (
            <div className='bg-white rounded-[20px] p-5 mt-5'>
                <h2 className='font-semibold text-xl text-graySix'>Academic</h2>

                <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                    {AcademicData.map((data, index) => (
                        <InformationBox key={index} data={data} />
                    ))}
                </div>
                <h2 className='mt-6 font-semibold text-xl text-graySix'>Proficiency</h2>
                <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                    {Proficiency.map((data, index) => (
                        <InformationBox key={index} data={data} />
                    ))}
                </div>
                <h2 className='mt-6 font-semibold text-xl text-graySix'>Publication</h2>

                <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                    <div className='mt-6 border rounded-lg py-4 px-5 border-[#1952BB] border-opacity-20'>
                        <div className='flex justify-between items-center'>
                            <div className='flex flex-col gap-2.5'>
                                <div className='flex items-center gap-4'>
                                    <span className='font-medium text-lg text-graySix'>Marine Biology, Climate Change</span>
                                    <span className='font-medium text-xs text-primaryColor bg-primaryOne rounded-[16px] py-[2px] px-2'>Feb 12, 2023</span>
                                </div>
                                <p className='font-normal text-xs leading-5 tracking-[0.25px] text-graySix'>International Journal of Business and Management Studies</p>
                            </div>
                            <div>
                                <MarineBiology />
                            </div>
                        </div>
                    </div>
                    <div className='mt-6 border rounded-lg py-4 px-5 border-[#1952BB] border-opacity-20'>
                        <div className='flex justify-between items-center'>
                            <div className='flex flex-col gap-2.5'>
                                <div className='flex items-center gap-4'>
                                    <span className='font-medium text-lg text-graySix'>Marine Biology, Climate Change</span>
                                    <span className='font-medium text-xs text-primaryColor bg-primaryOne rounded-[16px] py-[2px] px-2'>Feb 12, 2023</span>
                                </div>
                                <p className='font-normal text-xs leading-5 tracking-[0.25px] text-graySix'>International Journal of Business and Management Studies</p>
                            </div>
                            <div>
                                <MachineLearning />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    };

    const scholarships = () => {
        return (
            <div className='bg-white rounded-[20px] p-5 mt-5'>
                <div className='rounded-[10px] border py-4 px-5 border-[#1952BB33] max-w-[848px]'>
                    <div className='flex gap-3 items-center'>
                        <MarineBiology />
                        <span className='font-medium text-lg text-graySix'>Fulbright Foreign Student Program</span>
                        <span className='font-medium text-xs leading-[18px] text-primaryColor'>Valid till Apr 16, 2025</span>
                    </div>
                    <div className='flex gap-3 mt-2.5'>
                        <span className='font-normal text-sm text-grayFive'>Grant Date :</span>
                        <span className='font-semibold text-sm text-grayFive'>Feb 12, 2023</span>
                    </div>
                    <div className='flex gap-3 mt-1.5'>
                        <span className='font-normal text-sm text-grayFive'>Amount :</span>
                        <span className='font-semibold text-sm text-grayFive'>$22,000</span>
                    </div>
                </div>
            </div>
        )
    };

    const billAndPayment = () => {
        return (
            <div className='bg-white rounded-[20px] p-5 mt-5'>
                <h2 className='mt-6 font-semibold text-xl text-graySix'>Scholarships</h2>

                <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                    <div className='mt-6 border rounded-lg py-4 px-5 border-[#1952BB] border-opacity-20'>
                        <div className='flex justify-between items-center'>
                            <div className='flex flex-col gap-2.5'>
                                <div className='flex items-center gap-4'>
                                    <span className='font-medium text-lg text-graySix'>Marine Biology, Climate Change</span>
                                    <span className='font-medium text-xs text-primaryColor bg-primaryOne rounded-[16px] py-[2px] px-2'>Feb 12, 2023</span>
                                </div>
                                <p className='font-normal text-xs leading-5 tracking-[0.25px] text-graySix'>International Journal of Business and Management Studies</p>
                            </div>
                            <div>
                                <MarineBiology />
                            </div>
                        </div>
                    </div>
                    <div className='mt-6 border rounded-lg py-4 px-5 border-[#1952BB] border-opacity-20'>
                        <div className='flex justify-between items-center'>
                            <div className='flex flex-col gap-2.5'>
                                <div className='flex items-center gap-4'>
                                    <span className='font-medium text-lg text-graySix'>Marine Biology, Climate Change</span>
                                    <span className='font-medium text-xs text-primaryColor bg-primaryOne rounded-[16px] py-[2px] px-2'>Feb 12, 2023</span>
                                </div>
                                <p className='font-normal text-xs leading-5 tracking-[0.25px] text-graySix'>International Journal of Business and Management Studies</p>
                            </div>
                            <div>
                                <MachineLearning />
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        )
    };

    const tabContentsStudentProfile = [
        {
            value: 'personal',
            label: 'Personal',
            content: personal
        },
        {
            value: 'education',
            label: 'Education',
            content: education
        },
        {
            value: 'scholarships',
            label: 'Scholarships',
            content: scholarships
        },
        {
            value: 'Bill & Payment',
            label: 'Bill & Payment',
            content: billAndPayment
        }
    ];

    const tabContents = tabContentsStudentProfile.map((tab) => ({
        value: tab.value,
        children: <tab.content />
    }));

    return (
        <DashboardLayout>
            <StudentProfile />

            <div className='mt-5'>
                <VerticalTabLayout 
                    tabMenu={tabContentsStudentProfile}
                    tabContents={tabContents}
                    defaultValue='personal'
                    tabListClassName='!p-0 max-w-[1193px] md:!justify-start'
                    tabContentClassName = 'pb-[96px]'
                    tabTriggerClassName=''
                />
            </div>
        </DashboardLayout>
    )
}

export default page