'use client'
 
import { courses } from '@/common';
import React, { useState } from 'react';
import search from '@/app/assets/svg/search';
import canada from '@/app/assets/svg/canada';
import Heading from '@/app/components/Heading';
import ThreeDots from '@/app/assets/svg/ThreeDots';
import Pagination from '@/app/components/Pagination';
import InputWithIcon from '@/app/components/InputWithIcon';
import DropDownButton from '@/app/components/DropDownButton';
import DatePickerWithRange from '@/app/components/DatepickerWithRange';
import DashboardLayout from '@/app/components/layout/DashboardLayout';

const page = () => {
    const itemsPerPage = 2;
    const [currentPage, setCurrentPage] = useState(1);
    const totalPages = Math.ceil(courses.length / itemsPerPage);

    // const currentCources = courses.slice(
    //     (currentPage - 1) * itemsPerPage,
    //     currentPage * itemsPerPage
    // );

    const goToPage = (page: number) => {
        setCurrentPage(page);
    };
    const tableHeadData = [
        'AID',
        'agency',
        'All Applications',
        'Paid Applications',
        'Unpaid Applications',
        'Key Account Manager',
        'Email',
        'Country',
        'Action'
    ];

    const paidUnpaidApplications = [
        {
            aid: 262,
            agency: 'FICC',
            allApplications: '00',
            paidApplications: '00',
            unpaidApplications: '00',
            keyAccountManager: 'Brandon Philips',
            email: '<EMAIL>',
            country: canada
        }
    ];
    return (
        <DashboardLayout>
            <div className='flex justify-between items-center'>
                <Heading level='h1'> 
                    Paid and Unpaid Applications
                </Heading>
                <DatePickerWithRange />
            </div>
            <div className='flex justify-between items-center my-5'>
                <InputWithIcon 
                    icon={search} 
                    placeholder='search by id, name, mobile, email' 
                    className='font-normal text-xs leading-none text-grayTwo w-[300px] rounded-[50px] py-2.5  border-[0.5px] bg-white border-grayTwo' 
                />
                <DropDownButton />
            </div>
            <div className='overflow-x-auto'>
                <table className='bg-white min-w-full rounded-[20px] text-sm text-left'>
                    <thead>
                        <tr>
                            {tableHeadData.map((thead, index) => (
                                <th 
                                    key={index}
                                    className={`${ index === 0 ? 'flex items-center gap-0.5 cursor-pointer': ''} py-3.5 px-6 font-bold text-xs tracking-[0.4px] text-grayFive`}
                                >
                                    {thead}
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody className='border-t border-grayOne divide-y divide-grayOne'>
                        {paidUnpaidApplications.map((application, index) => (
                            <tr key={index}>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{application.aid}</td>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{application.agency}</td>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{application.allApplications} </td>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{application.paidApplications}</td>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{application.unpaidApplications}</td>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{application.keyAccountManager}</td>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{application.email}</td>
                                <td className='text-xs leading-5 text-graySix px-10 py-3.5'>
                                    {<application.country />}
                                </td>
                                <td className='text-xs leading-5 text-graySix px-9 py-3.5'>
                                    <ThreeDots />
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <div className='pb-24 pt-12'>
                <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={goToPage}
                />
            </div>
        </DashboardLayout>
    )
}

export default page