import React from 'react';

interface MinusProps {
    className?: string
}

const Minus:React.FC<MinusProps> = ({className}) => {
    return (
        <svg
            className={className}
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="mask0_1029_20653"
                style={{maskType:'alpha'}}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="24"
                height="24"
            >
                <rect width="24" height="24" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_1029_20653)">
                <path
                    d="M6 12.9999C5.71667 12.9999 5.47917 12.904 5.2875 12.7124C5.09583 12.5207 5 12.2832 5 11.9999C5 11.7165 5.09583 11.479 5.2875 11.2874C5.47917 11.0957 5.71667 10.9999 6 10.9999H18C18.2833 10.9999 18.5208 11.0957 18.7125 11.2874C18.9042 11.479 19 11.7165 19 11.9999C19 12.2832 18.9042 12.5207 18.7125 12.7124C18.5208 12.904 18.2833 12.9999 18 12.9999H6Z"
                    fill="#36373B"
                />
            </g>
        </svg>
    );
};

export default Minus;
