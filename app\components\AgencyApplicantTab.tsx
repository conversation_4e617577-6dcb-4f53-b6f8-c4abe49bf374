import React from 'react'
import { DocumentItem } from './DocumentItem'

const AgencyApplicantTab = () => {
    return (
        <div className=" bg-white w-full p-4 rounded-xl">
            <div className="space-y-4">
                <DocumentItem
                    title="CAL Issued"
                    issued={true}
                    downloadable={true}
                    fileName="CAL.pdf"
                />
                <DocumentItem
                    title="I20 Issued"
                    issued={true}
                    downloadable={true}
                    fileName="I20.pdf"
                />
                <DocumentItem
                    title="Appointment Confirmation"
                    issued={true}
                    downloadable={true}
                    fileName="Appointment.pdf"
                />
            </div>
            
            <div className="border-t border-secondaryColor my-5"></div>
            
            <div>
                <h2 className="text-xl font-medium text-gray-800 mb-4">Documents</h2>
                <div className="space-y-4">
                    <DocumentItem
                        title="DS 160"
                        uploadable={true}
                    />
                    <DocumentItem
                        title="SEVIS Fee"
                        uploadable={true}
                    />
                </div>
            </div>
        </div>
    )
}

export default AgencyApplicantTab
