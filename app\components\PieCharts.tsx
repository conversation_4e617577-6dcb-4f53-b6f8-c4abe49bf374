// 'use client';
// import { Pie, PieChart } from 'recharts';
// import {
//     Card,
//     CardContent,
//     CardFooter,
//     CardHeader,
//     CardTitle,
// } from '@/components/ui/card';
// import {
//     ChartConfig,
//     ChartContainer,
//     ChartTooltip,
//     ChartTooltipContent,
// } from '@/components/ui/chart';
// interface ChartDataItem {
//     status: string;
//     value: number;
//     fill: string;
// }

// type TimePeriod = 'today' | 'thisMonth' | 'thisYear';

// interface CustomLabels {
//     [key: string]: string;
// }
// interface ChartData {
//     [key: string]: ChartDataItem[];
// }

// interface PieChartProps {
//     title?: string;
//     data: ChartDataItem[];
//     customLabels?: CustomLabels;
//     showLegendLayout?: 'bottom' | 'right';
//     className?: string;
//     showLabelsOnPie?: boolean;
//     tooltipConfig?: {
//         hideLabel?: boolean;
//         formatter?: (value: number, name: string, props: any) => React.ReactNode;
//     };
//     ChartClassName?: string;
// }

// export function PieCharts({
//     title = 'Agency Status',
//     data,
//     customLabels = {},
//     showLegendLayout = 'bottom',
//     className = '',
//     showLabelsOnPie = true,
//     tooltipConfig = { hideLabel: false },
//     ChartClassName = 'max-h-[525px]'
// }: PieChartProps) {

//     const chartData = data || [];

//     const generateChartConfig = (data: ChartDataItem[]) => {
//         const config: Record<string, { label: string; color: string }> = {};

//         data.forEach((item) => {
//             if (!item || !item.status) return;
            
//             const key = item.status.toLowerCase().replace(/\s+/g, '');
//             config[key] = {
//                 label: customLabels[item.status] || item.status,
//                 color: item.fill,
//             };
//         });

//         return config as ChartConfig;
//     };

//     const chartConfig = generateChartConfig(chartData);

//     const generateLegendItems = (data: ChartDataItem[]) => {
//         return data.map((item, index) => (
//             <div key={index} className="flex items-center gap-3">
//                 <div
//                     className="w-3.5 h-3.5 rounded-[20px]"
//                     style={{ backgroundColor: item.fill }}
//                 ></div>
//                 <span className="md:text-base text-xs md:leading-5 leading-3 text-grayFour font-medium">
//                     {customLabels[item.status] || item.status}
//                 </span>
//             </div>
//         ));
//     };

//     const RADIAN = Math.PI / 180;
//     const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index }: { cx: number; cy: number; midAngle: number; innerRadius: number; outerRadius: number; percent: number; index: number }) => {
//         const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
//         const x = cx + radius * Math.cos(-midAngle * RADIAN);
//         const y = cy + radius * Math.sin(-midAngle * RADIAN);
//         return (
//             <text x={x} y={y} fill="white" textAnchor={x > cx ? 'start' : 'end'} dominantBaseline="central">
//                 {showLabelsOnPie ? ` ${chartData[index].value}` : ''}
//             </text>
//         );
//     };

//     return (
//         <Card
//             className={`drop-shadow-none shadow-none border-none ${className}`}
//         >
//             <CardHeader className="flex flex-row items-center justify-between">
//                 <CardTitle className="flex md:flex-row flex-col gap-4 justify-between">
//                     <div className="flex items-center gap-2.5 md:text-xl text-base md:leading-[29px] leading-5 text-grayFive font-bold">
//                         <h2>{title}</h2>
//                     </div>
//                 </CardTitle>
//             </CardHeader>
//             <CardContent className="flex-1 pb-0 h-full flex">
//                 <ChartContainer
//                     config={chartConfig}
//                     className={`mx-auto aspect-square w-1/2 ${ChartClassName}`}
//                 >
//                     <PieChart>
//                         <ChartTooltip
//                             cursor={false}
//                             content={<ChartTooltipContent formatter={(value) => `${value.toLocaleString()} %`} />}
//                             labelStyle={{ fontSize: '50px', color: 'red' }}
//                         />
//                         <Pie
//                             data={chartData}
//                             stroke="white"
//                             dataKey="value"
//                             nameKey="status"
//                             labelLine={false}
//                             label={renderCustomizedLabel}
//                             startAngle={450}
//                             endAngle={90}

//                         />
//                     </PieChart>
//                 </ChartContainer>
//                 {showLegendLayout == 'right' && (
//                     <div className='h-[320px] w-1/2 flex justify-center items-center'>
//                         <div className='flex flex-col items-start text-grayFive gap-3'>
//                             {chartData.map((item, index) => (
//                                 <div key={index} className='flex items-center justify-center gap-4'>
//                                     <span 
//                                         className='w-3 h-3 rounded-[20px]' 
//                                         style={{ backgroundColor: item.fill }}
//                                     ></span>
//                                     <span>{item.status}</span>
//                                 </div>
//                             ))}
//                         </div>
//                     </div>
//                 )}
//             </CardContent>
//             {showLegendLayout == 'bottom' && (
//                 <CardFooter className="flex justify-center">
//                     <div className="flex flex-wrap items-center md:gap-7 gap-2.5">
//                         {generateLegendItems(chartData)}
//                     </div>
//                 </CardFooter>
//             )}
//         </Card>
//     );
// }

'use client';
import { Pie, PieChart } from 'recharts';
import {
    Card,
    CardContent,
    CardFooter,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import {
    ChartConfig,
    ChartContainer,
    ChartTooltip,
    ChartTooltipContent,
} from '@/components/ui/chart';
interface ChartDataItem {
    [key: string]: string | number;
    fill: string;
}

interface CustomLabels {
    [key: string]: string;
}

interface PieChartProps {
    title?: string;
    data: ChartDataItem[];
    labelKey?: string;
    valueKey?: string; 
    customLabels?: CustomLabels;
    showLegendLayout?: 'bottom' | 'right';
    className?: string;
    showLabelsOnPie?: boolean;
    tooltipConfig?: {
        hideLabel?: boolean;
        formatter?: (value: number, name: string, props: any) => React.ReactNode;
    };
    ChartClassName?: string;
}

export function PieCharts({
    title = 'Chart',
    data,
    labelKey = 'status', 
    valueKey = 'value',
    customLabels = {},
    showLegendLayout = 'bottom',
    className = '',
    showLabelsOnPie = true,
    tooltipConfig = { hideLabel: false },
    ChartClassName = 'max-h-[525px]'
}: PieChartProps) {

    const chartData = data || [];

    const generateChartConfig = (data: ChartDataItem[]) => {
        const config: Record<string, { label: string; color: string }> = {};

        data.forEach((item) => {
            if (!item || !item[labelKey]) return;
            
            const label = String(item[labelKey]);
            const key = label.toLowerCase().replace(/\s+/g, '');
            config[key] = {
                label: customLabels[label] || label,
                color: item.fill,
            };
        });

        return config as ChartConfig;
    };

    const chartConfig = generateChartConfig(chartData);

    const generateLegendItems = (data: ChartDataItem[]) => {
        return data.map((item, index) => {
            const label = String(item[labelKey]);
            return (
                <div key={index} className="flex items-center gap-3">
                    <div
                        className="w-3.5 h-3.5 rounded-[20px]"
                        style={{ backgroundColor: item.fill }}
                    ></div>
                    <span className="md:text-base text-xs md:leading-5 leading-3 text-grayFour font-medium">
                        {customLabels[label] || label}
                    </span>
                </div>
            );
        });
    };

    const RADIAN = Math.PI / 180;
    const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index }: { cx: number; cy: number; midAngle: number; innerRadius: number; outerRadius: number; percent: number; index: number }) => {
        const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
        const x = cx + radius * Math.cos(-midAngle * RADIAN);
        const y = cy + radius * Math.sin(-midAngle * RADIAN);
        return (
            <text x={x} y={y} fill="white" textAnchor={x > cx ? 'start' : 'end'} dominantBaseline="central">
                {showLabelsOnPie ? ` ${chartData[index][valueKey]}` : ''}
            </text>
        );
    };

    return (
        <Card
            className={`drop-shadow-none shadow-none border-none ${className}`}
        >
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="flex md:flex-row flex-col gap-4 justify-between">
                    <div className="flex items-center gap-2.5 md:text-xl text-base md:leading-[29px] leading-5 text-grayFive font-bold">
                        <h2>{title}</h2>
                    </div>
                </CardTitle>
            </CardHeader>
            <CardContent className="flex-1 pb-0 h-full flex">
                <ChartContainer
                    config={chartConfig}
                    className={`mx-auto aspect-square w-1/2 ${ChartClassName}`}
                >
                    <PieChart>
                        <ChartTooltip
                            cursor={false}
                            content={<ChartTooltipContent formatter={(value) => `${value.toLocaleString()} %`} />}
                            labelStyle={{ fontSize: '50px', color: 'red' }}
                        />
                        <Pie
                            data={chartData}
                            stroke="white"
                            dataKey={valueKey}
                            nameKey={labelKey}
                            labelLine={false}
                            label={renderCustomizedLabel}
                            startAngle={450}
                            endAngle={90}
                        />
                    </PieChart>
                </ChartContainer>
                {showLegendLayout == 'right' && (
                    <div className='h-[320px] w-1/2 flex justify-center items-center'>
                        <div className='flex flex-col items-start text-grayFive gap-3'>
                            {chartData.map((item, index) => {
                                const label = String(item[labelKey]);
                                return (
                                    <div key={index} className='flex items-center justify-center gap-4'>
                                        <span 
                                            className='w-3 h-3 rounded-[20px]' 
                                            style={{ backgroundColor: item.fill }}
                                        ></span>
                                        <span>{customLabels[label] || label}</span>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                )}
            </CardContent>
            {showLegendLayout == 'bottom' && (
                <CardFooter className="flex justify-center">
                    <div className="flex flex-wrap items-center md:gap-7 gap-2.5">
                        {generateLegendItems(chartData)}
                    </div>
                </CardFooter>
            )}
        </Card>
    );
}
