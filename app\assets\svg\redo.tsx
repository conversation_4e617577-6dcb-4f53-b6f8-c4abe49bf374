import React from 'react';
import { xIconProps } from '@/types';


const redo: React.FC<xIconProps> = ({ className }) => {
    return (
        <svg
            className={className}
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="mask0_2560_27526"
                style={{maskType:'alpha'}}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="18"
                height="18"
            >
                <rect width="18" height="18" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_2560_27526)">
                <path
                    d="M12.15 7.49961H7.425C6.6375 7.49961 5.95313 7.74961 5.37188 8.24961C4.79063 8.74961 4.5 9.37461 4.5 10.1246C4.5 10.8746 4.79063 11.4996 5.37188 11.9996C5.95313 12.4996 6.6375 12.7496 7.425 12.7496H12C12.2125 12.7496 12.3906 12.8215 12.5344 12.9652C12.6781 13.109 12.75 13.2871 12.75 13.4996C12.75 13.7121 12.6781 13.8902 12.5344 14.034C12.3906 14.1777 12.2125 14.2496 12 14.2496H7.425C6.2125 14.2496 5.17188 13.8559 4.30313 13.0684C3.43438 12.2809 3 11.2996 3 10.1246C3 8.94961 3.43438 7.96836 4.30313 7.18086C5.17188 6.39336 6.2125 5.99961 7.425 5.99961H12.15L10.725 4.57461C10.5875 4.43711 10.5188 4.26211 10.5188 4.04961C10.5188 3.83711 10.5875 3.66211 10.725 3.52461C10.8625 3.38711 11.0375 3.31836 11.25 3.31836C11.4625 3.31836 11.6375 3.38711 11.775 3.52461L14.475 6.22461C14.55 6.29961 14.6031 6.38086 14.6344 6.46836C14.6656 6.55586 14.6812 6.64961 14.6812 6.74961C14.6812 6.84961 14.6656 6.94336 14.6344 7.03086C14.6031 7.11836 14.55 7.19961 14.475 7.27461L11.775 9.97461C11.6375 10.1121 11.4625 10.1809 11.25 10.1809C11.0375 10.1809 10.8625 10.1121 10.725 9.97461C10.5875 9.83711 10.5188 9.66211 10.5188 9.44961C10.5188 9.23711 10.5875 9.06211 10.725 8.92461L12.15 7.49961Z"
                    fill="currentColor"
                />
            </g>
        </svg>
    );
};

export default redo;
