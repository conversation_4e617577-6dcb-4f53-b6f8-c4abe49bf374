'use client';

import { useState } from 'react';
import {
    <PERSON>, 
    <PERSON><PERSON><PERSON>, 
    CartesianGrid, 
    Label<PERSON>ist, 
    Legend, 
    XAxis, 
    YAxis 
} from 'recharts';
import { 
    Card, 
    CardContent, 
    CardHeader, 
    CardTitle 
} from '@/components/ui/card';
import {
    ChartContainer,
    ChartTooltip,
    ChartTooltipContent,
} from '@/components/ui/chart';
import DateRangeSelect from './DateRangeSelect';
import Up from '../assets/svg/Up';

interface ChartConfig {
    [key: string]: { label: string; color: string };
}

interface MultipleBarChartProps {
    data: Record<string, any[]>;  // Different datasets based on time ranges
    chartConfig: ChartConfig;     // Dynamic chart configuration
    title: string;
    barSize?: number;                // Title for the chart (e.g., "Students", "Visa Success")
    icon?: React.ReactNode;       // Optional icon
    deltaValue?: number;
    xAxisAngle?: number;
    xAxisHeight?: number;
    chartHeightClass?: string;
    textAnchor?: 'end' | 'middle' | 'start' | 'inherit'
}

const MultipleBarChart = ({ 
    data,
    chartConfig, 
    title, 
    icon,
    barSize = 15,
    xAxisAngle,
    xAxisHeight = 20,
    deltaValue = 1324,
    chartHeightClass = 'max-h-[400px]',
    textAnchor = "middle"

 }: MultipleBarChartProps) => {
    const [selectedRange, setSelectedRange] = useState<keyof typeof data>('This Year');
    const chartData = data[selectedRange] || [];

    // Determine the X-Axis field dynamically
    const xAxisKey = chartData.length > 0 
        ? Object.keys(chartData[0]).find(key => ['month', 'day', 'hour', 'agency','university', 'country', 'applicant'].includes(key)) || 'month'
        : 'month';

    // Get all keys excluding the xAxisKey (these are the bars)
    const barKeys = Object.keys(chartConfig);

    const options = Object.keys(data);  // ["This Year", "This Month", "This Week", "Today"]
    
    return (
        <Card className="drop-shadow-none shadow-none border-none">
            <CardHeader className="space-y-[26px]">
                <CardTitle className="flex md:flex-row flex-col gap-4 justify-between w-full">
                    <div className="flex items-center gap-2.5 md:text-xl text-base md:leading-[29px] leading-5 text-grayFive font-bold">
                        {icon}
                        <h2>{title}</h2>
                    </div>
                    <div className=" flex items-center gap-[60px] text-primaryColor md:text-sm text-[10px] md:leading-[17px] leading-3 font-semibold">
                        {/* legend should be here */}
                        <div className="flex flex-wrap gap-4">
                            {barKeys.map((key) => (
                                <div key={key} className="flex items-center gap-2">
                                    <div 
                                        className="w-3 h-3 rounded-full" 
                                        style={{ backgroundColor: chartConfig[key]?.color }}
                                    />
                                    <span className="text-sm text-gray-700">
                                        {chartConfig[key]?.label}
                                    </span>
                                </div>
                            ))}
                        </div>
                        <div className='w-fit'>
                            <DateRangeSelect
                                options={options}
                                selectedValue={selectedRange}
                                onChange={(value) => setSelectedRange(value as keyof typeof data)}
                                placeholder="Select Time Range"
                            />
                        </div>
                    </div>
                </CardTitle>
                {deltaValue > 0 && 
                    <div className=" flex items-center gap-[30px]">
                        <span className="font-bold md:text-[32px] text-2xl md:leading-[38px] leading-[29px] text-primaryColor">
                            {deltaValue}
                        </span>
                        <span className="font-medium md:text-base text-sm md:leading-[19px] leading-[17px] text-primaryColor flex items-center gap-1">
                            <Up />
                            23% than last year
                        </span>
                    </div>
                }
            </CardHeader>
            <CardContent className="md:px-5 px-0 md:pl-0">
                <ChartContainer className={`${chartHeightClass} w-full`} config={chartConfig}>
                    <BarChart accessibilityLayer data={chartData}>
                        <CartesianGrid vertical={false} />
                        <XAxis
                            dataKey={xAxisKey}
                            tickLine={false}
                            tickMargin={5}
                            axisLine={false}
                            angle={xAxisAngle}
                            height={xAxisHeight}
                            textAnchor={textAnchor}
                            // tickFormatter={(value) => value.slice(0, 3)}
                        />
                        <YAxis axisLine={false} tickLine={false} tickMargin={20} />
                        <ChartTooltip cursor={false} content={<ChartTooltipContent indicator="dot" />} />
                        {/* <Legend 
                            verticalAlign="bottom" 
                            iconType='rect' 
                            height={40} 
                        /> */}
                        {/* Dynamically render bars based on `chartConfig` */}
                        {barKeys.map((key) => (
                            <Bar 
                                key={key} 
                                dataKey={key} 
                                fill={chartConfig[key]?.color} 
                                radius={15} 
                                barSize={barSize}
                            >
                                <LabelList  
                                    position='top' 
                                    angle={0} 
                                    fill='black'

                                />
                            </Bar>
                        ))}
                    </BarChart>
                </ChartContainer>
            </CardContent>
        </Card>
    );
};

export default MultipleBarChart;
