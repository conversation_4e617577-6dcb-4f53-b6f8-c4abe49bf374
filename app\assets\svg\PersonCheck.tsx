import React from 'react'

const PersonCheck = () => {
    return (
        <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <rect x="0.800781" width="24" height="24" rx="12" fill="#F4F7FE" />
            <path d="M16.0975 10.3508L18.1538 8.27995C18.2704 8.16328 18.409 8.10495 18.5694 8.10495C18.7298 8.10495 18.8684 8.16328 18.985 8.27995C19.1017 8.39661 19.16 8.53516 19.16 8.69557C19.16 8.85599 19.1017 8.99453 18.985 9.1112L16.5059 11.5904C16.3892 11.707 16.2531 11.7654 16.0975 11.7654C15.942 11.7654 15.8059 11.707 15.6892 11.5904L14.4496 10.3508C14.3329 10.2341 14.2746 10.0956 14.2746 9.93516C14.2746 9.77474 14.3329 9.6362 14.4496 9.51953C14.5663 9.40286 14.7024 9.34453 14.8579 9.34453C15.0135 9.34453 15.1496 9.40286 15.2663 9.51953L16.0975 10.3508ZM11.11 11.9987C10.4684 11.9987 9.91905 11.7702 9.46211 11.3133C9.00517 10.8563 8.77669 10.307 8.77669 9.66536C8.77669 9.0237 9.00517 8.47439 9.46211 8.01745C9.91905 7.5605 10.4684 7.33203 11.11 7.33203C11.7517 7.33203 12.301 7.5605 12.7579 8.01745C13.2149 8.47439 13.4434 9.0237 13.4434 9.66536C13.4434 10.307 13.2149 10.8563 12.7579 11.3133C12.301 11.7702 11.7517 11.9987 11.11 11.9987ZM6.44336 15.4987V15.032C6.44336 14.7015 6.52843 14.3977 6.69857 14.1206C6.86871 13.8435 7.09475 13.632 7.37669 13.4862C7.97947 13.1848 8.59197 12.9588 9.21419 12.8081C9.83642 12.6574 10.4684 12.582 11.11 12.582C11.7517 12.582 12.3836 12.6574 13.0059 12.8081C13.6281 12.9588 14.2406 13.1848 14.8434 13.4862C15.1253 13.632 15.3513 13.8435 15.5215 14.1206C15.6916 14.3977 15.7767 14.7015 15.7767 15.032V15.4987C15.7767 15.8195 15.6625 16.0942 15.434 16.3227C15.2055 16.5511 14.9309 16.6654 14.61 16.6654H7.61003C7.28919 16.6654 7.01454 16.5511 6.78607 16.3227C6.5576 16.0942 6.44336 15.8195 6.44336 15.4987ZM7.61003 15.4987H14.61V15.032C14.61 14.9251 14.5833 14.8279 14.5298 14.7404C14.4763 14.6529 14.4059 14.5848 14.3184 14.5362C13.7934 14.2737 13.2635 14.0768 12.7288 13.9456C12.1941 13.8143 11.6545 13.7487 11.11 13.7487C10.5656 13.7487 10.026 13.8143 9.49128 13.9456C8.95655 14.0768 8.42669 14.2737 7.90169 14.5362C7.81419 14.5848 7.74371 14.6529 7.69023 14.7404C7.63676 14.8279 7.61003 14.9251 7.61003 15.032V15.4987ZM11.11 10.832C11.4309 10.832 11.7055 10.7178 11.934 10.4893C12.1625 10.2609 12.2767 9.9862 12.2767 9.66536C12.2767 9.34453 12.1625 9.06988 11.934 8.84141C11.7055 8.61293 11.4309 8.4987 11.11 8.4987C10.7892 8.4987 10.5145 8.61293 10.2861 8.84141C10.0576 9.06988 9.94336 9.34453 9.94336 9.66536C9.94336 9.9862 10.0576 10.2609 10.2861 10.4893C10.5145 10.7178 10.7892 10.832 11.11 10.832Z" fill="#36373B" />
        </svg>
    )
}

export default PersonCheck