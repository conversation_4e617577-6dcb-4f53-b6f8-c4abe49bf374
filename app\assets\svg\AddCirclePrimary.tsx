import React from 'react'

const AddCirclePrimary = () => {
    return (
        <svg width="25" height="24" viewBox="0 0 25 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_10190_45479)">
                <mask id="mask0_10190_45479" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="24">
                    <rect x="0.201172" width="24" height="24" fill="#D9D9D9" />
                </mask>
                <g mask="url(#mask0_10190_45479)">
                    <path d="M11.2031 13V16C11.2031 16.2833 11.299 16.5208 11.4906 16.7125C11.6823 16.9042 11.9198 17 12.2031 17C12.4865 17 12.724 16.9042 12.9156 16.7125C13.1073 16.5208 13.2031 16.2833 13.2031 16V13H16.2031C16.4865 13 16.724 12.9042 16.9156 12.7125C17.1073 12.5208 17.2031 12.2833 17.2031 12C17.2031 11.7167 17.1073 11.4792 16.9156 11.2875C16.724 11.0958 16.4865 11 16.2031 11H13.2031V8C13.2031 7.71667 13.1073 7.47917 12.9156 7.2875C12.724 7.09583 12.4865 7 12.2031 7C11.9198 7 11.6823 7.09583 11.4906 7.2875C11.299 7.47917 11.2031 7.71667 11.2031 8V11H8.20313C7.91979 11 7.68229 11.0958 7.49063 11.2875C7.29896 11.4792 7.20313 11.7167 7.20313 12C7.20313 12.2833 7.29896 12.5208 7.49063 12.7125C7.68229 12.9042 7.91979 13 8.20313 13H11.2031ZM12.2031 22C10.8198 22 9.51979 21.7375 8.30313 21.2125C7.08646 20.6875 6.02813 19.975 5.12813 19.075C4.22813 18.175 3.51563 17.1167 2.99063 15.9C2.46563 14.6833 2.20312 13.3833 2.20312 12C2.20312 10.6167 2.46563 9.31667 2.99063 8.1C3.51563 6.88333 4.22813 5.825 5.12813 4.925C6.02813 4.025 7.08646 3.3125 8.30313 2.7875C9.51979 2.2625 10.8198 2 12.2031 2C13.5865 2 14.8865 2.2625 16.1031 2.7875C17.3198 3.3125 18.3781 4.025 19.2781 4.925C20.1781 5.825 20.8906 6.88333 21.4156 8.1C21.9406 9.31667 22.2031 10.6167 22.2031 12C22.2031 13.3833 21.9406 14.6833 21.4156 15.9C20.8906 17.1167 20.1781 18.175 19.2781 19.075C18.3781 19.975 17.3198 20.6875 16.1031 21.2125C14.8865 21.7375 13.5865 22 12.2031 22ZM12.2031 20C14.4365 20 16.3281 19.225 17.8781 17.675C19.4281 16.125 20.2031 14.2333 20.2031 12C20.2031 9.76667 19.4281 7.875 17.8781 6.325C16.3281 4.775 14.4365 4 12.2031 4C9.96979 4 8.07813 4.775 6.52813 6.325C4.97813 7.875 4.20313 9.76667 4.20313 12C4.20313 14.2333 4.97813 16.125 6.52813 17.675C8.07813 19.225 9.96979 20 12.2031 20Z" fill="#1E62E0" />
                </g>
            </g>
            <defs>
                <clipPath id="clip0_10190_45479">
                    <rect width="24" height="24" fill="white" transform="translate(0.201172)" />
                </clipPath>
            </defs>
        </svg>

    )
}

export default AddCirclePrimary