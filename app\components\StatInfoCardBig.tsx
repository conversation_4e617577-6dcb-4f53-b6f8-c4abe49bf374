import Image from 'next/image';
import { StatInfoCardBigProps } from '@/types';

const StatInfoCardBig: React.FC<StatInfoCardBigProps> = ({
    number,
    imageSrc,
    description,
}) => {
    return (
        <div className='flex flex-col p-[30px] gap-[26px] md:items-start items-center '>
            <Image src={imageSrc} alt='add icon' height={58} width={58} />
            <div className='flex flex-col gap-5'>
                <span className='text-primaryColor md:text-start text-center font-bold text-5xl leading-[58px]'>
                    {number}+
                </span>
                <span className='text-primaryColor font-medium text-xl leading-[24px]'>
                    {description}
                </span>
            </div>
        </div>
    );
};

export default StatInfoCardBig;
