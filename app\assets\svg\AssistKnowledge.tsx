import React from 'react'

const AssistKnowledge = () => {
    return (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_1642_10225" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="20">
                <rect width="20" height="20" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_1642_10225)">
                <path d="M5 18.3333C4.30556 18.3333 3.71528 18.0902 3.22917 17.6041C2.74306 17.118 2.5 16.5277 2.5 15.8333V4.16663C2.5 3.47218 2.74306 2.8819 3.22917 2.39579C3.71528 1.90968 4.30556 1.66663 5 1.66663H12.5C12.9583 1.66663 13.3507 1.82982 13.6771 2.15621C14.0035 2.4826 14.1667 2.87496 14.1667 3.33329V13.3333C14.1667 13.7916 14.0035 14.184 13.6771 14.5104C13.3507 14.8368 12.9583 15 12.5 15H5C4.76389 15 4.56597 15.0798 4.40625 15.2395C4.24653 15.3993 4.16667 15.5972 4.16667 15.8333C4.16667 16.0694 4.24653 16.2673 4.40625 16.427C4.56597 16.5868 4.76389 16.6666 5 16.6666H15.8333V4.16663C15.8333 3.93051 15.9132 3.7326 16.0729 3.57288C16.2326 3.41315 16.4306 3.33329 16.6667 3.33329C16.9028 3.33329 17.1007 3.41315 17.2604 3.57288C17.4201 3.7326 17.5 3.93051 17.5 4.16663V16.6666C17.5 17.125 17.3368 17.5173 17.0104 17.8437C16.684 18.1701 16.2917 18.3333 15.8333 18.3333H5ZM7.5 13.3333H12.5V3.33329H7.5V13.3333ZM5.83333 13.3333V3.33329H5C4.76389 3.33329 4.56597 3.41315 4.40625 3.57288C4.24653 3.7326 4.16667 3.93051 4.16667 4.16663V13.4791C4.30556 13.4375 4.44097 13.4027 4.57292 13.375C4.70486 13.3472 4.84722 13.3333 5 13.3333H5.83333Z" fill="#1952BB" />
            </g>
        </svg>
    )
}

export default AssistKnowledge