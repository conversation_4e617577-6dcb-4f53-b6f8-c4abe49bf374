import Heading from '../Heading';
import { courses } from '@/common';
import Pagination from '../Pagination';
import React, { useState } from 'react';
import Menu from '@/app/assets/svg/menu';
import Grid from '@/app/assets/svg/grid';
import { TableLayoutProps } from '@/types';
import Check from '@/app/assets/svg/check';
import DropDownButton from '../DropDownButton';
import { Button } from '@/components/ui/button';
import AllStudentStatus from '../AllStudentStatus';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow
} from '@/components/ui/table';

const TableLayout: React.FC<TableLayoutProps> = ({
    tableHead,
    tableData,
    tableHeading,
}) => {
    const [layout, setLayout] = useState(true);
    const itemsPerPage = 2;
    const [currentPage, setCurrentPage] = useState(1);
    const totalPages = Math.ceil(courses.length / itemsPerPage);

    const currentCources = courses.slice(
        (currentPage - 1) * itemsPerPage,
        currentPage * itemsPerPage
    );

    const goToPage = (page: number) => {
        setCurrentPage(page);
    };

    const tableCellClass = 'py-3.5 px-6';
    return (
        <>
        <div className='flex flex-col md:flex-row justify-between pt-10 pb-5'>
            <div className='flex gap-7'>
                <Heading level='h2'>
                    { tableHeading }
                </Heading>
            </div>
            <div className='flex items-center gap-5 md:mt-0 mt-5'>
                <div className='flex h-8 border-[0.5px] border-grayFive rounded-[20px]'>
                    <Button 
                        className={`py-0 w-[60px] border-r rounded-l-[50px] border-grayFive ${layout===true ? 'bg-white' : ''} `}
                        onClick={() => setLayout(true)}
                    >
                        {layout === true && (
                            <Check />
                        )}
                        <Menu />
                    </Button>
                    <Button 
                        className={`w-[60px] py-0 rounded-r-[50px] ${layout===false ? 'bg-white' : ''}`} 
                        onClick={() => setLayout(false)}
                    >
                        {layout === false && (
                            <Check />
                        )}
                        <Grid />
                    </Button>
                </div>
                <DropDownButton />
            </div>
        </div>
        <Table className='rounded-[20px] bg-white'>
            <TableHeader>
                <TableRow>
                    {tableHead.map((th, index) => (
                        <TableHead 
                            key={index}
                            className='py-3.5 px-6 font-bold text-sm leading-5 tracking-[0.4px] text-grayFive'
                        >
                            {th.icon ? (
                                <button className='flex items-center gap-0.5'>
                                    {th.label}
                                    {th.icon && (
                                        <span>{th.icon}</span>
                                    )}
                                </button>
                            ): (
                                <span>{th.label}</span>
                            )}
                        </TableHead>
                    ))}
                </TableRow>
            </TableHeader>
            <TableBody>
                {tableData.map((invoice) => (
                    <TableRow 
                        key={invoice.id}
                        className=' font-normal text-sm leading-5 tracking-[0.25px] text-graySix' 
                    >
                        <TableCell className={`${tableCellClass}`}>{invoice.id}</TableCell>
                        <TableCell className={`${tableCellClass}`}>{invoice.name}</TableCell>
                        <TableCell className={`${tableCellClass}`}>{invoice.mobile}</TableCell>
                        <TableCell className={`${tableCellClass}`}>{invoice.program}</TableCell>
                        <TableCell className={`${tableCellClass}`}>{invoice.intake}</TableCell>
                        <TableCell className={`${tableCellClass}`}>{invoice.country}</TableCell>
                        <TableCell className={`${tableCellClass}`}> 
                            <AllStudentStatus status={invoice.status} />
                        </TableCell>
                    </TableRow>
                ))}
            </TableBody>
        </Table>
        <div className='pt-14'>
            <Pagination
                currentPage={currentPage}
                totalPages={totalPages}
                onPageChange={goToPage}
            />
        </div>
        </>
    )
}

export default TableLayout