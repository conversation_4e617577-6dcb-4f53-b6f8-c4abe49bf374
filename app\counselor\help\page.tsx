'use client';

import React from 'react';
import Link from 'next/link';
import { FAQData } from '@/common';
import Plus from '@/app/assets/svg/plus';
import Minus from '@/app/assets/svg/Minus';
import ArrowForward from '@/app/assets/svg/arrow_forward';
import SupportAgent from '@/app/assets/svg/support_agent';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from '@/components/ui/accordion';


const page = () => {
    return (
        <DashboardLayout>
            <div className='py-5 flex flex-col gap-[30px]'>
                <div className='flex justify-end items-center'>
                    {/* <h2 className='md:text-[28px] text-[18px] md:leading-[33px] leading-[21px] font-bold text-graySix'>Help</h2> */}
                    <Link 
                        href={'/chat'} 
                        className='py-2.5 px-5 flex items-center gap-2 rounded-[50px] bg-primaryColor text-white md:text-sm text-xs leading-[17px] font-semibold drop-shadow-5xl hover:bg-secondaryColor'
                    >
                        <SupportAgent />
                        <span>Chat with Our Agent</span>
                        <ArrowForward className='w-[15px] h-[15px]' />
                    </Link>
                </div>
                <div className=''>
                    {FAQData.map((section, index) => {
                        const midIndex = Math.ceil(section.faqItem.length / 2);
                        const firstHalf = section.faqItem.slice(0, midIndex);
                        const secondHalf = section.faqItem.slice(midIndex);

                        return (
                            <div key={index} className='mb-6'>
                                <h3 className='text-lg leading-[21px] font-semibold mb-4 text-graySix'>
                                    {section.SectionTitle}
                                </h3>
                                <Accordion type='single' collapsible className='flex w-full self-start gap-6'>
                                    {/* First Column */}
                                    <div className='w-full flex flex-col gap-6'>
                                        {firstHalf.map((item, idx) => (
                                            <AccordionItem 
                                                key={idx} 
                                                value={`item-${index}-${item.id}`} 
                                                className='bg-white p-5 rounded-[10px] self-start w-full'
                                            >
                                                <AccordionTrigger 
                                                    openIcon={Minus} 
                                                    closeIcon={Plus} 
                                                    className='hover:no-underline text-base text-graySix font-medium'
                                                    IconClass='!text-graySix'
                                                >
                                                    <div className='w-[90%]'>
                                                        {item.question}
                                                    </div>
                                                </AccordionTrigger>
                                                <AccordionContent className='font-normal text-sm text-grayFour w-[90%]'>
                                                    {item.answer}
                                                </AccordionContent>
                                            </AccordionItem>
                                        ))}
                                    </div>

                                    {/* Second Column */}
                                    <div className='w-full flex flex-col gap-6'>
                                        {secondHalf.map((item, idx) => (
                                            <AccordionItem 
                                                key={idx} 
                                                value={`item-${index}-${item.id}`} 
                                                className='bg-white p-5 rounded-[10px] self-start w-full'
                                            >
                                                <AccordionTrigger
                                                    openIcon={Minus} 
                                                    closeIcon={Plus} 
                                                    className='hover:no-underline text-base text-graySix font-medium'
                                                    IconClass='!text-graySix'
                                                >
                                                    <div className='w-[90%]'>
                                                        {item.question}
                                                    </div>
                                                </AccordionTrigger>
                                                <AccordionContent className='font-normal text-sm text-grayFour w-[90%]'>
                                                    {item.answer}
                                                </AccordionContent>
                                            </AccordionItem>
                                        ))}
                                    </div>
                                </Accordion>
                            </div>
                        );
                    })}
                </div>
            </div>
        </DashboardLayout>
    )
}

export default page
