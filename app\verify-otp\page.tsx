'use client';

import OtpForm from '../components/OtpForm';
import OtpCover from '../assets/svg/OtpCover';
import React, { useEffect, useState } from 'react';
import AuthLayout from '../components/layout/AuthLayout';

const Page = () => {
    const [email, setEmail] = useState<string | null>(null);

    useEffect(() => {
        const storedEmail = sessionStorage.getItem('verificationEmail');
        setEmail(storedEmail);
    }, []);

    return (
        <AuthLayout
            imageSrc={<OtpCover />}
            description='Secure your journey—verify with <PERSON><PERSON> and unlock new opportunities!'
            title='OTP Verification'
            heading={`Enter the verification code we just sent to ${
                email ? email.replace(/(.{2})(.*)(@.*)/, '$1***$3') : 'your email'
            }.`}
        >
            <div className='pt-9 max-w-[420px] mx-auto'>
                <OtpForm />
            </div>
        </AuthLayout>
    );
};

export default Page;
