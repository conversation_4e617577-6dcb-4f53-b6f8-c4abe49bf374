import React from 'react';
import { CheckboxProps } from '@/types';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox'

export const CheckboxGroup: React.FC<CheckboxProps> = ({
    options,
    onChange,
    value = []
}) => {
    const handleChange = (id: string) => {
        const isSelected = value.includes(id);
        const updatedValues = isSelected
            ? value.filter((v) => v !== id)
            : [...value, id]; 
        onChange?.(updatedValues);
    };
    return (
        <div className='flex flex-col'>
            <Label className='font-medium text-sm text-grayFive mb-1.5'>
                Gender
            </Label>
            <div className='flex gap-10 mt-1.5'>
                {options.map((option) => (
                    <label
                        key={option.id}
                        className='flex items-center space-x-3 cursor-pointer'
                    >
                        <Checkbox
                            className='border border-grayFour rounded-md'
                            checked={value.includes(option.id)}
                            onCheckedChange={() => handleChange(option.id)}
                        />
                        <span className='font-normal text-sm text-grayFive'>{option.label}</span>
                    </label>
                ))}
            </div>
        </div>
    );
};
