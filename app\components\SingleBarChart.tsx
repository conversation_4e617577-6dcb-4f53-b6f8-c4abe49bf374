// 'use client'

// import { useState } from 'react';
// import DateRangeSelect from './DateRangeSelect';
// import { CounselorBarChartData } from '@/common';
// import { Bar, Bar<PERSON><PERSON>, Legend, XAxis, YAxis } from 'recharts';
// import {
//     Card,
//     CardContent,
//     CardHeader,
//     CardTitle,
// } from '@/components/ui/card';
// import {
//     ChartContainer,
//     ChartLegend,
//     ChartLegendContent,
//     ChartTooltip,
//     ChartTooltipContent,
// } from '@/components/ui/chart';
// interface ChartConfig {
//     [key: string]: { label: string; color: string, name: string };
// }

// interface SingleBarChartProps {
//     data: Record<string, any[]>; 
//     chartConfig: ChartConfig;     
//     title: string;                
//     chartHeightClass?: string;
//     barRadius?: number;
//     barSize?: number;
//     xAxisLabelAngle?: number;
//     showLegend?: boolean;
//     xAxisHeight?: number;
//     xAxisTickMargin?: number;
// }

// export function SingleBarChart({
//     data,
//     title,
//     chartConfig, 
//     chartHeightClass = 'max-h-[400px]',
//     barRadius = 8,
//     barSize= 50,
//     xAxisLabelAngle = 0,
//     xAxisHeight = 50,
//     xAxisTickMargin = 40,
//     showLegend = true
// }:SingleBarChartProps) {

//     const [selectedRange, setSelectedRange] = useState<keyof typeof data>('This Year');
//         const chartData = data[selectedRange] || [];

//         const xAxisKey = chartData.length > 0 
//             ? Object.keys(chartData[0]).find(key => ['month', 'day', 'hour','designation','percentage','Students','country'].includes(key)) || 'month'
//             : 'month';

//         const barKeys = Object.keys(chartConfig);
    
//         const options = Object.keys(data); 


//     return (
//         <Card className="drop-shadow-none shadow-none border-none rounded-[20px]">
//             <CardHeader className="space-y-[26px]">
//                 <CardTitle className=" flex md:flex-row flex-col gap-4 justify-between">
//                     <div className="flex items-center gap-2.5 md:text-xl text-base md:leading-[29px] leading-5 text-grayFive font-bold">
//                         <h2>{title}</h2>
//                     </div>
//                 </CardTitle>
//             </CardHeader>
//             <CardContent className='pl-0'>
//                 <ChartContainer 
//                     className={`${chartHeightClass} w-full`} 
//                     config={chartConfig}
                    
//                 >
//                     <BarChart 
//                         accessibilityLayer
//                         data={chartData} 
//                     >
//                         <YAxis
//                             axisLine={false}
//                             tickLine={false}
//                             tickMargin={30}
//                             textAnchor="left"
//                         />
//                         <XAxis
//                             dataKey={xAxisKey}
//                             tickLine={false}
//                             tickMargin={xAxisTickMargin}
//                             axisLine={false}
//                             tickFormatter={(value) => value.slice(0)}
//                             angle={xAxisLabelAngle}
//                             height={xAxisHeight}
//                         />
//                         {showLegend && 
//                             <Legend verticalAlign="top" iconType='plainline' />
//                         } 
//                         <ChartTooltip
//                             cursor={false}
//                             content={<ChartTooltipContent />}
//                         />
//                         {barKeys.map((key) => (
//                             <Bar 
//                                 name={chartConfig[key]?.name} 
//                                 key={key} 
//                                 dataKey={key} 
//                                 fill={chartConfig[key]?.color}
//                                 radius={barRadius} 
//                                 barSize={barSize}
//                             />
//                         ))}
//                     </BarChart>
//                 </ChartContainer>
//             </CardContent>
            
//         </Card>
//     );
// }


'use client'

import { Bar, BarChart, CartesianGrid, Legend, XAxis, YAxis } from 'recharts';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import {
    ChartContainer,
    ChartTooltip,
    ChartTooltipContent,
} from '@/components/ui/chart';

interface ChartConfig {
    [key: string]: { label: string; color: string, name: string };
}

interface SingleBarChartProps {
    data: any[];                  // Simple array of data objects
    chartConfig: ChartConfig;     // Dynamic chart configuration
    title: string;                // Title for the chart (e.g., "Students", "Visa Success")
    chartHeightClass?: string;
    barRadius?: number;
    barSize?: number;
    xAxisLabelAngle?: number;
    showLegend?: boolean;
    xAxisHeight?: number;
    xAxisTickMargin?: number;
    xAxisKey?: string; 
    textAnchor?: 'end' | 'middle' | 'start' | 'inherit' 
}

export function SingleBarChart({
    data,
    title,
    chartConfig, 
    chartHeightClass = 'max-h-[400px]',
    barRadius = 8,
    barSize= 50,
    xAxisLabelAngle = 0,
    xAxisHeight = 50,
    xAxisTickMargin = 20,
    showLegend = true,
    xAxisKey,
    textAnchor = 'end'

}:SingleBarChartProps) {

    // Determine the X-Axis field dynamically if not provided
    const determineXAxisKey = () => {
        if (xAxisKey) return xAxisKey;
        
        if (data.length > 0) {
            const firstItem = data[0];
            // Look for common x-axis keys
            for (const key of ['country', 'month', 'day', 'hour', 'designation', 'name', 'category',]) {
                if (key in firstItem) return key;
            }
            // Fallback to the first key that's not in chartConfig
            const possibleKey = Object.keys(firstItem).find(k => !(k in chartConfig));
            return possibleKey || Object.keys(firstItem)[0];
        }
        return 'category'; // Default fallback
    };

    const actualXAxisKey = determineXAxisKey();
    
    // Get all keys excluding the xAxisKey (these are the bars)
    const barKeys = Object.keys(chartConfig);

    return (
        <Card className="drop-shadow-none shadow-none border-none rounded-[20px]">
            <CardHeader className="space-y-[26px]">
                <CardTitle className="flex md:flex-row flex-col gap-4 justify-between">
                    <div className="flex items-center gap-2.5 md:text-xl text-base md:leading-[29px] leading-5 text-grayFive font-bold">
                        <h2>{title}</h2>
                    </div>
                </CardTitle>
            </CardHeader>
            <CardContent className='pl-0'>
                <ChartContainer 
                    className={`${chartHeightClass} w-full`} 
                    config={chartConfig}
                >
                    <BarChart 
                        accessibilityLayer
                        data={data} 
                    >
                          <CartesianGrid vertical={false} strokeDasharray="3 3" />
                        <YAxis
                            axisLine={false}
                            tickLine={false}
                            tickMargin={30}
                            textAnchor="left"
                        />
                        <XAxis
                            dataKey={actualXAxisKey}
                            tickLine={false}
                            tickMargin={xAxisTickMargin}
                            axisLine={true}
                            angle={xAxisLabelAngle}
                            height={xAxisHeight}
                            textAnchor={textAnchor}
                        />
                        {showLegend && 
                            <Legend verticalAlign="top" iconType='plainline' />
                        } 
                        <ChartTooltip
                            cursor={false}
                            content={<ChartTooltipContent />}
                        />
                        {barKeys.map((key) => (
                            <Bar 
                                name={chartConfig[key]?.name} 
                                key={key} 
                                dataKey={key} 
                                fill={chartConfig[key]?.color}
                                radius={barRadius} 
                                barSize={barSize}
                            />
                        ))}
                    </BarChart>
                </ChartContainer>
            </CardContent>
        </Card>
    );
}