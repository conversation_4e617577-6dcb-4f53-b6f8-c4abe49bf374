import React from 'react'

const UkLarge = () => {
    return (
        <svg width="45" height="44" viewBox="0 0 45 44" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_2303_13065)">
                <path d="M2.375 2H42.375V42H2.375V2Z" fill="#012169" />
                <path d="M42.375 2V7L27.5312 22L42.375 36.6094V42H37.1406L22.2188 27.3125L7.6875 42H2.375V36.6875L16.9062 22.0781L2.375 7.78125V2H7.21875L22.2188 16.6875L36.75 2H42.375Z" fill="white" />
                <path d="M16.75 27.3125L17.6094 29.9688L5.65625 42H2.375V41.7656L16.75 27.3125ZM26.4375 26.375L30.6562 27L42.375 38.4844V42L26.4375 26.375ZM42.375 2L27.375 17.3125L27.0625 13.875L38.7812 2H42.375ZM2.375 2.07812L17.4531 16.8438L12.8438 16.2188L2.375 5.82812V2.07812Z" fill="#C8102E" />
                <path d="M16.125 2V42H28.625V2H16.125ZM2.375 15.75V28.25H42.375V15.75H2.375Z" fill="white" />
                <path d="M2.375 18.25V25.75H42.375V18.25H2.375ZM18.625 2V42H26.125V2H18.625Z" fill="#C8102E" />
            </g>
            <rect x="1.375" y="1" width="42" height="42" rx="21" stroke="#C7C7CA" strokeWidth="2" />
            <defs>
                <clipPath id="clip0_2303_13065">
                    <rect x="2.375" y="2" width="40" height="40" rx="20" fill="white" />
                </clipPath>
            </defs>
        </svg>
    )
}

export default UkLarge