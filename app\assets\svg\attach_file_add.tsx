import React from 'react'

const attach_file_add = () => {
    return (
        <svg width="23" height="24" viewBox="0 0 23 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_3724_36239" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="23" height="24">
            <rect x="0.457031" y="0.853516" width="22.4587" height="22.4587" fill="#D9D9D9"/>
            </mask>
            <g mask="url(#mask0_3724_36239)">
            <path d="M11.5511 20.9722C10.0313 20.9722 8.74122 20.4388 7.68067 19.372C6.62012 18.3054 6.08984 17.0122 6.08984 15.4925V7.07938C6.08984 5.99964 6.46478 5.08188 7.21465 4.32608C7.96437 3.57028 8.87909 3.19238 9.95882 3.19238C11.0386 3.19238 11.9534 3.57028 12.7032 4.32608C13.4531 5.08188 13.828 5.99964 13.828 7.07938V13.252C13.828 13.4509 13.7607 13.6176 13.6261 13.7522C13.4915 13.8866 13.3248 13.9538 13.126 13.9538C12.927 13.9538 12.7602 13.8866 12.6258 13.7522C12.4915 13.6176 12.4244 13.4509 12.4244 13.252V7.07049C12.4147 6.37941 12.1747 5.79424 11.7043 5.31496C11.2339 4.83569 10.6521 4.59605 9.95882 4.59605C9.26853 4.59605 8.68507 4.83717 8.20845 5.31941C7.73182 5.8018 7.49351 6.38846 7.49351 7.07938V15.4925C7.48384 16.6239 7.87492 17.586 8.66675 18.3789C9.45857 19.172 10.4202 19.5685 11.5515 19.5685C11.8167 19.5685 12.0722 19.5412 12.3182 19.4866C12.5641 19.4321 12.7992 19.3628 13.0235 19.2789C13.2071 19.2092 13.3884 19.2116 13.5674 19.2861C13.7463 19.3607 13.8716 19.4896 13.9431 19.6729C14.0127 19.8564 14.0088 20.0382 13.9314 20.2182C13.8541 20.3982 13.7236 20.5229 13.5401 20.5925C13.2305 20.7173 12.9093 20.8117 12.5764 20.8758C12.2435 20.9401 11.9017 20.9722 11.5511 20.9722ZM16.2931 20.0364C16.0941 20.0364 15.9275 19.9691 15.7932 19.8345C15.6587 19.7001 15.5915 19.5334 15.5915 19.3346V17.409H13.6661C13.4673 17.409 13.3006 17.3417 13.166 17.2071C13.0315 17.0725 12.9643 16.9058 12.9643 16.7069C12.9643 16.5079 13.0315 16.3413 13.166 16.207C13.3006 16.0725 13.4673 16.0053 13.6661 16.0053H15.5915V14.0799C15.5915 13.8811 15.6588 13.7144 15.7934 13.5798C15.928 13.4453 16.0948 13.3781 16.2938 13.3781C16.4927 13.3781 16.6593 13.4453 16.7938 13.5798C16.928 13.7144 16.9952 13.8811 16.9952 14.0799V16.0053H18.9208C19.1196 16.0053 19.2863 16.0726 19.4207 16.2072C19.5553 16.3418 19.6226 16.5086 19.6226 16.7076C19.6226 16.9065 19.5553 17.0731 19.4207 17.2076C19.2863 17.3418 19.1196 17.409 18.9208 17.409H16.9952V19.3346C16.9952 19.5334 16.9279 19.7001 16.7933 19.8345C16.6587 19.9691 16.492 20.0364 16.2931 20.0364ZM9.95859 16.7071C9.75974 16.7071 9.59309 16.6399 9.45865 16.5055C9.32436 16.3709 9.25722 16.2042 9.25722 16.0053V7.52949C9.25722 7.33063 9.32444 7.16391 9.45888 7.02931C9.59348 6.89487 9.76028 6.82765 9.95929 6.82765C10.1581 6.82765 10.3248 6.89487 10.4592 7.02931C10.5935 7.16391 10.6607 7.33063 10.6607 7.52949V16.0053C10.6607 16.2042 10.5934 16.3709 10.4588 16.5055C10.3243 16.6399 10.1576 16.7071 9.95859 16.7071ZM16.2931 11.3264C16.0941 11.3264 15.9275 11.2592 15.7932 11.1248C15.6587 10.9902 15.5915 10.8234 15.5915 10.6246V7.52949C15.5915 7.33063 15.6588 7.16391 15.7934 7.02931C15.928 6.89487 16.0948 6.82765 16.2938 6.82765C16.4927 6.82765 16.6593 6.89487 16.7938 7.02931C16.928 7.16391 16.9952 7.33063 16.9952 7.52949V10.6246C16.9952 10.8234 16.9279 10.9902 16.7933 11.1248C16.6587 11.2592 16.492 11.3264 16.2931 11.3264Z" fill="#7A7B82"/>
            </g>
        </svg>
    )
}

export default attach_file_add