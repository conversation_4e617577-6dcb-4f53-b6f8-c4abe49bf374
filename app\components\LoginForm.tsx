'use client';
import { z } from 'zod';
import Link from 'next/link';
import Cookies from 'js-cookie';
import React, { useState } from 'react';
import { signInSchema } from '@/schema';
import { signIn } from 'next-auth/react';
import { useForm } from 'react-hook-form';
import GoogleSignIn from './GoogleSignIn';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import Visibility from '@/app/assets/svg/visibility';
import { zodResolver } from '@hookform/resolvers/zod';
import VisibilityOff from '@/app/assets/svg/visibility_off';
import {
    Form,
    FormItem,
    FormLabel,
    FormField,
    FormMessage,
    FormControl,
} from '@/components/ui/form';
import { useLoginMutation } from '@/lib/redux/api/authApi';

const LoginForm = () => {
    const router = useRouter();
    const [showPassword, setShowPassword] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [login, { isLoading }] = useLoginMutation();

    const form = useForm<z.infer<typeof signInSchema>>({
        resolver: zodResolver(signInSchema),
        defaultValues: {
            email: '',
            password: '',
        },
    });

    const togglePasswordVisibility = () => setShowPassword((prev) => !prev);

    const onSubmit = async (values: z.infer<typeof signInSchema>) => {
        setError(null);

        try {
            const res = await login(values).unwrap();
            if (res.success) {
                Cookies.set('accessToken', res.accessToken || '');
                router.push('/');
                router.refresh();
            } else {
                setError('Invalid email or password.');
            }
        } catch (err: any) {
            console.error('Login failed:', err);
            setError('Login failed. Please try again.');
        }
    };

    return (
        <div>
            <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)}>
                    {error && (
                        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md mb-4">
                            {error}
                        </div>
                    )}

                    <FormField
                        control={form.control}
                        name="email"
                        render={({ field }) => (
                            <FormItem className="space-y-1.5">
                                <FormLabel className="text-sm font-medium text-graySix">
                                    Email*
                                </FormLabel>
                                <FormControl>
                                    <input
                                        className="flex w-full rounded-[8px] outline-none border focus:ring-1 border-tertiary border-opacity-20 py-2.5 px-4 placeholder:text-grayThree shadow-sm"
                                        placeholder="Enter your email"
                                        type="email"
                                        disabled={isLoading}
                                        {...field}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="password"
                        render={({ field }) => (
                            <FormItem className="space-y-1.5 mt-5">
                                <FormLabel className="text-sm font-medium text-graySix">
                                    Password*
                                </FormLabel>
                                <FormControl>
                                    <div className="relative">
                                        <input
                                            type={showPassword ? 'text' : 'password'}
                                            className="flex w-full rounded-[8px] outline-none border focus:ring-1 border-tertiary border-opacity-20 py-2.5 px-4 placeholder:text-grayThree shadow-sm"
                                            placeholder="Enter your password"
                                            disabled={isLoading}
                                            {...field}
                                        />
                                        <button
                                            type="button"
                                            onClick={togglePasswordVisibility}
                                            className="absolute right-3 top-1/2 transform -translate-y-1/2"
                                            disabled={isLoading}
                                        >
                                            {showPassword ? (
                                                <VisibilityOff />
                                            ) : (
                                                <Visibility className="text-grayThree" />
                                            )}
                                        </button>
                                    </div>
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <div className="flex justify-between items-center mt-6">
                        <div className="flex items-center space-x-2 py-1">
                            <Checkbox className="border-tertiary border-opacity-20 shadow-none" />
                            <label className="text-sm font-medium text-graySix">
                                Remember me
                            </label>
                        </div>
                        <Link
                            href="/forget-password"
                            className="text-primaryColor font-semibold text-sm"
                        >
                            Forgot password
                        </Link>
                    </div>

                    <Button
                        type="submit"
                        disabled={isLoading}
                        className="w-full bg-primaryColor rounded-[8px] py-2.5 mt-6 mb-5 hover:bg-tertiary text-white text-base font-semibold disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        {isLoading ? 'Logging in...' : 'Log in'}
                    </Button>
                </form>
            </Form>

            <div className="w-full">
                <GoogleSignIn />
            </div>

            <div className="flex justify-center gap-1 mt-8">
                <span className="text-sm font-normal text-grayFive">
                    Don't have an account?
                </span>
                <Link
                    className="text-sm font-semibold text-primaryColor"
                    href="/sign-up"
                >
                    Sign up
                </Link>
            </div>
        </div>
    );
};

export default LoginForm;