'use client';

// import { useEffect, useState } from 'react';
import { StepperProps } from '@/types';
import RightIcon from '@/app/assets/svg/righticon';


const Stepper: React.FC<StepperProps> = ({ steps, currentStep }) => {
    const isStepperDisabled = steps.some((step) => step.status === 'rejected');
    return (
        // <div className="w-full flex items-center overflow-x-auto">
        //     {steps.map((step, index) => {
        //         const isCompleted = index < currentStep;
        //         const isInProgress = index === currentStep;

        //         return (
        //             <div
        //                 key={index}
        //                 className="flex py-6 h-[106px] justify-cente items-center w-full"
        //             >
        //                 <div className="flex flex-col items-cente relative">
        //                     <div
        //                         className={`w-5 h-5 mx-1.5 rounded-full flex items-center justify-center 
        //                         ${isCompleted ? 'bg-primaryColor border-primaryColor text-white' : ''}
        //                         ${isInProgress ? 'bg-white border-primaryColor text-primaryColor' : ''}
        //                         ${
        //                             !isCompleted && !isInProgress
        //                                 ? 'bg-grayThree border-grayThree text-white'
        //                                 : ''
        //                         }
        //                         transition duration-300`}
        //                     >
        //                         {isCompleted ? '✔' : index + 1}
        //                     </div>
        //                     <span
        //                         className={`text-xs leading-[14px] absolute left-0 top-6 whitespace-nowrap text-center  ${
        //                             isCompleted || isInProgress ? 'text-primaryColor' : 'text-grayThree'
        //                         }`}
        //                     >
        //                         {step}
        //                     </span>
        //                 </div>

        //                 {index < steps.length - 1 && (
        //                     <div
        //                         className={`flex-1 h-0.5 ${
        //                             isCompleted ? 'bg-primaryColor' : 'bg-grayThree'
        //                         }`}
        //                     ></div>
        //                 )}
        //             </div>
        //         );
        //     })}
        // </div>
        <ul className={`flex overflow-hidden w-full space-x-8 ${
            isStepperDisabled ? 'opacity-50 pointer-events-none' : ''
        }`}>
            {steps.map((step, index) => (
                <li
                    key={index}
                    className="relative min-w-24 w-full"
                >
                    <div className="grid grid-cols-1 gap-4 w-full">
                        <div className="relative justify-center flex items-center">
                            <div
                                className={`w-5 h-5 flex justify-center items-center rounded-full border ${
                                    step.status === 'completed'
                                        ? 'bg-primaryColor border-primaryColor text-white'
                                        : step.status === 'active'
                                        ? 'bg-primaryColor border-primaryColor'
                                        : step.status === 'regular'
                                        ? 'bg-white border-grayTwo'
                                        : ''
                                }`}
                            >
                                {step.status === 'completed' ? (
                                    <div><RightIcon /></div>
                                ) : step.status === 'active' ? (
                                    <div className="w-[8px] h-[8px] rounded-full bg-white"></div>
                                ) : step.status === 'regular' ? (
                                    <div className="w-[8px] h-[8px] rounded-full bg-grayThree"></div>
                                ) : (
                                    <div className="w-[8px] h-[8px] rounded-full bg-grayThree"></div>
                                )}
                            </div>
                            {index !== steps.length - 1 && (
                                <div
                                    className={`absolute left-[63%] h-0.5 w-full ${
                                        step.status === 'completed'
                                            ? 'bg-primaryColor'
                                            : 'bg-gray-300'
                                    }`}
                                ></div>
                            )}
                        </div>
                        {/* Optional: Display the step's title below */}
                        <div className={` text-xs font-medium text-center ${
                                step.status === 'completed'
                                    ? 'text-primaryColor border-primaryColor'
                                    : step.status === 'active'
                                    ? 'text-primaryColor border-primaryColor'
                                    : step.status === 'regular'
                                    ? ' text-grayThree border-grayTwo'
                                    : 'text-grayThree'
                                }`}>
                            {step.title}
                        </div>
                    </div>
                </li>
            ))}
        </ul>
    );
};



export default Stepper;
