import React from 'react';

const CounselorBarChartIcon = () => {
    return (
        <svg
            width="20"
            height="21"
            viewBox="0 0 20 21"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <g clipPath="url(#clip0_1010_3693)">
                <path
                    fillRule="evenodd"
                    clipRule="evenodd"
                    d="M10.0004 9.65399C10.4724 9.65399 10.9339 9.79397 11.3264 10.0562C11.7188 10.3185 12.0248 10.6912 12.2054 11.1274C12.386 11.5635 12.4333 12.0434 12.3412 12.5063C12.2491 12.9693 12.0218 13.3946 11.688 13.7284C11.3542 14.0622 10.929 14.2895 10.466 14.3816C10.003 14.4737 9.52312 14.4264 9.087 14.2458C8.65089 14.0651 8.27813 13.7592 8.01588 13.3667C7.75362 12.9742 7.61364 12.5128 7.61364 12.0407C7.61364 11.4077 7.8651 10.8006 8.3127 10.353C8.76029 9.90545 9.36736 9.65399 10.0004 9.65399ZM9.91989 5.51516C9.99622 5.54213 10.0797 5.54074 10.1551 5.51126V5.51153C11.8641 4.87179 13.5744 4.23518 15.286 3.60169C15.2967 3.59766 15.3082 3.59616 15.3196 3.5973C15.331 3.59844 15.342 3.60219 15.3517 3.60826C15.3614 3.61433 15.3696 3.62255 15.3756 3.63229C15.3816 3.64203 15.3853 3.65302 15.3864 3.66442L15.4919 4.73669C15.4936 4.75251 15.4902 4.76847 15.4821 4.78218C15.474 4.79589 15.4617 4.80661 15.447 4.81274C15.35 4.85435 15.2675 4.92387 15.2101 5.01245C15.1528 5.10103 15.123 5.20467 15.1247 5.3102L15.1427 6.41493C15.143 6.42916 15.146 6.44321 15.1516 6.45627C15.1573 6.46934 15.1654 6.48116 15.1756 6.49107C15.1859 6.50098 15.1979 6.50878 15.2111 6.51403C15.2244 6.51928 15.2385 6.52187 15.2527 6.52165L16.0995 6.50778C16.1283 6.50735 16.1557 6.49552 16.1757 6.4749C16.1957 6.45428 16.2067 6.42655 16.2063 6.39782L16.1882 5.29309C16.1861 5.17266 16.1432 5.05648 16.0666 4.96354C15.99 4.8706 15.8841 4.80638 15.7663 4.78137C15.7507 4.77824 15.7365 4.77014 15.7258 4.75828C15.7152 4.74641 15.7087 4.73143 15.7073 4.71555L15.5922 3.54735C15.5904 3.5309 15.5942 3.51434 15.603 3.50031C15.6117 3.48628 15.625 3.4756 15.6405 3.46997C15.9221 3.36546 16.2034 3.26089 16.4845 3.15626C16.4905 3.15393 16.4957 3.1498 16.4993 3.14444C16.5029 3.13907 16.5048 3.13272 16.5047 3.12625C16.5046 3.11978 16.5025 3.11349 16.4987 3.10825C16.4949 3.103 16.4896 3.09905 16.4835 3.09692L10.0809 0.950046C10.0046 0.923084 9.92108 0.924471 9.84567 0.953952L3.51681 3.30907C3.51063 3.31121 3.50527 3.31523 3.5015 3.32057C3.49773 3.32592 3.49573 3.33231 3.49578 3.33885C3.49583 3.34539 3.49793 3.35174 3.50179 3.35703C3.50565 3.36231 3.51106 3.36625 3.51728 3.36829L9.91989 5.51516ZM14.2667 6.72329C14.2566 6.72794 14.2463 6.73235 14.2359 6.73649L10.3002 8.30509C10.1618 8.36209 10.0069 8.36466 9.86661 8.31227L5.88173 6.87333C5.84681 6.86071 5.81849 6.84208 5.77978 6.86813C5.76936 6.87497 5.76086 6.88437 5.75511 6.89543C5.74936 6.90649 5.74654 6.91884 5.74692 6.9313L5.75118 7.19262C5.75122 7.26437 5.77365 7.33431 5.81535 7.39268C5.85706 7.45106 5.91594 7.49496 5.9838 7.51825L9.96872 8.95723C10.0497 8.98827 10.1395 8.98687 10.2195 8.95333L14.1551 7.3847C14.2222 7.35917 14.2797 7.3133 14.3195 7.25351C14.3592 7.19371 14.3793 7.12298 14.3769 7.05122L14.3726 6.79012C14.3725 6.77768 14.3692 6.76547 14.3631 6.75461C14.357 6.74375 14.3483 6.73459 14.3378 6.72796C14.3272 6.72133 14.3152 6.71744 14.3028 6.71664C14.2904 6.71585 14.2779 6.71817 14.2667 6.72341V6.72329ZM5.73665 6.3047L5.70681 4.48048C5.70656 4.4685 5.7092 4.45665 5.71451 4.44591C5.71981 4.43518 5.72763 4.42588 5.73729 4.41881C5.74695 4.41173 5.75817 4.40709 5.77001 4.40527C5.78185 4.40346 5.79394 4.40452 5.80528 4.40837L9.83817 5.76067C9.97051 5.80684 10.115 5.80443 10.2457 5.75387V5.7536L14.2317 4.26989C14.2429 4.26567 14.255 4.26422 14.2668 4.26565C14.2787 4.26708 14.2901 4.27135 14.3 4.2781C14.3099 4.28485 14.318 4.29388 14.3237 4.30443C14.3293 4.31499 14.3324 4.32674 14.3325 4.33872L14.3624 6.16294C14.3647 6.23468 14.3446 6.30538 14.3049 6.36515C14.2651 6.42491 14.2077 6.47077 14.1406 6.4963L10.2049 8.06473C10.125 8.09829 10.0351 8.09969 9.95419 8.06864L5.96911 6.62977C5.90131 6.60651 5.84246 6.56268 5.80076 6.50438C5.75907 6.44607 5.7366 6.37622 5.73649 6.30454L5.73665 6.3047ZM6.75142 15.2934C6.4488 15.14 6.11413 15.0606 5.77485 15.0615H3.81907C3.24032 15.0633 2.68579 15.294 2.27654 15.7033C1.8673 16.1125 1.63659 16.667 1.63478 17.2458V17.5045C1.63461 17.5147 1.63663 17.5249 1.64072 17.5344C1.64481 17.5438 1.65087 17.5522 1.65849 17.5591C2.1709 18.0374 2.78583 18.3923 3.45632 18.5968C4.12681 18.8012 4.83514 18.8497 5.52724 18.7386C5.57208 18.7314 5.63583 18.6678 5.54286 18.5874C5.5249 18.572 5.51049 18.5529 5.50062 18.5314C5.49075 18.5099 5.48565 18.4865 5.48567 18.4628V17.9913C5.48627 17.4932 5.60185 17.002 5.8234 16.5559C6.04496 16.1098 6.36652 15.7209 6.76302 15.4195C6.77307 15.4118 6.78101 15.4016 6.78609 15.39C6.79117 15.3784 6.79321 15.3657 6.79204 15.3531C6.79087 15.3405 6.78652 15.3284 6.77939 15.318C6.77227 15.3075 6.7626 15.299 6.7513 15.2933L6.75142 15.2934ZM6.12642 18.6709L5.83958 18.417L5.81481 18.3615V17.9916C5.81718 17.2213 6.12423 16.4833 6.66891 15.9386C7.21358 15.3939 7.95164 15.0869 8.72192 15.0845H11.2792C12.8634 15.0845 14.1863 16.3763 14.1863 17.9916V18.36C14.1864 18.3705 14.1843 18.381 14.1799 18.3906C14.1756 18.4003 14.1692 18.4088 14.1613 18.4158L13.8835 18.6634L13.8838 18.6637L13.4435 18.998C12.3964 19.7197 11.1491 20.0941 9.87763 20.0683C8.60619 20.0426 7.375 19.6179 6.35802 18.8544L6.12642 18.6711V18.6709ZM4.79685 10.5204C5.16549 10.5204 5.52585 10.6297 5.83237 10.8345C6.13888 11.0393 6.37778 11.3304 6.51886 11.671C6.65994 12.0115 6.69686 12.3863 6.62494 12.7479C6.55303 13.1094 6.37552 13.4415 6.11485 13.7022C5.85419 13.9629 5.52208 14.1404 5.16052 14.2123C4.79897 14.2842 4.4242 14.2473 4.08362 14.1063C3.74304 13.9652 3.45194 13.7263 3.24714 13.4198C3.04233 13.1133 2.93302 12.7529 2.93302 12.3843C2.93303 11.8899 3.1294 11.4159 3.47893 11.0663C3.82846 10.7168 4.30253 10.5204 4.79685 10.5204ZM14.4734 18.7383C15.1225 18.8425 15.7864 18.8064 16.4204 18.6325C17.0544 18.4586 17.6437 18.1509 18.1488 17.73L18.1485 17.7297L18.341 17.5588C18.349 17.5518 18.3555 17.5433 18.3598 17.5336C18.3641 17.5239 18.3663 17.5134 18.3661 17.5029V17.2456C18.3647 16.6668 18.1341 16.112 17.7248 15.7027C17.3155 15.2934 16.7607 15.0628 16.1818 15.0613H14.2261C13.8865 15.0609 13.5516 15.1404 13.2485 15.2934C13.2372 15.2992 13.2276 15.3076 13.2205 15.3181C13.2133 15.3286 13.209 15.3407 13.2078 15.3533C13.2066 15.3659 13.2087 15.3786 13.2138 15.3902C13.2188 15.4018 13.2268 15.4119 13.2368 15.4196C13.6334 15.721 13.955 16.1099 14.1767 16.5559C14.3984 17.002 14.5142 17.4932 14.515 17.9913V18.4625H14.5145C14.5145 18.5965 14.3854 18.5963 14.4139 18.6869C14.4179 18.7002 14.4256 18.7122 14.4361 18.7213C14.4467 18.7304 14.4596 18.7363 14.4734 18.7383L14.4734 18.7383ZM15.2039 10.5204C15.5725 10.5203 15.9329 10.6296 16.2394 10.8344C16.546 11.0392 16.7849 11.3302 16.926 11.6708C17.0671 12.0114 17.104 12.3861 17.0322 12.7477C16.9603 13.1093 16.7828 13.4414 16.5221 13.7021C16.2615 13.9628 15.9293 14.1403 15.5678 14.2123C15.2062 14.2842 14.8315 14.2473 14.4909 14.1062C14.1503 13.9652 13.8592 13.7263 13.6544 13.4198C13.4495 13.1133 13.3402 12.7529 13.3402 12.3843C13.3402 11.89 13.5366 11.4159 13.8861 11.0664C14.2356 10.7169 14.7096 10.5205 15.2039 10.5204V10.5204Z"
                    fill="currentColor"
                />
            </g>
            <defs>
                <clipPath id="clip0_1010_3693">
                    <rect
                        width="20"
                        height="20"
                        fill="white"
                        transform="translate(0 0.5)"
                    />
                </clipPath>
            </defs>
        </svg>
    );
};

export default CounselorBarChartIcon;