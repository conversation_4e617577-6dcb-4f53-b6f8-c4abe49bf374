'use client'
import Image from 'next/image';
import React, { useState } from 'react';
import Avatar from '@/app/assets/img/Avatar.png';
import { Checkbox } from '@/components/ui/checkbox';
import { ChevronDown, ChevronUp } from 'lucide-react';

const EmployeeAvatars = ({ count }: { count: number }) => (
    <div className='flex items-center'>
        <div className='flex -space-x-2'>
            <Image className='inline-block h-6 w-6 rounded-full ring-2 ring-white' src={Avatar} alt='Employee 1' />
            <Image className='inline-block h-6 w-6 rounded-full ring-2 ring-white' src={Avatar} alt='Employee 2' />
            <Image className='inline-block h-6 w-6 rounded-full ring-2 ring-white' src={Avatar} alt='Employee 3' />
        </div>
        {count > 3 &&
            <div className='-ml-3 h-6 w-6 rounded-full border border-white bg-grayTwo flex items-center justify-center text-[10px] leading-none text-white'>
                +{count - 3}
            </div>
        }
        <span className='ml-2 font-normal text-xs leading-none text-graySix'>Employees</span>
    </div>
);


export interface TreeCardDataItem {
    agencyName?: string;
    departmentName?: string;
    teamName?: string;
    name?: string;
    designation?: string;
    employList?: any[];
    children?: TreeCardDataItem[];
    defaultOpen?: boolean;
    selected?: boolean;
}

interface TreeCardViewProps extends TreeCardDataItem {
    depth?: number;
}

const TreeCardView: React.FC<TreeCardViewProps> = ({
    agencyName,
    departmentName,
    teamName,
    name,
    designation,
    employList = [],
    children,
    defaultOpen = false,
    selected = false,
    depth = 0,
}) => {
    const [open, setOpen] = useState(defaultOpen);
    const [isSelected, setIsSelected] = useState(selected);
    const hasChildren = children && children.length > 0;
    const isAgency = !!agencyName;
    const isDepartment = !!departmentName;
    const isTeam = !!teamName;

    const handleCardClick = () => {
        if (isTeam) {
            setIsSelected(!isSelected);
        }
        if (hasChildren) {
            setOpen(!open);
        }
    };

    const renderCardContent = () => {
        const headDesignation = designation;
        const childCount = children?.length || 0;

        return (
            <div className={`rounded-[10px] py-3 px-4 cursor-pointer ${isTeam && isSelected ? 'bg-[#1E62E01A]' : 'bg-white'}`}>
                <div className='flex justify-between items-start'>
                    <div className='flex items-center gap-4'>
                        {isAgency && (
                            <div className='flex flex-col gap-3.5'>
                                <div>
                                    <span className='font-semibold text-sm leading-none text-graySix'>{agencyName}</span>
                                </div>
                                <div className='flex items-center gap-4'>
                                    <div>
                                        <Image src={Avatar} alt='d' width={38} height={38} />
                                    </div>
                                    <div className='flex flex-col gap-1'>
                                        <span className='font-normal text-sm leading-[18px] text-graySix'>{name}</span>
                                        <span className='font-normal text-xs leading-none text-grayThree'>{headDesignation}</span>
                                    </div>
                                </div>
                            </div>
                        )}
                        {(isDepartment || isTeam) && (
                            <div className='flex flex-col gap-3.5'>
                                <div>
                                    <span className='font-semibold text-sm leading-none text-graySix'>{departmentName || teamName}</span>
                                </div>
                                <div className='flex items-center gap-4'>
                                    <div>
                                        <Image src={Avatar} alt='d' width={38} height={38} />
                                    </div>
                                    <div className='flex flex-col gap-1'>
                                        <span className='font-normal text-sm leading-[18px] text-graySix'>{name}</span>
                                        <span className='font-normal text-xs leading-none text-grayThree'>{headDesignation}</span>
                                    </div>
                                </div>
                            </div>
                        )}
                    </div>

                    <div className='flex flex-col gap-4'>
                        {isAgency && (
                            <div className='flex items-center gap-1 text-sm text-gray-500'>
                                <span>{childCount} Departments</span>
                                {open ? <ChevronUp className='w-4 h-4' /> : <ChevronDown className='w-4 h-4' />}
                            </div>
                        )}
                        {isDepartment && (
                            <div className='flex items-center justify-end gap-1 text-sm text-gray-500'>
                                <span>{childCount} Teams</span>
                                {open ? <ChevronUp className='w-4 h-4' /> : <ChevronDown className='w-4 h-4' />}
                            </div>
                        )}
                        {isTeam && (
                            <div className='flex justify-end'>
                                <Checkbox
                                    className='rounded-full w-5 h-5 border-grayThree'
                                    id={name || '' }
                                    checked={isSelected}
                                    onCheckedChange={() => setIsSelected(!isSelected)}
                                />
                            </div>
                        )}
                        {(isDepartment || isTeam) && <EmployeeAvatars count={9} />}
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className=''>
            <div
                className='cursor-pointer'
                onClick={handleCardClick}
            >
                {renderCardContent()}
            </div>

            {open && hasChildren && (
                <div className='relative ml-6'>
                    <div className='absolute left-0 top-0 h-[calc(100%-52px)] border-l-[0.5px] border-grayTwo' />
                    <div className='space-y-4 pl-6 pt-4 relative'>
                        {children.map((child, index) => (
                            <div className='relative' key={index}>
                                <div className='absolute -left-6 top-12 h-0 w-6 border-t-[0.5px] border-grayTwo' />
                                <TreeCardView {...child} depth={depth + 1} />
                            </div>
                        ))}
                    </div>
                </div>
            )}
        </div>
    );
};

export default TreeCardView;


