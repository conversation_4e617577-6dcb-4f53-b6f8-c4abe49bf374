import React from 'react';
import Link from 'next/link';

const CheckEmail = () => {
    return (
        <div className='flex flex-col items-center gap-8 w-full'>
            <div className='flex flex-col items-center gap-[26px] w-full'>
                <Link
                    href={' '}
                    className='font-semibold text-base text-center w-full bg-primaryColor rounded-[50px] py-2.5 hover:bg-tertiary text-white'
                >
                    Open Email
                </Link>
                <Link 
                    href={' '}
                    className='font-normal text-sm leading-[18px] text-grayFive'
                >
                    Skip, I’ll confirm later
                </Link>
            </div>
            <div className='flex justify-center gap-1'>
                <span className='text-sm font-normal text-grayFive'>
                    Did not receive the email?
                </span>
                <Link
                    className='text-sm font-semibold text-primaryColor'
                    href={'/login'}
                >
                    Resend
                </Link>
            </div>
        </div>
    );
};

export default CheckEmail;
