import { HorizontalIconChartProps } from '@/types';
import Image from 'next/image';
import Link from 'next/link';
import React from 'react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

const HorizontalIconChart: React.FC<HorizontalIconChartProps> = ({
    title,
    viewAllLink,
    items,
}) => {
    return (
        <div className="flex flex-col p-7 gap-8">
            <div className="flex items-center justify-between">
                <h2 className="flex items-center gap-2.5 md:text-xl text-base md:leading-[29px] leading-5 text-grayFive font-bold">
                    {title}
                </h2>
                {viewAllLink && (
                    <Link
                        href={viewAllLink}
                        className="text-secondaryColor font-medium text-sm py-2 px-3.5 bg-primaryOne rounded-full w-fit"
                    >
                        View All
                    </Link>
                )}
            </div>

            <div className="flex flex-col gap-5">
                {items.map((item, index) => (
                    <div key={index} className="flex items-center gap-3">
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger>
                                    <Image
                                        src={item.img}
                                        alt={`Item ${index}`}
                                        width={30}
                                        height={30}
                                    />
                                </TooltipTrigger>
                                <TooltipContent className='bg-secondaryColor'>
                                    {item.label}
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                        <div className="relative w-full h-6 bg-primaryOne rounded-full overflow-hidden my-3">
                            <div
                                className="absolute h-full bg-primaryColor text-white text-sm text-end flex justify-end items-center px-2 rounded-full"
                                style={{ width: `${item.value}%` }}
                            >
                                {item.value}
                            </div>
                        </div>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default HorizontalIconChart;
