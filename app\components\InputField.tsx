import React from 'react';
import { InputFieldProps} from '@/types';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';

const InputField: React.FC<InputFieldProps> = ({ 
    id, 
    label, 
    value, 
    onChange,
    switcher, 
    register,
    className,
    errorMessage,
    type = 'text',
    placeholder = '',
    required = false,
    disabled = false,
}) => {
    return (
        <>
        {label && (
            <div className='flex gap-5 pb-1.5 items-center'>
                <Label 
                    htmlFor={id} 
                    className='font-medium text-sm text-grayFive'
                >
                    {label}
                    {required && <span className='text-red-500'>*</span>}
                </Label>
                {switcher && (
                    <div className='flex items-center gap-5'>
                        <p className='font-medium text-xs text-tertiary'>*Same as present address</p>
                        <Switch id='airplane-mode' />
                    </div>
                )}
            </div>
        )}
        <Input 
            id={id}
            type={type} 
            disabled={disabled}
            placeholder={placeholder} 
            value={value}
            {...(register || {})}
            className={`w-full placeholder:font-normal placeholder:text-base placeholder:text-grayTwo border border-tertiary border-opacity-20 focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 ${className} py-2.5 px-3.5 rounded-lg
            ${disabled && 'bg-gray-100 cursor-not-allowed'}`}
            onChange={onChange}
        />
        {errorMessage && (
            <p className='text-xs text-red-500'>{errorMessage}</p>
        )}
        </>
    )
}

export default InputField