import React from 'react';

interface CircularProgressProps {
    value: number; 
    size?: number; 
    strokeWidth?: number;
  }

const CircularProgressBar: React.FC<CircularProgressProps> = ({
    value,
    size = 50,
    strokeWidth = 2,
}) => {
    const radius = (size - strokeWidth) / 2;
    const circumference = 2 * Math.PI * radius;
    const progressOffset = circumference - (value / 100) * circumference;

    const dynamicProgressStrock = () => {
        if(value < 50) {
            return '#FF3B30'
        }else if(value < 75 && value > 50) {
            return '#F2A735'
        }else {
            return '#12B76A'
        }
    }
    
    const dynamicStrockColor = () => {
        if(value < 50) {
            return '#FFEBEA'
        }else if(value < 75 && value > 50) {
            return '#FEF3CC'
        }else {
            return '#4CAF501A'
        }
    }

    return (
        <svg width={size} height={size} viewBox={`0 0 ${size} ${size}`}>
            <circle
                cx={size / 2}
                cy={size / 2}
                r={radius}
                stroke={dynamicStrockColor()}
                strokeWidth={strokeWidth}
                fill="none"
            />
            <circle
                cx={size / 2}
                cy={size / 2}
                r={radius}
                stroke={dynamicProgressStrock()}
                strokeWidth={strokeWidth}
                fill="none"
                strokeDasharray={circumference}
                strokeDashoffset={progressOffset}
                strokeLinecap="round"
                transform={`rotate(-90 ${size / 2} ${size / 2})`}
            />
            <text
                x="50%"
                y="50%"
                dominantBaseline="middle"
                textAnchor="middle"
                className="text-[8px] font-semibold fill-graySix"
            >
                {value}%
            </text>
        </svg>
    )
}

export default CircularProgressBar