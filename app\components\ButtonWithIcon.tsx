import React from 'react';
import { ButtonWithIconProps } from '@/types';

const ButtonWithIcon: React.FC<ButtonWithIconProps> = ({ 
    icon, 
    label, 
    className
})=> {
    return (
        <div>
            <button className={`${className} flex items-center gap-2 font-medium text-sm leading-5 py-2.5 px-4`}>
                <div>
                   {icon}
                </div>
                {label}
            </button>
        </div>
    )
}

export default ButtonWithIcon