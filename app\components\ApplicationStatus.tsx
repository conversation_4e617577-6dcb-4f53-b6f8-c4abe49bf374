import { AllApplicationsTableProps } from '@/types'

const ApplicationStatus: React.FC<AllApplicationsTableProps> = ({ status }) => {
    const getStatusColor = (status: string) => {
        switch (status) {
            case 'New':
                return 'text-graySix border-[#B0BEC533] bg-[#B0BEC51A]';
            case 'In Progress':
                return 'text-[#42A5F5] border-[#42A5F533] bg-[#42A5F51A]';
            case 'Awaiting Documents':
                return 'text-[#A56200] border-[#FFB74D33] bg-[#FFB74D1A]';
            case 'Submitted':
                return 'text-[#66BB6A] border-[#66BB6A33] bg-[#66BB6A1A]';
            case 'Accepted':
                return 'text-[#388E3C] border-[#388E3C33] bg-[#388E3C1A]';
            case 'Deferred':
                return 'text-[#AB47BC] border-[#AB47BC33] bg-[#AB47BC1A]';
            case 'Rejected':
                return 'text-[#FF3B30] border-[#FF3B3033] bg-[#FF52521A]';
            case 'Withdrawn':
                return 'text-graySix border-[#CFD8DC33] bg-[#CFD8DC1A]';
            case 'Enrolled':
                return 'text-[#26A69A] border-[#26A69A33] bg-[#26A69A1A]';
            case 'Conditional Offer':
                return 'text-[#695E00] border-[#FFEB3B33] bg-[#FFEB3B1A]';
            case 'Payment Pending':
                return 'text-[#946F00] border-[#FFC10733] bg-[#FFC1071A]';
            case 'Transferred':
                return 'text-[#29B6F6] border-[#29B6F633] bg-[#29B6F61A]';
            case 'Success':
                return 'text-[#388E3C] border-[#388E3C33] bg-[#388E3C1A]';
            case 'Pending':
                return 'text-[#A56200] border-[#FFB74D33] bg-[#FFB74D1A]';
            case 'Discontinue':
                return 'text-[#9FA0A6] border-[#DEDEE0] bg-[#EFF0F0]';
            case 'Paid':
                return 'text-[#388E3C] border-[#388E3C33] bg-[#388E3C1A]';
            case 'Unpaid':
                return 'text-[#FF3B30] border-[#FF3B3033] bg-[#FF52521A]';
            case 'Due':
                return 'text-[#A56200] border-[#FFB74D33] bg-[#FFB74D1A]';
            case 'Completed':
                return 'text-[#1E62E0] border-[#1E62E033] bg-[#1E62E01A]';
            case 'Active':
                return 'text-[#26A69A] border-[#26A69A33] bg-[#26A69A1A]';
            case 'Graduated':
                return 'text-[#388E3C] border-[#388E3C33] bg-[#388E3C1A]';
            case 'On Hold':
                return 'text-[#946F00] border-[#FFC10733] bg-[#FFC1071A]';
            case 'Withdrawn':
                return 'text-[#9FA0A6] border-[#DEDEE0] bg-[#EFF0F0]';
            case 'On Leave':
                return 'text-[#A56200] border-[#FFB74D33] bg-[#FFB74D1A]';
            case 'Inactive':
                return 'text-[#FF3B30] border-[#FF3B3033] bg-[#FF52521A]';
            case 'Approved':
                return 'text-[#388E3C] border-[#388E3C33] bg-[#388E3C1A]';
            case 'Good':
                return 'text-[#388E3C] border-[#388E3C33] bg-[#388E3C1A]';
            case 'Average':
                return 'text-[#A56200] border-[#FFB74D33] bg-[#FFB74D1A]';
            case 'Weak':
                return 'text-[#FF3B30] border-[#FF3B3033] bg-[#FF52521A]';
            case 'High':
                return 'text-[#FF3B30] border-[#FF3B3033] bg-[#FF52521A]';
            case 'Medium':
                return 'text-[#A56200] border-[#FFB74D33] bg-[#FFB74D1A]';
            case 'Low':
                return 'text-[#9FA0A6] border-[#DEDEE0] bg-[#EFF0F0]';
            case 'Received':
                return 'text-[#42A5F5] border-[#42A5F533] bg-[#42A5F51A]';
            case 'Forwarded':
                return 'text-[#388E3C] border-[#388E3C33] bg-[#388E3C1A]';
            case 'Cancelled':
                return 'text-[#FF3B30] border-[#FF3B3033] bg-[#FF52521A]';
            default:
                return '';
        }
    };
    return (
        <div
            className={`text-xs leading-[18px] w-fit font-medium py-1 px-2.5 rounded-2xl border ${getStatusColor(status)}`}
        >
            {status}
        </div>
    );
};

export default ApplicationStatus;
