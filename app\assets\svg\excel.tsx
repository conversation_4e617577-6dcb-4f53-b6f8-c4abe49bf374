import React from 'react'

const excel = () => {
  return (
    <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
        <g clipPath="url(#clip0_2560_37034)">
        <path d="M15.5078 3.96188L12.0686 0.540039H4.49472C3.97006 0.540039 3.53906 0.941852 3.53906 1.46648V6.36023C3.53906 6.35929 3.57103 6.35254 3.59228 6.35254H4.00781V1.46648C4.00781 1.20457 4.23281 1.00879 4.49472 1.00879H11.8567L12.0078 1.15051V3.07529C12.0078 3.59776 12.4327 4.00879 12.9552 4.00879H14.8799L15.0078 4.15863V15.0822C15.0078 15.3441 14.8259 15.5713 14.564 15.5713H4.49472C4.23281 15.5713 4.00781 15.344 4.00781 15.0822V13.1963H3.59228C3.57103 13.1963 3.53906 13.1974 3.53906 13.1965V15.0821C3.53906 15.6068 3.97006 16.04 4.49472 16.04H14.564C15.0886 16.04 15.5078 15.6068 15.5078 15.0822V3.96188ZM12.9552 3.54004C12.6932 3.54004 12.4766 3.3372 12.4766 3.07529V1.67229V1.6632L14.3673 3.54004H12.9552Z" fill="#398861"/>
        <path d="M12.5579 6.82129H3.20775C2.68313 6.82129 2.25781 7.2466 2.25781 7.77123V11.7464C2.25781 12.271 2.68313 12.6963 3.20775 12.6963H12.5579C13.0825 12.6963 13.5078 12.271 13.5078 11.7464V7.77123C13.5078 7.2466 13.0825 6.82129 12.5579 6.82129ZM6.00925 11.3213L5.42222 10.38L4.84134 11.3213H3.92062L4.91962 9.78629L3.98038 8.32129H4.88256L5.42634 9.25232L5.94953 8.32129H6.8785L5.92481 9.85013L6.95056 11.3213H6.00925ZM9.25781 11.3213H7.28906V8.32129H8.10156V10.665H9.25781V11.3213ZM10.5233 9.27638C10.5844 9.32307 10.7502 9.4082 11.0207 9.53179C11.2803 9.64851 11.4605 9.77382 11.5614 9.9077C11.6624 10.0416 11.7128 10.2102 11.7128 10.4134C11.7128 10.6002 11.6654 10.7659 11.5707 10.9108C11.476 11.0557 11.3393 11.1683 11.1608 11.2486C10.9823 11.329 10.7729 11.3691 10.5326 11.3691C10.3321 11.3691 10.1628 11.355 10.0269 11.3269C9.89094 11.2988 9.75781 11.2496 9.60156 11.1796V10.4546C9.75781 10.5342 9.91909 10.5964 10.0866 10.641C10.2541 10.6856 10.4085 10.7079 10.5485 10.7079C10.6693 10.7079 10.7582 10.687 10.8145 10.6451C10.8708 10.6032 10.8991 10.5494 10.8991 10.4834C10.8991 10.4422 10.8878 10.4062 10.8651 10.3753C10.8425 10.3444 10.8061 10.3132 10.756 10.2815C10.7059 10.25 10.5724 10.1854 10.3554 10.0879C10.159 9.99869 10.0117 9.91219 9.91359 9.82838C9.81537 9.74463 9.74262 9.64851 9.69525 9.54001C9.64787 9.43154 9.62422 9.30313 9.62422 9.15482C9.62422 8.87748 9.72516 8.6612 9.927 8.50598C10.1289 8.35082 10.4062 8.27323 10.7592 8.27323C11.0709 8.27323 11.3888 8.34532 11.7129 8.48948L11.4636 9.11773C11.1821 8.98866 10.9391 8.9241 10.7344 8.9241C10.6287 8.9241 10.5518 8.94263 10.5037 8.9797C10.4557 9.01679 10.4317 9.06279 10.4317 9.11773C10.4316 9.17685 10.4622 9.22969 10.5233 9.27638Z" fill="#398861"/>
        </g>
        <defs>
        <clipPath id="clip0_2560_37034">
        <rect width="16" height="16" fill="white" transform="translate(0.882812 0.290039)"/>
        </clipPath>
        </defs>
    </svg>
  )
}

export default excel