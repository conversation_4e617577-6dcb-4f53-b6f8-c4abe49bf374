import React from 'react';
import { ActivitiesSectionProps } from '@/types';
import ActivitiesLogCard from './ActivitiesLogCard';

const ActivitiesSection: React.FC<ActivitiesSectionProps> = ({ 
    section 
}) => {
    return (
        <div className="space-y-6 md:mb-14 mb-10 md:px-[30px] px-0">
            <div className="flex items-center gap-4 py-2.5">
                <h2 className="text-xl leading-6 text-grayFive font-semibold">
                    {section.date}
                </h2>
                <span className="text-grayFour text-xs leading-[15px] py-1 px-2.5 bg-grayOne rounded-full">
                    {section.count} 
                </span>
            </div>
            <div className="space-y-6">
                {section.entries.map((entry, index) => (
                    <ActivitiesLogCard key={index} entry={entry} />
                ))}
            </div>
        </div>
    );
};

export default ActivitiesSection;
