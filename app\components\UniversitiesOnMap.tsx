import React from 'react';
import WorldMap from '../assets/svg/WorldMap';
import Location from '../assets/svg/Location';
import { Progress } from "@/components/ui/progress"

const UniversitiesOnMap = () => {
    const countryLists = [
        {
            name: 'New zealand',
            coordinate: 'bottom-[10%] right-[5.5%]',
            university: 36
        },
        {
            name: 'Australia',
            coordinate: 'bottom-[18%] right-[17%]',
            university: 65
        },
        {
            name: 'malaysia',
            coordinate: 'bottom-[32%] right-[25.5%]',
            university: 80
        },
        {
            name: 'Bangladesh',
            coordinate: 'bottom-[39%] right-[25%]',
            university: 90
        },
        {
            name: 'China',
            coordinate: 'bottom-[46%] right-[29%]',
            university: 76
        },
        {
            name: 'Cyprus',
            coordinate: 'bottom-[44.5%] right-[42.55%]',
            university: 20
        },
        {
            name: 'Finland',
            coordinate: 'left-[46%] top-[28%]',
            university: 90
        },
        {
            name: '<PERSON><PERSON>',
            coordinate: 'left-[15%] bottom-[46%]',
            university: 50
        },
        {
            name: 'Canada',
            coordinate: 'left-[14%] top-[35%]',
            university: 30
        }
    ];

    return (
        <div className='bg-white p-7 rounded-[20px] drop-shadow-[0_1px_4px_rgba(0,0,0,0.0.5)]'>
            <h3 className='font-bold text-xl leading-none text-grayFive'>Universities On Map</h3>
            
            <div className='mt-10 flex justify-center items-center gap-[180px] md:px-[200px]'>
                <div className='w-2/3 relative'>
                    <WorldMap />
                    {countryLists.map((country, index) => (
                        <div key={index} className={`absolute ${country.coordinate}`}>
                            <div className='flex gap-1 items-center'>
                                <Location />
                                <span className='font-bold text-xs leading-none text-graySix uppercase'>{country.name}</span>
                            </div>
                        </div>
                    ))}
                </div>
                <div className='w-1/3 space-y-5'>
                    {countryLists.slice(0,5).map((country, index) => (
                        <div key={index} className='space-y-1.5'>
                            <span className='font-medium text-base leading-none text-graySix capitalize'>{country.name}</span>
                            <div className='flex gap-3.5 items-center'>
                                <Progress className='bg-primaryOne' value={country.university} />
                                <span className='font-semibold text-base leading-none text-graySix'>{country.university}</span>
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    )
}

export default UniversitiesOnMap