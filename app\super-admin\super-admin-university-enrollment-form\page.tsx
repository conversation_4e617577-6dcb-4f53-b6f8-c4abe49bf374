'use client';

import React, { useState, FormEvent } from 'react';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import Heading from '@/app/components/Heading';
import EditButton from '@/app/components/EditButton';
import VerticalTabLayout from '@/app/components/VerticalTabLayout';
import InputField from '@/app/components/InputField';
import InputFieldWithIcon from '@/app/components/InputFieldWithIcon';
import EmailInbox from '@/app/assets/svg/emailInbox';
import { DatePicker } from '@/app/components/DatePicker';
import { CheckboxGroup } from '@/app/components/CheckboxGroup';
import SelectField from '@/app/components/SelectField';
import PhoneNumberInput from '@/app/components/PhoneNumberInput';
import MultiSelect from '@/app/components/MultiSelect';
import SelectedItem from '@/app/components/SelectedItem';
import InputfieldWithButton from '@/app/components/InputfieldWithButton';
import NoteTextarea from '@/app/components/NoteTextarea';
import { useF<PERSON>, SubmitHandler, Controller  } from 'react-hook-form';
import { Country, State, City } from 'country-state-city';
import { 
    subjects, 
    maritalStatus, 
    gender, 
    frameworks, 
    personalInfo, 
    socialPlatform,
    Reference,
    prefferedCountry,
    socialLinks,
    UniTypeSelectButton
} from '@/common';
import SectionLayout from '@/app/components/layout/SectionLayout';
import FormSubSection from '@/app/components/FormSubSection';
import AddNewFieldButton from '@/app/components/AddNewFieldButton';
import InputFieldWithCurrency from '@/app/components/InputFieldWithCurrency';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import ImageUploader from '@/app/components/ImageUploader';

interface Step {
    id: number;
    value: string;
}

type FormData = {
    present_country: string;
    present_state: string;
    permanent_country: string;
    permanent_state: string;
    prefferedCountry: string[];
    preferred_subject: string[];
    selectedPlatform: string;
    dob: Date | undefined; // Updated dob field
    gender: string[]; // Add gender field
    marital_status: string; // Add marital_status field
    phoneNumber: string; // Add phoneNumber field
    gurdian_phone_number: string; // Add gurdian_phone_number field
    // Add other fields as needed
};

const page = () => {
    const [nextId, setNextId] = useState(2); // Track next available ID
    const [steps, setSteps] = useState<Step[]>([
        { id: 1, value: '' }
    ]);
    const [fee, setFee] = useState<string>('');
    // const [selectedCurrency, setSelectedCurrency] = useState({
    //     code: 'USD',
    //     symbol: '$',
    //     label: 'USD ($)'
    // });
    const [applicationFeeCurrency, setApplicationFeeCurrency] = useState({
            code: 'USD',
            symbol: '$',
            label: 'USD ($)'
        });
const [tuitionFeeCurrency, setTuitionFeeCurrency] =useState({
    code: 'USD',
    symbol: '$',
    label: 'USD ($)'
});
const [livingCostCurrency, setLivingCostCurrency] = useState({
    code: 'USD',
    symbol: '$',
    label: 'USD ($)'
});


    // Add these state variables to your component
const [scholarshipOption, setScholarshipOption] = useState<string>("");
const [financialAidOption, setFinancialAidOption] = useState<string>("");
const [workStudyOption, setWorkStudyOption] = useState<string>("");
const [paymentPlanOption, setPaymentPlanOption] = useState<string>("");
const [enrollmentOption, setEnrollmentOption] = useState<string>("");

    // const handleCurrencyChange = (currency) => {
    //     console.log('Currency selected:', currency);
    //     setSelectedCurrency(currency);
    //   };

    // Modified addStep function
    const addStep = () => {
        setSteps(prevSteps => [...prevSteps, { 
            id: nextId, 
            value: '' 
        }]);
        setNextId(prev => prev + 1); // Increment next available ID
    };

    // Modified removeStep function
    const removeStep = (idToRemove: number) => {
        setSteps(prevSteps => {
            const updatedSteps = prevSteps.filter(step => step.id !== idToRemove);
            // If all steps are removed, add one empty step
            if (updatedSteps.length === 0) {
                return [{ id: 1, value: '' }];
            }
            return updatedSteps;
        });
    };

    // Modified updateStepValue function
    const updateStepValue = (id: number, newValue: string) => {
        setSteps(prevSteps => prevSteps.map(step => 
            step.id === id ? { ...step, value: newValue } : step
        ));
    };

    const handleSubmit = (e: FormEvent) => {
        e.preventDefault();
        // Handle your form submission here
        console.log('Form submitted:', steps);
    };

    const general = () => {
        const countriesList = Country.getAllCountries();
        const [states, setStates] = useState(State.getStatesOfCountry(countriesList[0].isoCode));
        const [logo, setLogo] = useState<File | null>(null);
        const [universityImages, setUniversityImages] = useState<File[]>([]);
        const [agrrementImages, setAgreementImages] = useState<File[]>([]);
        const { 
                register, 
                control, 
                getValues, 
                setValue, 
                watch, 
                handleSubmit, 
                formState: { errors } 
            } = useForm<FormData>({
                defaultValues: {
                    present_country: countriesList[0].isoCode,
                    present_state: states[0]?.isoCode,
                    permanent_country: countriesList[0].isoCode,
                    permanent_state: states[0]?.isoCode,
                    prefferedCountry: [],
                    preferred_subject: [],
                    // socialLinks: [],
                    selectedPlatform: socialPlatform[0].value,
                },
            });
        return (
            <SectionLayout className='p-[30px]' heading={'University Info'}>
                <form 
                    onSubmit={handleSubmit((data) => console.log(data))}
                    className='space-y-[30px]'
                >
                    <FormSubSection heading={'University Info'}>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <InputField
                                id="university_name"
                                placeholder="Enter the university name"
                                type="text"
                                label="Name"
                            />
                            <InputField
                                id="university_id"
                                placeholder="Enter the university id"
                                type="text"
                                label="ID"
                            />
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className='max-h-[137px]'>
                                <NoteTextarea 
                                // register={register('note')} 
                                />
                            </div>
                            <div className='grid grid-cols-2 gap-6'>
                                <SelectField
                                    label="Type"
                                    options={UniTypeSelectButton}
                                />
                                <InputField
                                    id="website_url"
                                    placeholder="Website link paste here"
                                    type="text"
                                    label="Website URL (if available)"
                                />
                                <InputField
                                    id="founded_on"
                                    placeholder="Enter the founding year"
                                    type="text"
                                    label="Founded On"
                                />
                                <InputField
                                    id="dli_number"
                                    placeholder="Enter the DLI number"
                                    type="text"
                                    label="DLI Number"
                                />
                            </div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className='max-h-[137px]'>
                                <NoteTextarea 
                                // register={register('note')} 
                                />
                            </div>
                            <div className='grid grid-cols-2 gap-6'>
                                <SelectField
                                    label="Country"
                                    options={UniTypeSelectButton}
                                />
                                <SelectField
                                    label="State"
                                    options={UniTypeSelectButton}
                                />
                                <SelectField
                                    label="City"
                                    options={UniTypeSelectButton}
                                />
                                <InputField
                                    id="dli_number"
                                    placeholder="Enter the Postal/Zip Code"
                                    type="text"
                                    label="Postal/Zip Code"
                                />
                            </div>
                        </div>
                    </FormSubSection>
                    <FormSubSection heading={'Campuses'}>
                        <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                            <div className="grid grid-cols-1 gap-6">
                                <InputField
                                    id="campus_name"
                                    placeholder="Enter the campus name"
                                    type="text"
                                    label="Campus Name"
                                />
                                <InputField
                                    id="addresses"
                                    placeholder="Enter addresses"
                                    type="text"
                                    label="Address"
                                />
                            </div>
                            <div className='grid grid-cols-2 gap-6'>
                                    <SelectField
                                        label="Country"
                                        options={UniTypeSelectButton}
                                    />
                                    <SelectField
                                        label="State"
                                        options={UniTypeSelectButton}
                                    />
                                    <SelectField
                                        label="City"
                                        options={UniTypeSelectButton}
                                    />
                                    <InputField
                                        id="dli_number"
                                        placeholder="Enter the Postal/Zip Code"
                                        type="text"
                                        label="Postal/Zip Code"
                                    />
                            </div>
                        </div>
                        <AddNewFieldButton 
                            onClick={() => console.log('Add New Field')} 
                        />
                    </FormSubSection>
                    <FormSubSection heading={'Contact Information'}>
                        <div className="grid grid-cols-2 gap-6">
                            <PhoneNumberInput
                                label="Phone Number"
                                className="border border-tertiary border-opacity-20 focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 py-2.5 mt-0.5 px-3.5 rounded-lg w-full"
                            />
                            <InputFieldWithIcon
                                label="Email"
                                placeholder="Email"
                                icon={<EmailInbox />}
                                type="email"
                            />
                        </div>
                    </FormSubSection>
                    <FormSubSection heading={'Nationality Requirements'}>
                        <div className="grid grid-cols-2 gap-6">
                            <SelectField
                                label="Eligible Countries"
                                options={UniTypeSelectButton}
                            />
                            <SelectField
                                label="Non-Eligible Countries"
                                options={UniTypeSelectButton}
                            />
                        </div>
                    </FormSubSection>
                    <FormSubSection heading={'Application Steps'}>
                        <div className="space-y-[30px]">
                            <span className=''>Multiple Steps</span>
                            {steps.map((step, index) => (
                                <div key={step.id} className="flex items-center ">
                                    <div className="bg-primaryThree px-3.5 py-2.5 rounded-l-lg border border-primaryColor/20">
                                        <span className="text-primaryColor font-medium">
                                            Step {String(index+1).padStart(2, '0')}
                                        </span>
                                    </div>
                                    <input
                                        id={`step_${step.id}`}
                                        placeholder={`Enter ${index + 1}${getOrdinalSuffix(index + 1)} step`}
                                        type="text"
                                        // value={step.value}
                                        // onChange={(e) =>{
                                        //     e.preventDefault();
                                        //     updateStepValue(step.id, e.target.value);
                                        // }}
                                        className='rounded-r-lg border px-3.5 py-2.5 border-primaryColor/20 outline-none'
                                    />
                                    {steps.length > 1 && (
                                        <button 
                                            type="button" // Prevent form submission
                                            className="text-[#FF3B30] rounded-full bg-[#FF3B30]/10 ml-5"
                                            onClick={(e) => {
                                                e.preventDefault();
                                                removeStep(step.id);
                                            }}
                                        >
                                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <path d="M18 6L6 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                                <path d="M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            </svg>
                                        </button>
                                    )}
                                </div>
                            ))}
                        </div>
                        <div className="mt-6">
                            <button
                                className='border border-primaryColor/20 text-grayFive px-3.5 py-2.5 rounded-full'
                                type="button" // Prevent form submission
                                onClick={(e) => {
                                    e.preventDefault();
                                    addStep();
                                }} 
                            >
                                Add Step
                            </button>
                        </div>
                    </FormSubSection>

                    <FormSubSection heading={'Application Fee Details'}>
                        <div className="grid grid-cols-2 gap-6">
                            <InputField
                                id="application_fee"
                                placeholder="Enter the minimum application criteria"
                                type="text"
                                label="Minimum Application Criteria"
                            />
                            <InputFieldWithCurrency
                                value={fee}
                                onChange={(value) => setFee(String(value))}
                                required
                                selectedCurrency={applicationFeeCurrency}
                                onCurrencyChange={setApplicationFeeCurrency}
                                label="Application Fee"
                            />
                            {/* <div className="mt-4 p-2 bg-gray-100 rounded">
                                Current currency: {selectedCurrency.code} ({selectedCurrency.symbol})
                            </div> */}
                        </div>
                        <div className='grid grid-cols-4 gap-6'>
                            <InputFieldWithCurrency
                                value={fee}
                                onChange={(value) => setFee(String(value))}
                                required
                                selectedCurrency={tuitionFeeCurrency}
                                onCurrencyChange={setTuitionFeeCurrency}
                                label="Avg. Tuition Fee"
                            />
                            <InputFieldWithCurrency
                                value={fee}
                                onChange={(value) => setFee(String(value))}
                                required
                                selectedCurrency={livingCostCurrency}
                                onCurrencyChange={setLivingCostCurrency}
                                label="Cost of Living"
                            />
                            <SelectField
                                label="Avg. Undergraduate Program"
                                options={UniTypeSelectButton}
                            />
                            <SelectField
                                label="Avg. Graduate Program"
                                options={UniTypeSelectButton}
                            />
                        </div>
                        <div className='grid grid-cols-4 gap-6'>
                            <SelectField
                                label="Discount Type"
                                options={UniTypeSelectButton}
                            />
                            <InputField
                                id="discount_value"
                                placeholder="Enter the minimum discount value"
                                type="text"
                                label="Discount Value"
                            />
                            <DatePicker
                            title='Start Date'
                            />
                            <DatePicker
                            title='End Date'
                            />
                        </div>
                        <div className='grid grid-cols-2 gap-6'>
                            <InputField
                                id="promo_code"
                                placeholder="Enter the promo code"
                                type="text"
                                label="Promo Code"
                            />
                            <InputField
                                id="maximum_redemptions_allowed"
                                placeholder="Enter the Maximum Redemptions Allowed"
                                type="text"
                                label="Maximum Redemptions Allowed"
                            />
                        </div>
                    </FormSubSection>
                    <FormSubSection heading={'Typical Timeframe for Receiving an Acceptance Letter'}>
                        <div className="grid grid-cols-3 gap-6">
                            <SelectField
                                label="Semester 01"
                                options={UniTypeSelectButton}
                            />
                            <SelectField
                                label="Semester 02"
                                options={UniTypeSelectButton}
                            />
                            <SelectField
                                label="Semester 03"
                                options={UniTypeSelectButton}
                            />
                            <SelectField
                                label="Duration"
                                options={UniTypeSelectButton}
                            />
                            <SelectField
                                label="Duration"
                                options={UniTypeSelectButton}
                            />
                            <SelectField
                                label="Duration"
                                options={UniTypeSelectButton}
                            />
                        </div>
                    </FormSubSection>
                    <FormSubSection heading={'Tuition Financial Aid & Scholarships'}>
                        <div className='grid grid-cols-4 gap-6'>
                            <div className='flex flex-col gap-2'>
                                <label className="font-medium text-sm text-grayFive">Tuition Fee Discount</label>
                                <RadioGroup value={scholarshipOption} onValueChange={setScholarshipOption}>
                                    <div className='flex gap-6'>
                                        <div className='flex items-center gap-2'>
                                            <RadioGroupItem value="scholarship_yes" id="scholarship_yes" />
                                            <label htmlFor="scholarship_yes" className="text-sm text-grayFive">Yes</label>
                                        </div>
                                        <div className='flex items-center gap-2'>
                                            <RadioGroupItem value="scholarship_no" id="scholarship_no" />
                                            <label htmlFor="scholarship_no" className="text-sm text-grayFive">No</label>
                                        </div>
                                    </div>
                                </RadioGroup>
                            </div>
                            
                            <div className='flex flex-col gap-2'>
                                <label className="font-medium text-sm text-grayFive">Financial Aid Acceptance</label>
                                <RadioGroup value={financialAidOption} onValueChange={setFinancialAidOption}>
                                    <div className='flex gap-6'>
                                        <div className='flex items-center gap-2'>
                                            <RadioGroupItem value="financial_aid_yes" id="financial_aid_yes" />
                                            <label htmlFor="financial_aid_yes" className="text-sm text-grayFive">Yes</label>
                                        </div>
                                        <div className='flex items-center gap-2'>
                                            <RadioGroupItem value="financial_aid_no" id="financial_aid_no" />
                                            <label htmlFor="financial_aid_no" className="text-sm text-grayFive">No</label>
                                        </div>
                                    </div>
                                </RadioGroup>
                            </div>
                            
                            <div className='flex flex-col gap-2'>
                                <label className="font-medium text-sm text-grayFive">Scholarship Opportunity</label>
                                <RadioGroup value={workStudyOption} onValueChange={setWorkStudyOption}>
                                    <div className='flex gap-6'>
                                        <div className='flex items-center gap-2'>
                                            <RadioGroupItem value="work_study_yes" id="work_study_yes" />
                                            <label htmlFor="work_study_yes" className="text-sm text-grayFive">Yes</label>
                                        </div>
                                        <div className='flex items-center gap-2'>
                                            <RadioGroupItem value="work_study_no" id="work_study_no" />
                                            <label htmlFor="work_study_no" className="text-sm text-grayFive">No</label>
                                        </div>
                                    </div>
                                </RadioGroup>
                            </div>
                            
                            <div className='flex flex-col gap-2'>
                                <label className="font-medium text-sm text-grayFive">Accommodation Status</label>
                                <RadioGroup value={paymentPlanOption} onValueChange={setPaymentPlanOption}>
                                    <div className='flex gap-6'>
                                        <div className='flex items-center gap-2'>
                                            <RadioGroupItem value="payment_plan_yes" id="payment_plan_yes" />
                                            <label htmlFor="payment_plan_yes" className="text-sm text-grayFive">Yes</label>
                                        </div>
                                        <div className='flex items-center gap-2'>
                                            <RadioGroupItem value="payment_plan_no" id="payment_plan_no" />
                                            <label htmlFor="payment_plan_no" className="text-sm text-grayFive">No</label>
                                        </div>
                                    </div>
                                </RadioGroup>
                            </div>
                        </div>
                        <div className='grid grid-cols-2 gap-6'>
                            <ImageUploader
                                label="University Logo"
                                multiple={false}
                                value={logo}
                                onChange={(file) => setLogo(file as File | null)}
                            />
                            <ImageUploader
                                label="University Image Collection"
                                multiple={true}
                                maxFiles={5}
                                value={universityImages}
                                onChange={(files) => setUniversityImages(files as File[])}
                            />
                        </div>
                    </FormSubSection>
                    <FormSubSection heading={'Status and Agreements'}>
                        <div className='grid grid-cols-2 gap-6'>
                            <ImageUploader
                                label="Agreement Document"
                                multiple={true}
                                maxFiles={5}
                                value={agrrementImages}
                                onChange={(files) => setAgreementImages(files as File[])}
                            />
                            <div className='flex flex-col gap-2'>
                                <label className="font-medium text-sm text-grayFive">Active Enrollment</label>
                                <RadioGroup value={enrollmentOption} onValueChange={setEnrollmentOption}>
                                    <div className='flex gap-10 py-3'>
                                        <div className='flex items-center gap-4'>
                                            <RadioGroupItem value="enrollment_yes" id="enrollment_yes" />
                                            <label htmlFor="enrollment_yes" className="text-sm text-grayFive">Yes</label>
                                        </div>
                                        <div className='flex items-center gap-4'>
                                            <RadioGroupItem value="enrollment_no" id="enrollment_no" />
                                            <label htmlFor="enrollment_no" className="text-sm text-grayFive">No</label>
                                        </div>
                                    </div>
                                </RadioGroup>
                            </div>
                        </div>
                    </FormSubSection>
                    <FormSubSection heading='Banner'>
                        {steps.map((step, index) => (
                            <div key={step.id} className="flex items-center ">
                                <div className="bg-primaryThree px-3.5 py-2.5 rounded-l-lg border border-primaryColor/20">
                                    <span className="text-primaryColor font-medium">
                                        Step {String(index+1).padStart(2, '0')}
                                    </span>
                                </div>
                                <input
                                    id={`step_${step.id}`}
                                    placeholder={`Enter ${index + 1}${getOrdinalSuffix(index + 1)} step`}
                                    type="text"
                                    // value={step.value}
                                    // onChange={(e) =>{
                                    //     e.preventDefault();
                                    //     updateStepValue(step.id, e.target.value);
                                    // }}
                                    className='rounded-r-lg border px-3.5 py-2.5 border-primaryColor/20 outline-none'
                                />
                                {steps.length > 1 && (
                                    <button 
                                        type="button" // Prevent form submission
                                        className="text-[#FF3B30] rounded-full bg-[#FF3B30]/10 ml-5"
                                        onClick={(e) => {
                                            e.preventDefault();
                                            removeStep(step.id);
                                        }}
                                    >
                                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M18 6L6 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                            <path d="M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                                        </svg>
                                    </button>
                                )}
                            </div>
                        ))}
                        <div className="mt-6">
                            <button
                                className='border border-primaryColor/20 text-grayFive px-3.5 py-2.5 rounded-full'
                                type="button" // Prevent form submission
                                onClick={(e) => {
                                    e.preventDefault();
                                    addStep();
                                }} 
                            >
                                Add Banner
                            </button>
                        </div>
                    </FormSubSection>
                    <button type="button" >Click</button>
                </form>
            </SectionLayout>
        );
    };
    const course = () => {
        return (
            <SectionLayout className='p-[30px]' heading={'Course Info'}>
                <div className="py-5">course</div>
            </SectionLayout>
        );
    };
    const alumni = () => {
        return <div className="py-5">alumni</div>;
    };
    const commission = () => {
        return <div className="py-5">commission</div>;
    };

    const tabContentsUniEnrollForm = [
        {
            value: 'general',
            label: 'General',
            content: general,
        },
        {
            value: 'course',
            label: 'Course',
            content: course,
        },
        {
            value: 'alumni',
            label: 'Alumni',
            content: alumni,
        },
        {
            value: 'commission',
            label: 'Commission',
            content: commission,
        },
    ];

    const tabContents = tabContentsUniEnrollForm.map((tab) => ({
        value: tab.value,
        children: <tab.content />,
    }));
    return (
        <DashboardLayout>
            <div className="flex justify-between pt-10 pb-5">
                <div>
                    <Heading level="h1">University Enrollment Form</Heading>
                </div>
            </div>
            <div>
                <VerticalTabLayout
                    tabMenu={tabContentsUniEnrollForm}
                    tabContents={tabContents}
                    defaultValue="general"
                    tabListClassName="!p-0 max-w-[1193px] md:!justify-start"
                    tabContentClassName="pb-[96px]"
                    tabTriggerClassName=""
                />
            </div>
        </DashboardLayout>
    );
};

// Helper function for ordinal suffixes
const getOrdinalSuffix = (num: number): string => {
    const j = num % 10;
    const k = num % 100;
    if (j === 1 && k !== 11) return "st";
    if (j === 2 && k !== 12) return "nd";
    if (j === 3 && k !== 13) return "rd";
    return "th";
};

export default page;
