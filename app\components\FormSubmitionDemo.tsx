'use client';

import React from 'react';
import { useForm } from 'react-hook-form';
import { useMutation, useQueryClient  } from '@tanstack/react-query';

type PostInput = {
    title: string;
    body: string;
};


const createPost = async (data: PostInput) => {
    const response = await fetch('https://jsonplaceholder.typicode.com/posts', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
  
    if (!response.ok) {
      throw new Error('Failed to create post');
    }
  
    return response.json();
};

const FormSubmitionDemo = () => {
    const queryClient = useQueryClient();
    const { register, handleSubmit, reset } = useForm<PostInput>();
    
    const { mutate, isPending, isSuccess, error } = useMutation({
        mutationFn: createPost,
        onSuccess: (newPost) => {
            queryClient.setQueryData(['posts'], (oldData: any[]) => {
              const lastId = oldData?.[oldData.length - 1]?.id || 100;
              const newId = lastId + 1;
          
              const modifiedPost = {
                ...newPost,
                id: newId,
              };
          
              return oldData ? [...oldData, modifiedPost] : [modifiedPost];
            });
          
            reset();
          }
    });

    const onSubmit = (data: PostInput) => {
        mutate(data);
    };
    
    return (
        <div>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <div>
                    <label className="block text-sm font-medium mb-1">Title</label>
                    <input
                        type="text"
                        {...register('title', { required: true })}
                        className="w-full p-2 border rounded"
                        placeholder="Enter title"
                    />
                </div>

                <div>
                    <label className="block text-sm font-medium mb-1">Body</label>
                    <textarea
                        {...register('body', { required: true })}
                        className="w-full p-2 border rounded"
                        placeholder="Enter body"
                    />
                </div>

                <button
                    type="submit"
                    disabled={isPending}
                    className="bg-blue-500 text-white px-4 py-2 rounded disabled:opacity-50"
                >
                    {isPending ? 'Submitting...' : 'Submit'}
                </button>
            </form>
            {isSuccess && (
                <p className="mt-4 text-green-600">✅ Post submitted successfully!</p>
            )}
            {error && (
                <p className="mt-4 text-red-500">❌ {(error as Error).message}</p>
            )}
        </div>
    )
}

export default FormSubmitionDemo