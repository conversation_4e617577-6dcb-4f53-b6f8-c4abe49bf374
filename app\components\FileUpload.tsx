import React, { useState } from 'react';
import Close from '../assets/svg/Close';

export default function FileUpload() {
    const [selectedFile, setSelectedFile] = useState<File | null>(null);

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setSelectedFile(file);
        }
    };

    const handleRemoveFile = () => {
        setSelectedFile(null);
    };

    return (
        <div className='w-full space-y-2'>
            <div className='flex items-center gap-2.5 border border-[#1952BB33] rounded-lg px-5 py-[18px]'>
                <label className='border border-primaryColor text-primaryColor font-medium text-xs py-0.5 px-2 rounded cursor-pointer'>
                    Choose File
                    <input
                        type='file'
                        className='hidden'
                        onChange={handleFileChange}
                        accept='image/*'
                    />
                </label>

                {selectedFile && (
                    <div className='flex items-center bg-primaryOne text-secondaryColor rounded-full px-3 text-sm'>
                        {selectedFile.name}
                        <button
                            type='button'
                            onClick={handleRemoveFile}
                            className='ml-2 hover:text-red-500'
                        >
                            <Close />
                        </button>
                    </div>
                )}
            </div>
        </div>
    );
}
