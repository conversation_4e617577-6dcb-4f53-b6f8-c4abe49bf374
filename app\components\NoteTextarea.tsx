import React from 'react';
import { TextareaProps } from '@/types';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

const NoteTextarea: React.FC<TextareaProps> = ({
    register
}) => {
    return (
        <div className='h-full'>
            <Label className='font-medium text-sm text-grayFive leading-5'>Note</Label>
            <Textarea 
                {...(register || {})}
                rows={4} 
                className='placeholder:font-normal placeholder:text-sm 
                placeholder:leading-6 placeholder:text-grayTwo rounded-lg 
                mt-1.5 p-4 w-full border 
                border-tertiary border-opacity-20 
                focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 h-full' 
                placeholder='Enter a Note...' 
                
            />
        </div>
    )
}

export default NoteTextarea