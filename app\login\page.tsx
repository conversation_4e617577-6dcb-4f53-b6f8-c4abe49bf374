import React from 'react';
import { auth } from '@/lib/auth';
import LoginForm from '../components/LoginForm';
import LoginCover from '../assets/svg/LoginCover';
import AuthLayout from '../components/layout/AuthLayout';

const page = async () => {
    const session = await auth();
    console.log(session?.user.email)
    return (
        <>
        <AuthLayout
            imageSrc={<LoginCover />}
            description='Every login is a step closer to your goals - stay driven, stay focused, and make it count!'
            title='Log in to your account'
            heading='Welcome back! Please enter your details.'
        >
            <div className='pt-8 max-w-[440px] mx-auto'>
                <LoginForm />
            </div>
        </AuthLayout>
        </>
    )
}

export default page