import React from 'react'

const WhatCircle = () => {
    return (
        <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_648_3441" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="25" height="25">
                <rect x="0.873047" y="0.102539" width="24" height="24" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_648_3441)">
                <path d="M12.823 18.1025C13.173 18.1025 13.4689 17.9817 13.7105 17.74C13.9522 17.4984 14.073 17.2025 14.073 16.8525C14.073 16.5025 13.9522 16.2067 13.7105 15.965C13.4689 15.7234 13.173 15.6025 12.823 15.6025C12.473 15.6025 12.1772 15.7234 11.9355 15.965C11.6939 16.2067 11.573 16.5025 11.573 16.8525C11.573 17.2025 11.6939 17.4984 11.9355 17.74C12.1772 17.9817 12.473 18.1025 12.823 18.1025ZM12.873 22.1025C11.4897 22.1025 10.1897 21.84 8.97305 21.315C7.75638 20.79 6.69805 20.0775 5.79805 19.1775C4.89805 18.2775 4.18555 17.2192 3.66055 16.0025C3.13555 14.7859 2.87305 13.4859 2.87305 12.1025C2.87305 10.7192 3.13555 9.41921 3.66055 8.20254C4.18555 6.98587 4.89805 5.92754 5.79805 5.02754C6.69805 4.12754 7.75638 3.41504 8.97305 2.89004C10.1897 2.36504 11.4897 2.10254 12.873 2.10254C14.2564 2.10254 15.5564 2.36504 16.773 2.89004C17.9897 3.41504 19.048 4.12754 19.948 5.02754C20.848 5.92754 21.5605 6.98587 22.0855 8.20254C22.6105 9.41921 22.873 10.7192 22.873 12.1025C22.873 13.4859 22.6105 14.7859 22.0855 16.0025C21.5605 17.2192 20.848 18.2775 19.948 19.1775C19.048 20.0775 17.9897 20.79 16.773 21.315C15.5564 21.84 14.2564 22.1025 12.873 22.1025ZM12.873 20.1025C15.1064 20.1025 16.998 19.3275 18.548 17.7775C20.098 16.2275 20.873 14.3359 20.873 12.1025C20.873 9.86921 20.098 7.97754 18.548 6.42754C16.998 4.87754 15.1064 4.10254 12.873 4.10254C10.6397 4.10254 8.74805 4.87754 7.19805 6.42754C5.64805 7.97754 4.87305 9.86921 4.87305 12.1025C4.87305 14.3359 5.64805 16.2275 7.19805 17.7775C8.74805 19.3275 10.6397 20.1025 12.873 20.1025ZM12.973 7.80254C13.3897 7.80254 13.7522 7.93587 14.0605 8.20254C14.3689 8.46921 14.523 8.80254 14.523 9.20254C14.523 9.56921 14.4105 9.89421 14.1855 10.1775C13.9605 10.4609 13.7064 10.7275 13.423 10.9775C13.0397 11.3109 12.7022 11.6775 12.4105 12.0775C12.1189 12.4775 11.973 12.9275 11.973 13.4275C11.973 13.6609 12.0605 13.8567 12.2355 14.015C12.4105 14.1734 12.6147 14.2525 12.848 14.2525C13.098 14.2525 13.3105 14.1692 13.4855 14.0025C13.6605 13.8359 13.773 13.6275 13.823 13.3775C13.8897 13.0275 14.0397 12.715 14.273 12.44C14.5064 12.165 14.7564 11.9025 15.023 11.6525C15.4064 11.2859 15.7355 10.8859 16.0105 10.4525C16.2855 10.0192 16.423 9.53587 16.423 9.00254C16.423 8.15254 16.0772 7.45671 15.3855 6.91504C14.6939 6.37337 13.8897 6.10254 12.973 6.10254C12.3397 6.10254 11.7355 6.23587 11.1605 6.50254C10.5855 6.76921 10.148 7.17754 9.84805 7.72754C9.73138 7.92754 9.69388 8.14004 9.73555 8.36504C9.77721 8.59004 9.88971 8.76087 10.073 8.87754C10.3064 9.01087 10.548 9.05254 10.798 9.00254C11.048 8.95254 11.2564 8.81087 11.423 8.57754C11.6064 8.32754 11.8355 8.13587 12.1105 8.00254C12.3855 7.86921 12.673 7.80254 12.973 7.80254Z" fill="#1E62E0" />
            </g>
        </svg>
    )
}

export default WhatCircle