'use client';

import { z } from 'zod';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import Visibility from '@/app/assets/svg/visibility';
import { zodResolver } from '@hookform/resolvers/zod';
import VisibilityOff from '@/app/assets/svg/visibility_off';
import {
    Form,
    FormControl,
    FormDescription,
    FormField,
    FormItem,
    FormLabel,
    FormMessage,
} from '@/components/ui/form';

const formSchema = z
    .object({
        password: z
            .string()
            .min(8, { message: 'Password must be at least 8 characters.' }),
        confirmPassword: z.string(),
    })
    .refine((data) => data.password === data.confirmPassword, {
        message: 'Passwords must match.',
        path: ['confirmPassword'],
    });

const CreateNewPassword = () => {
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);

    const togglePasswordVisibility = () => setShowPassword((prev) => !prev);
    const toggleConfirmPasswordVisibility = () =>
        setShowConfirmPassword((prev) => !prev);

    const form = useForm<z.infer<typeof formSchema>>({
        resolver: zodResolver(formSchema),
        defaultValues: {
            password: '',
            confirmPassword: '',
        },
    });

    function onSubmit(values: z.infer<typeof formSchema>) {
        // console.log(values);
        return values;
    }

  return (
    <div>
        <Form {...form}>
                <form
                    onSubmit={form.handleSubmit(onSubmit)}
                    className='space-y-5'
                >

                    <FormField
                        control={form.control}
                        name='password'
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel className='text-sm font-medium text-graySix'>
                                    Password
                                </FormLabel>
                                <FormControl>
                                    <div className="relative">
                                        <input
                                            type={
                                                showPassword ? 'text' : 'password'
                                            }
                                            className="flex w-full rounded-[50px] outline-none focus:border-opacity-60 border focus-within:outline-none focus-within:ring-1 focus-within:ring-opacity-60 border-tertiary border-opacity-20 py-2.5 px-4 placeholder:text-grayThree shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]"
                                            placeholder="Enter your password"
                                            {...field}
                                        />
                                        <button
                                            type="button"
                                            onClick={togglePasswordVisibility}
                                            className="absolute right-3 top-1/2 transform -translate-y-1/2"
                                        >
                                            { showPassword ? (<VisibilityOff />) : (<Visibility className='text-grayThree' />)}
                                        </button>
                                    </div>
                                </FormControl>
                                <FormDescription>
                                    Must be at least 8 characters.
                                </FormDescription>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name='confirmPassword'
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel className='text-sm font-medium text-graySix'>
                                    Confirm Password
                                </FormLabel>
                                <FormControl>
                                <div className='relative'>
                                        <input
                                            type={
                                                showConfirmPassword
                                                    ? 'text'
                                                    : 'password'
                                            }
                                            className='flex w-full rounded-[50px] outline-none focus:border-opacity-60 border focus-within:outline-none focus-within:ring-1 focus-within:ring-opacity-60 border-tertiary border-opacity-20 py-2.5 px-4 placeholder:text-grayThree shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]'
                                            placeholder='Confirm your password'
                                            {...field}
                                        />
                                        <button
                                            type='button'
                                            onClick={toggleConfirmPasswordVisibility}
                                            className='absolute right-3 top-1/2 transform -translate-y-1/2'
                                        >
                                            { showConfirmPassword ? (<VisibilityOff />) : (<Visibility className='text-grayThree' />)}
                                        </button>
                                    </div>
                                </FormControl>
                                <FormDescription>
                                    Both passwords must match.
                                </FormDescription>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <Button
                        type='submit'
                        className='w-full bg-primaryColor rounded-[50px] py-2.5 mt-6 mb-5 hover:bg-tertiary text-white text-base font-semibold'
                    >
                        Reset Password
                    </Button>
                    
                </form>
        </Form>
    </div>
  )
}

export default CreateNewPassword;
