import Image from 'next/image';
import Heading from './Heading';
import { frameworks } from '@/common';
// import { FormData } from '@/types';
import InputField from './InputField';
import React, { useState } from 'react';
import { DatePicker } from './DatePicker';
import NoteTextarea from './NoteTextarea';
import Upload from '@/app/assets/svg/upload';
import { Switch } from '@/components/ui/switch';
import Error from '@/app/assets/svg/error.svg';
import SectionLayout from './layout/SectionLayout';
import AddNewFieldButton from './AddNewFieldButton';
import { useForm, Controller } from 'react-hook-form';
import SelectAndSearchCombobox from './SelectAndSearchCombobox';
import {
    HoverCard,
    HoverCardContent,
    HoverCardTrigger,
} from '@/components/ui/hover-card';

 interface FormData {
    institute: string;
    permanent_country: string;
    academic: {
        subject: string;
        board: string;
        gpa: string;
        year: string;
    }[];
    proficiency: {
        exam: string;
        score: string;
        reading: string;
        writing: string;
        listening: string;
        speaking: string;
    }[];
    activities: {
        field: string;
        link: string;
    }[];
  }
  
const Education = () => {
    const [isTextVisible, setIsTextVisible] = useState(false);
    const [fileName, setFileName] = useState('');
    const [inputValue, setInputValue] = useState('');
    const [academicFields, setAcademicFields] = useState<number[]>([0]);
    const [proficiencyFields, setProficiencyFields] = useState<number[]>([0]);
    const [publicationsFields, setPublicationsFields] = useState<number[]>([0]);
    const [activitiesFields, setActivitiesFields] = useState<number[]>([0]);

    const { control } = useForm<FormData>();

    const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files[0]) {
        setFileName(e.target.files[0].name);
        setInputValue('');
        }
    };

    const toggleTextVisibility = () => {
        setIsTextVisible((prev) => !prev);
    };

    const frameworks = [
        { value: 'next.js', label: 'Next.js' },
        { value: 'sveltekit', label: 'SvelteKit' },
        { value: 'nuxt.js', label: 'Nuxt.js' },
        { value: 'remix', label: 'Remix' },
        { value: 'astro', label: 'Astro' },
    ];

    const renderSelectField = <T extends keyof FormData>(label: string, name: T) => (
        <Controller
            name={name}
            control={control}
            render={({ field }) => (
                <SelectAndSearchCombobox
                    options={frameworks}
                    label={label}
                    type='select'
                    // field={field}
                />
            )}
        />
    );
  

    return (
        <SectionLayout heading='Educational Form'>
            <div>
                <div className='flex flex-col md:flex-row gap-3 items-start md:items-center'>
                    <Heading level='h3'>
                        Academic
                    </Heading>
                    <div className='flex gap-3 items-center'>
                        <Switch onClick={toggleTextVisibility} id='airplane-mode' />
                        <p className='font-normal text-xs md:text-sm leading-5 text-[#4B5563] text-opacity-70'>
                            If you have a foreign degree, please activate the button.
                        </p>
                        <HoverCard>
                            <HoverCardTrigger>
                                <Image width={16} height={16} src={Error} alt='Error icon' />
                            </HoverCardTrigger>
                        <HoverCardContent className='w-[290px] font-medium text-xs text-white -mt-11 ml-6 rounded-lg bg-grayFour' align='start'>
                            If you have a foreign degree, please specify the country where
                            you earned your degree in the dropdown below.
                        </HoverCardContent>
                        </HoverCard>
                    </div>
                </div>

                {isTextVisible && (
                    <div className='grid grid-cols-1 md:grid-cols-2 mb-6 gap-6 mt-[30px]'>
                        {renderSelectField('Country', 'permanent_country')}
                    </div>
                )}

                 {academicFields.map((index) => (
                    <div key={index} className='grid grid-cols-1 md:grid-cols-2 gap-6 mt-[30px]'>
                        <Controller
                            name='permanent_country'
                            control={control}
                            render={({ field }) => (
                                <SelectAndSearchCombobox
                                    options={frameworks}
                                    label='Institute'
                                    type='select'
                                    // field={field}
                                />
                            )}
                        />
                        <Controller
                            name='permanent_country'
                            control={control}
                            render={({ field }) => (
                                <SelectAndSearchCombobox
                                    options={frameworks}
                                    label='Group/ Subject'
                                    type='select'
                                    // field={field}
                                />
                            )}
                        />
                        <Controller
                            name='permanent_country'
                            control={control}
                            render={({ field }) => (
                                <SelectAndSearchCombobox
                                    options={frameworks}
                                    label='Board/ University'
                                    type='select'
                                    // field={field}
                                />
                            )}
                        />
                        <Controller
                            name='permanent_country'
                            control={control}
                            render={({ field }) => (
                                <SelectAndSearchCombobox
                                    options={frameworks}
                                    label='GPA/ CGPA/ Division'
                                    type='select'
                                    // field={field}
                                />
                            )}
                        />
                        <Controller
                            name='permanent_country'
                            control={control}
                            render={({ field }) => (
                                <SelectAndSearchCombobox
                                    options={frameworks}
                                    label='Passing Year'
                                    type='select'
                                    // field={field}
                                />
                            )}
                        />
                        
                    </div>
                 ))}
                
                <AddNewFieldButton onClick={() => setAcademicFields((prev) => [...prev, prev.length])} />
            </div>
            <div>
                <h3 className='pt-14 font-semibold text-xl leading-6 tracking-[0.4px] text-graySix'>Proficiency</h3>
                {proficiencyFields.map((index) => (
                    <div key={index} >
                        <div  className='grid grid-cols-1 md:grid-cols-2 gap-6 pt-[30px]'>
                            <Controller
                                name='permanent_country'
                                control={control}
                                render={({ field }) => (
                                    <SelectAndSearchCombobox
                                        options={frameworks}
                                        label='Name of Exam'
                                        type='select'
                                        // field={field}
                                    />
                                )}
                            />
                            <div>
                                <InputField 
                                    id='score'
                                    // value={''}
                                    // onChange={(e) => setName(e.target.value)}
                                    placeholder='Overall 0.0' 
                                    className='placeholder:text-center'
                                    type='text' 
                                    // errorMessage={'!name.trim() && error'}
                                    label='Score' 
                                />
                                <div className='grid grid-cols-4 gap-3 mt-2.5'>
                                    <InputField 
                                        id='reading'
                                        // value={''}
                                        // onChange={(e) => setName(e.target.value)}
                                        placeholder='R 0.0' 
                                        className='placeholder:text-center'
                                        type='text' 
                                        // errorMessage={'!name.trim() && error'}
                                    />
                                    <InputField 
                                        id='writing'
                                        // value={''}
                                        // onChange={(e) => setName(e.target.value)}
                                        className='placeholder:text-center'
                                        placeholder='W 0.0' 
                                        type='text' 
                                        // errorMessage={'!name.trim() && error'}
                                    />
                                    <InputField 
                                        id='listining'
                                        // value={''}
                                        // onChange={(e) => setName(e.target.value)}
                                        className='placeholder:text-center'
                                        placeholder='L 0.0' 
                                        type='text' 
                                        // errorMessage={'!name.trim() && error'}
                                    />
                                    <InputField 
                                        id='speaking'
                                        // value={''}
                                        // onChange={(e) => setName(e.target.value)}
                                        className='placeholder:text-center'
                                        placeholder='S 0.0' 
                                        type='text' 
                                        // errorMessage={'!name.trim() && error'}
                                    />
                                </div>
                            </div>
                            <DatePicker />
                            <DatePicker />
                        </div>
                        <div className='mt-[30px]'>
                            <NoteTextarea />
                        </div>
                    </div>
                ))}
                <AddNewFieldButton onClick={() => setProficiencyFields((prev) => [...prev, prev.length])} />
            </div>
            <div>
                <h3 className='pt-14 font-semibold text-xl leading-6 tracking-[0.4px] text-graySix'>Publications</h3>
                {publicationsFields.map((index) => (
                    <div key={index} className='grid grid-cols-1 md:grid-cols-2 gap-6 mt-[30px]'>
                        <div>
                            <InputField 
                                id='subject'
                                // value={''}
                                // onChange={(e) => setName(e.target.value)}
                                type='text' 
                                // errorMessage={'!name.trim() && error'}
                                label='Subject/ Field' 
                            />
                        </div>
                        <div>
                            <InputField 
                                id='journal'
                                // value={''}
                                // onChange={(e) => setName(e.target.value)}
                                type='text' 
                                // errorMessage={'!name.trim() && error'}
                                label='Journal' 
                            />
                        </div>
                        <DatePicker />
                        <div>
                            <InputField 
                                id='link'
                                // value={''}
                                // onChange={(e) => setName(e.target.value)}
                                type='text' 
                                // errorMessage={'!name.trim() && error'}
                                label='Link' 
                            />
                        </div>
                    </div>
                ))}
                <AddNewFieldButton onClick={() => setPublicationsFields((prev) => [...prev, prev.length])} />
            </div>
            
            <div>
                <h3 className='pt-14 font-semibold text-xl leading-6 tracking-[0.4px] text-graySix'>Other Activities</h3>
                {activitiesFields.map((index) => (
                    <div key={index} className='grid grid-cols-1 md:grid-cols-2 gap-6 mt-[30px]'>
                        <Controller
                            name='permanent_country'
                            control={control}
                            render={({ field }) => (
                                <SelectAndSearchCombobox
                                    options={frameworks}
                                    label='Subject/ Field'
                                    type='select'
                                    // field={field}
                                />
                            )}
                        />
                        <div className='flex gap-3 items-center'>
                            <div className='md:w-[80%] w-full'>
                                <InputField 
                                    id='link'
                                    // value={inputValue || fileName}
                                    // onChange={handleInputChange}
                                    type='text' 
                                    // errorMessage={'!name.trim() && error'}
                                    label='Link' 
                                    placeholder='Link here'
                                    disabled={!!fileName}
                                />
                            </div>
                            <div className='md:w-[20%] w-full mt-6'>
                                <label
                                    className={`flex gap-2 relative items-center cursor-pointer rounded-lg bg-primaryOne justify-center py-3 text-sm font-semibold text-primaryColor shadow-[0_1px_2px_0_rgba(16, 24, 40, 0.05)] ${
                                    inputValue ? 'opacity-50 cursor-not-allowed' : ''
                                    }`}
                                >
                                    {/* <Image src={Upload} alt='Upload icon' /> */}
                                    <Upload className='w-[18px] h-[18px]'/>
                                    <span>Upload</span>
                                    <input
                                        type='file'
                                        className='sr-only'
                                        disabled={!!inputValue} 
                                        onChange={handleFileUpload}
                                    />
                                </label>
                            </div>
                        </div>
                        <DatePicker />
                        <DatePicker />
                    </div>
                ))}
            </div>
            
            <AddNewFieldButton onClick={() => setActivitiesFields((prev) => [...prev, prev.length])} />
        </SectionLayout>
       
    )
}


//                 {academicFields.map((_, index) => (
//                     <div 
//                         key={`academic-field-${index}`} 
//                         className='grid grid-cols-1 md:grid-cols-2 gap-6 mt-[30px]'
//                     >
//                         {renderSelectField('Institute', `academic.${index}.subject` as keyof FormData)}
//                         {renderSelectField('Group/Subject', `academic.${index}.subject` as keyof FormData)}
//                         {renderSelectField('Board/University', `academic.${index}.board` as keyof FormData)}
//                         {renderSelectField('GPA/CGPA/Division', `academic.${index}.gpa` as keyof FormData)}
//                         {renderSelectField('Passing Year', `academic.${index}.year` as keyof FormData)}
//                     </div>
//                 ))}

//                 <AddNewFieldButton onClick={() => setAcademicFields((prev) => [...prev, prev.length])} />
//             </div>

//             <div className='pt-14'>
//                 <Heading level='h3'>
//                     Proficiency
//                 </Heading>
//                 {proficiencyFields.map((_, index) => (
//                     <div 
//                         key={`proficiency-field-${index}`} 
//                         className='grid grid-cols-1 md:grid-cols-2 gap-6 pt-[30px]'
//                     >
//                         {renderSelectField('Name of Exam', `proficiency_${index}_exam` as keyof FormData)}
//                         <div>
//                             <InputField id={`proficiency_${index}_score`} placeholder='Overall 0.0' type='text' label='Score' />
//                             <div className='grid grid-cols-2 md:grid-cols-4 gap-3'>
//                                 {['Reading', 'Writing', 'Listening', 'Speaking'].map((label) => (
//                                     <InputField
//                                         key={label}
//                                         id={`proficiency_${index}_${label.toLowerCase()}`}
//                                         placeholder={`${label.charAt(0)} 0.0`}
//                                         type='text'
//                                     />
//                                 ))}
//                             </div>
//                         </div>

//                         <DatePicker />
//                     </div>
//                 ))}
//                 <AddNewFieldButton onClick={() => setProficiencyFields((prev) => [...prev, prev.length])} />
//             </div>

//         <div className='pt-14'>
//             <Heading level='h3'>
//                 Publications
//             </Heading>
//             {publicationsFields.map((_, index) => (
//                 <div key={`publications-field-${index}`} className='grid grid-cols-1 md:grid-cols-2 gap-6 mt-[30px]'>
//                     <InputField 
//                         id={`publication_${index}_subject`} 
//                         type='text' 
//                         label='Subject/Field' 
//                     />
//                     <InputField 
//                         id={`publication_${index}_journal`} 
//                         type='text' 
//                         label='Journal' 
//                     />
//                     <DatePicker />
//                     <InputField 
//                         id={`publication_${index}_link`} 
//                         type='text' 
//                         label='Link' 
//                     />
//                 </div>
//             ))}
//             <AddNewFieldButton onClick={() => setPublicationsFields((prev) => [...prev, prev.length])} />
//         </div>

//         <div className='pt-14'>
//             <Heading level='h3'>
//                 Other Activities
//             </Heading>
//             {activitiesFields.map((_, index) => (
//                 <div key={`activities-field-${index}`} className='grid grid-cols-1 md:grid-cols-2 gap-6 mt-[30px]'>
//                     {renderSelectField('Subject/Field', `activity_${index}_field` as keyof FormData)}
//                     <div className='flex gap-3 items-center'>
//                         <div className='md:w-[80%] w-full'>
//                             <InputField
//                                 id={`activity_${index}_link`}
//                                 placeholder='Link here'
//                                 type='text'
//                                 disabled={!!fileName}
//                             />
//                         </div>
//                         <div className='md:w-[20%] w-full mt-6'>
//                             <label
//                                 className={`flex gap-2 relative items-center cursor-pointer rounded-lg bg-primaryOne justify-center py-3 text-sm font-semibold text-primaryColor ${
//                                     inputValue ? 'opacity-50 cursor-not-allowed' : ''
//                                 }`}
//                             >
//                                 <Upload className='w-[18px] h-[18px]' />
//                                 <span>Upload</span>
//                                 <input 
//                                     type='file' 
//                                     className='sr-only' 
//                                     disabled={!!inputValue} 
//                                     onChange={handleFileUpload} 
//                                 />
//                             </label>
//                         </div>
//                     </div>
//                     <DatePicker />
//                 </div>
//             ))}
//             <AddNewFieldButton onClick={() => setActivitiesFields((prev) => [...prev, prev.length])} />
//         </div>
//         </SectionLayout>
//     );
// };

export default Education;
