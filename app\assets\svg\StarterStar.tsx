import React from 'react'

const StarterStar = () => {
  return (
    <svg width="70" height="71" viewBox="0 0 70 71" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clipPath="url(#clip0_1684_5380)">
<path d="M21.9844 63.6094C21.5469 64.4844 20.4531 64.8125 19.5781 64.2656C-1.09377 51.4687 -0.875025 20.4062 20.125 7.93749C20.6719 7.60937 21.4375 7.82812 21.7656 8.37499C22.0937 8.92187 21.875 9.57812 21.3281 9.90624C1.74998 21.6094 1.96873 49.6094 21.3281 61.4219C22.0937 61.8594 22.3125 62.8437 21.9844 63.6094Z" fill="#85A8E6"/>
<path d="M21.2187 15.1562C19.6875 16.3594 18.0469 17.0156 16.4062 17.3437C14.7656 17.6719 13.2344 17.4531 12.0312 16.5781C14.7656 12.9687 19.4687 12.6406 21.2187 15.1562Z" fill="#E4E9FB"/>
<path d="M14.5469 6.40625C12.3594 8.15625 10.9375 10.3437 10.6094 12.2031C10.1719 14.1719 10.7187 15.7031 11.9219 16.6875C14.5469 13.1875 16.2969 8.92187 14.5469 6.40625Z" fill="#E4E9FB"/>
<path d="M15.9688 20.4063C14.875 21.8281 13.5625 23.0313 12.1406 23.6875C10.7188 24.3438 9.07813 24.6719 7.76562 24.125C9.29687 19.8594 13.5625 18.5469 15.9688 20.4063Z" fill="#E4E9FB"/>
<path d="M7.10938 13.8438C5.57813 15.9219 4.92187 18.3281 5.14062 20.1875C5.35937 22.0469 6.23438 23.4688 7.65625 24.0156C9.1875 20.1875 9.51563 15.8125 7.10938 13.8438Z" fill="#E4E9FB"/>
<path d="M12.5781 26.6406C11.9219 28.2813 10.9375 29.7031 9.73437 30.6875C8.53125 31.6719 7.10937 32.4375 5.57812 32.2188C6.125 27.8438 9.73438 25.4375 12.5781 26.6406Z" fill="#E4E9FB"/>
<path d="M2.29689 22.7031C1.42189 25.1094 1.42189 27.5156 2.07814 29.2656C2.73439 31.0156 4.04689 32.1094 5.57813 32.2188C6.01563 28.0625 5.14063 23.9063 2.29689 22.7031Z" fill="#E4E9FB"/>
<path d="M10.9375 33.4219C10.7187 35.1719 10.1719 36.8125 9.29687 38.125C8.42187 39.4375 7.21875 40.4219 5.6875 40.6406C5.03125 36.2656 7.98437 32.9844 10.9375 33.4219Z" fill="#E4E9FB"/>
<path d="M-1.82762e-05 32.3281C-0.218768 34.8438 0.437482 37.1406 1.53123 38.6719C2.62498 40.2031 4.15623 40.8594 5.68748 40.6406C5.03123 36.4844 3.06248 32.7656 -1.82762e-05 32.3281Z" fill="#E4E9FB"/>
<path d="M11.1562 40.4219C11.4843 42.1719 11.2656 43.9219 10.8281 45.3438C10.2812 46.875 9.40621 48.1875 7.98434 48.8438C6.23434 44.6875 8.20309 40.75 11.1562 40.4219Z" fill="#E4E9FB"/>
<path d="M0.4375 42.1719C0.875 44.6875 2.07812 46.7656 3.60937 47.8594C5.14062 49.0625 6.78125 49.2812 8.09375 48.7344C6.34375 44.9062 3.39063 41.8437 0.4375 42.1719Z" fill="#E4E9FB"/>
<path d="M13.2343 47.0938C14 48.7344 14.3281 50.375 14.2187 52.0156C14.1093 53.6562 13.6718 55.1875 12.4687 56.1719C9.62497 52.5625 10.5 48.1875 13.2343 47.0938Z" fill="#E4E9FB"/>
<path d="M3.28125 51.6875C4.375 53.9844 6.125 55.7344 7.875 56.5C9.625 57.2656 11.375 57.1563 12.4687 56.1719C9.73437 52.7813 6.125 50.5938 3.28125 51.6875Z" fill="#E4E9FB"/>
<path d="M17.1718 53C18.375 54.4219 19.25 55.9531 19.6875 57.5938C20.125 59.2344 20.125 60.875 19.3593 62.0781C15.2031 59.4531 14.7656 54.8594 17.1718 53Z" fill="#E4E9FB"/>
<path d="M8.85938 60.2188C10.6094 62.1875 12.9063 63.3906 14.875 63.7188C16.8437 64.0469 18.4844 63.3906 19.3594 62.1875C15.4219 59.5625 11.2656 58.3594 8.85938 60.2188Z" fill="#E4E9FB"/>
<path d="M48.0156 63.6094C48.4531 64.4844 49.5469 64.8125 50.4219 64.2656C71.0937 51.4687 70.875 20.4062 49.875 7.93749C49.3281 7.60937 48.5625 7.82812 48.2344 8.37499C47.9062 8.92187 48.125 9.57812 48.6719 9.90624C68.25 21.6094 67.9219 49.5 48.5625 61.4219C47.9062 61.8594 47.6875 62.8437 48.0156 63.6094Z" fill="#85A8E6"/>
<path d="M48.7812 15.1562C50.3125 16.3594 51.9531 17.0156 53.5938 17.3437C55.2344 17.6719 56.7656 17.4531 57.9688 16.5781C55.2344 12.9687 50.5313 12.6406 48.7812 15.1562Z" fill="#E4E9FB"/>
<path d="M55.4531 6.40625C57.6406 8.15625 59.0625 10.3437 59.3906 12.2031C59.8281 14.1719 59.2812 15.7031 58.0781 16.6875C55.4531 13.1875 53.7031 8.92187 55.4531 6.40625Z" fill="#E4E9FB"/>
<path d="M54.0312 20.4063C55.125 21.8281 56.4375 23.0313 57.8594 23.6875C59.2812 24.3438 60.9219 24.6719 62.2344 24.125C60.7031 19.8594 56.4375 18.5469 54.0312 20.4063Z" fill="#E4E9FB"/>
<path d="M62.8906 13.8438C64.4219 15.9219 65.0781 18.3281 64.8594 20.1875C64.6406 22.0469 63.7656 23.4688 62.3437 24.0156C60.8125 20.1875 60.4844 15.8125 62.8906 13.8438Z" fill="#E4E9FB"/>
<path d="M57.4219 26.6406C58.0781 28.2813 59.0625 29.7031 60.2656 30.6875C61.4687 31.6719 62.8906 32.4375 64.4219 32.2188C63.875 27.8438 60.2656 25.4375 57.4219 26.6406Z" fill="#E4E9FB"/>
<path d="M67.7031 22.7031C68.5781 25.1094 68.5781 27.5156 67.9219 29.2656C67.2656 31.0156 65.9531 32.1094 64.4219 32.2188C63.9844 28.0625 64.8594 23.9063 67.7031 22.7031Z" fill="#E4E9FB"/>
<path d="M59.0625 33.4219C59.2813 35.1719 59.8281 36.8125 60.7031 38.125C61.5781 39.4375 62.7812 40.4219 64.3125 40.6406C64.9687 36.2656 62.0156 32.9844 59.0625 33.4219Z" fill="#E4E9FB"/>
<path d="M70 32.3281C70.2188 34.8438 69.5625 37.1406 68.4687 38.6719C67.375 40.2031 65.8437 40.8594 64.3125 40.6406C64.9688 36.4844 66.9375 32.7656 70 32.3281Z" fill="#E4E9FB"/>
<path d="M58.8437 40.4219C58.5156 42.1719 58.7343 43.9219 59.1718 45.3438C59.7187 46.875 60.5937 48.1875 62.0156 48.8438C63.7656 44.6875 61.7968 40.75 58.8437 40.4219Z" fill="#E4E9FB"/>
<path d="M69.5625 42.1719C69.125 44.6875 67.9219 46.7656 66.3906 47.8594C64.8594 49.0625 63.2188 49.2812 61.9062 48.7344C63.6563 44.9062 66.6094 41.8437 69.5625 42.1719Z" fill="#E4E9FB"/>
<path d="M56.7656 47.0938C56 48.7344 55.6719 50.375 55.7812 52.0156C55.8906 53.6562 56.3281 55.1875 57.5312 56.1719C60.375 52.5625 59.5 48.1875 56.7656 47.0938Z" fill="#E4E9FB"/>
<path d="M66.7188 51.6875C65.625 53.9844 63.875 55.7344 62.125 56.5C60.375 57.2656 58.625 57.1563 57.5312 56.1719C60.2656 52.7813 63.875 50.5938 66.7188 51.6875Z" fill="#E4E9FB"/>
<path d="M52.8281 53C51.625 54.4219 50.75 55.9531 50.3125 57.5938C49.875 59.2344 49.875 60.875 50.6406 62.0781C54.7968 59.4531 55.2343 54.8594 52.8281 53Z" fill="#E4E9FB"/>
<path d="M61.1406 60.2188C59.3906 62.1875 57.0938 63.3906 55.125 63.7188C53.1563 64.0469 51.5156 63.3906 50.6406 62.1875C54.5781 59.5625 58.7344 58.3594 61.1406 60.2188Z" fill="#E4E9FB"/>
<path d="M45.0625 40.75L47.3594 54.5313L35 48.0781L22.6406 54.5313L24.9375 40.75L14.9844 31.0156L28.7656 28.9375L35 16.4688L41.2344 28.9375L55.0156 31.0156L45.0625 40.75Z" fill="#E4E9FB"/>
<path d="M35 16.4688V35.8281L41.2344 28.9375L35 16.4688Z" fill="#85A8E6"/>
<path d="M55.0156 31.0156L45.0625 40.75L35 35.8281L55.0156 31.0156Z" fill="#85A8E6"/>
<path d="M14.9844 31.0156L24.9375 40.75L35 35.8281L14.9844 31.0156Z" fill="#85A8E6"/>
<path d="M35 35.8281V48.0781L47.3594 54.5313L35 35.8281Z" fill="#85A8E6"/>
<path d="M35 35.8281V48.0781L22.6406 54.5313L35 35.8281Z" fill="#85A8E6"/>
</g>
<defs>
<clipPath id="clip0_1684_5380">
<rect width="70" height="70" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>

  )
}

export default StarterStar;

