const distance = () => {
    return (
        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_3475_9294" style={{ maskType: 'alpha' }} maskUnits="userSpaceOnUse" x="0" y="0" width="12" height="12">
            <rect width="12" height="12" fill="currentColor"/>
            </mask>
            <g mask="url(#mask0_3475_9294)">
            <path d="M6 11C5.11667 11 4.39583 10.8604 3.8375 10.5813C3.27917 10.3021 3 9.94167 3 9.5C3 9.34167 3.03542 9.19583 3.10625 9.0625C3.17708 8.92917 3.28333 8.80417 3.425 8.6875C3.54167 8.60417 3.66875 8.57083 3.80625 8.5875C3.94375 8.60417 4.05417 8.67083 4.1375 8.7875C4.22083 8.90417 4.25208 9.03125 4.23125 9.16875C4.21042 9.30625 4.14167 9.41667 4.025 9.5C4.13333 9.63333 4.38333 9.75 4.775 9.85C5.16667 9.95 5.575 10 6 10C6.425 10 6.83333 9.95 7.225 9.85C7.61667 9.75 7.86667 9.63333 7.975 9.5C7.85833 9.41667 7.78958 9.30625 7.76875 9.16875C7.74792 9.03125 7.77917 8.90417 7.8625 8.7875C7.94583 8.67083 8.05625 8.60417 8.19375 8.5875C8.33125 8.57083 8.45833 8.60417 8.575 8.6875C8.71667 8.80417 8.82292 8.92917 8.89375 9.0625C8.96458 9.19583 9 9.34167 9 9.5C9 9.94167 8.72083 10.3021 8.1625 10.5813C7.60417 10.8604 6.88333 11 6 11ZM6.0125 8.25C6.8375 7.64167 7.45833 7.03125 7.875 6.41875C8.29167 5.80625 8.5 5.19167 8.5 4.575C8.5 3.725 8.22917 3.08333 7.6875 2.65C7.14583 2.21667 6.58333 2 6 2C5.41667 2 4.85417 2.21667 4.3125 2.65C3.77083 3.08333 3.5 3.725 3.5 4.575C3.5 5.13333 3.70417 5.71458 4.1125 6.31875C4.52083 6.92292 5.15417 7.56667 6.0125 8.25ZM6 9.2625C5.9 9.2625 5.8 9.24584 5.7 9.2125C5.6 9.17917 5.50833 9.12917 5.425 9.0625C4.44167 8.27917 3.70833 7.51458 3.225 6.76875C2.74167 6.02292 2.5 5.29167 2.5 4.575C2.5 3.98333 2.60625 3.46458 2.81875 3.01875C3.03125 2.57292 3.30417 2.2 3.6375 1.9C3.97083 1.6 4.34583 1.375 4.7625 1.225C5.17917 1.075 5.59167 1 6 1C6.40833 1 6.82083 1.075 7.2375 1.225C7.65417 1.375 8.02917 1.6 8.3625 1.9C8.69583 2.2 8.96875 2.57292 9.18125 3.01875C9.39375 3.46458 9.5 3.98333 9.5 4.575C9.5 5.29167 9.25833 6.02292 8.775 6.76875C8.29167 7.51458 7.55833 8.27917 6.575 9.0625C6.49167 9.12917 6.4 9.17917 6.3 9.2125C6.2 9.24584 6.1 9.2625 6 9.2625ZM6 5.5C6.275 5.5 6.51042 5.40208 6.70625 5.20625C6.90208 5.01042 7 4.775 7 4.5C7 4.225 6.90208 3.98958 6.70625 3.79375C6.51042 3.59792 6.275 3.5 6 3.5C5.725 3.5 5.48958 3.59792 5.29375 3.79375C5.09792 3.98958 5 4.225 5 4.5C5 4.775 5.09792 5.01042 5.29375 5.20625C5.48958 5.40208 5.725 5.5 6 5.5Z" fill="#7A7B82"/>
            </g>
        </svg>
    )
}

export default distance;
