import React from 'react'

const ExclamationRounded = () => {
  return (
    <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <mask id="mask0_7358_32128" style={{maskType:'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="32" height="32">
    <rect x="32" y="32" width="32" height="32" transform="rotate(180 32 32)" fill="#D9D9D9"/>
    </mask>
    <g mask="url(#mask0_7358_32128)">
    <path d="M16.0007 9.33301C15.6229 9.33301 15.3062 9.46078 15.0506 9.71634C14.7951 9.9719 14.6673 10.2886 14.6673 10.6663C14.6673 11.0441 14.7951 11.3608 15.0506 11.6163C15.3062 11.8719 15.6229 11.9997 16.0007 11.9997C16.3784 11.9997 16.6951 11.8719 16.9507 11.6163C17.2062 11.3608 17.334 11.0441 17.334 10.6663C17.334 10.2886 17.2062 9.9719 16.9507 9.71634C16.6951 9.46078 16.3784 9.33301 16.0007 9.33301ZM16.0007 14.6663C15.6229 14.6663 15.3062 14.7941 15.0506 15.0497C14.7951 15.3052 14.6673 15.6219 14.6673 15.9997V21.333C14.6673 21.7108 14.7951 22.0275 15.0506 22.283C15.3062 22.5386 15.6229 22.6663 16.0007 22.6663C16.3784 22.6663 16.6951 22.5386 16.9507 22.283C17.2062 22.0275 17.334 21.7108 17.334 21.333V15.9997C17.334 15.6219 17.2062 15.3052 16.9507 15.0497C16.6951 14.7941 16.3784 14.6663 16.0007 14.6663ZM16.0007 2.66634C17.8451 2.66634 19.5784 3.01634 21.2007 3.71634C22.8229 4.41634 24.234 5.36634 25.434 6.56634C26.634 7.76634 27.584 9.17745 28.284 10.7997C28.984 12.4219 29.334 14.1552 29.334 15.9997C29.334 17.8441 28.984 19.5775 28.284 21.1997C27.584 22.8219 26.634 24.233 25.434 25.433C24.234 26.633 22.8229 27.583 21.2007 28.283C19.5784 28.983 17.8451 29.333 16.0007 29.333C14.1562 29.333 12.4229 28.983 10.8006 28.283C9.17843 27.583 7.76732 26.633 6.56732 25.433C5.36732 24.233 4.41732 22.8219 3.71732 21.1997C3.01732 19.5775 2.66732 17.8441 2.66732 15.9997C2.66732 14.1552 3.01732 12.4219 3.71732 10.7997C4.41732 9.17745 5.36732 7.76634 6.56732 6.56634C7.76732 5.36634 9.17843 4.41634 10.8006 3.71634C12.4229 3.01634 14.1562 2.66634 16.0007 2.66634Z" fill="white"/>
    </g>
    </svg>
  )
}

export default ExclamationRounded
