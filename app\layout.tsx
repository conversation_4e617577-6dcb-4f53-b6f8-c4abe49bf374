// "use client"

import './globals.css';
import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { Toaster } from "@/components/ui/toaster"
import ReactQueryProvider from './providers/ReactQueryProvider';
import AuthProvider from './providers/AuthProviders';
import ReduxProvider from './providers/ReduxProvider';


const inter = Inter({
    subsets: ['latin'],
    variable: '--font-inter',
    display: "swap",
    preload: true,
})

export const metadata: Metadata = {
    title: 'Apply Goal',
    description: 'Generated by create next app',
};

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {    
    return (
        <html lang='en'>
            <body
                className={`${inter.variable} font-inter bg-primaryOne antialiased`}
            >
                    <ReduxProvider>
                        {children}
                    </ReduxProvider>
                <Toaster />
            </body>
        </html>
    );
}
