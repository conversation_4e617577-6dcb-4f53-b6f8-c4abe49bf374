
const submitquestion = () => {
    return (
        <svg width="17" height="16" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
        <mask id="mask0_3490_8299" style={{maskType: "alpha"}} maskUnits="userSpaceOnUse" x="0" y="0" width="17" height="16">
        <rect x="0.5" width="16" height="16" fill="currentColor"/>
        </mask>
        <g mask="url(#mask0_3490_8299)">
        <path d="M11.3 13.6667L13.1667 11.8V13.3333H13.8333V10.6667H11.1667V11.3333H12.7L10.8333 13.2L11.3 13.6667ZM3.83333 14C3.46667 14 3.15278 13.8694 2.89167 13.6083C2.63056 13.3472 2.5 13.0333 2.5 12.6667V3.33333C2.5 2.96667 2.63056 2.65278 2.89167 2.39167C3.15278 2.13056 3.46667 2 3.83333 2H13.1667C13.5333 2 13.8472 2.13056 14.1083 2.39167C14.3694 2.65278 14.5 2.96667 14.5 3.33333V7.8C14.2889 7.7 14.0722 7.61389 13.85 7.54167C13.6278 7.46944 13.4 7.41667 13.1667 7.38333V3.33333H3.83333V12.6667H7.86667C7.9 12.9111 7.95278 13.1444 8.025 13.3667C8.09722 13.5889 8.18333 13.8 8.28333 14H3.83333ZM3.83333 12.6667V3.33333V7.38333V7.33333V12.6667ZM5.16667 11.3333H7.88333C7.91667 11.1 7.96944 10.8722 8.04167 10.65C8.11389 10.4278 8.19444 10.2111 8.28333 10H5.16667V11.3333ZM5.16667 8.66667H9.23333C9.58889 8.33333 9.98611 8.05556 10.425 7.83333C10.8639 7.61111 11.3333 7.46111 11.8333 7.38333V7.33333H5.16667V8.66667ZM5.16667 6H11.8333V4.66667H5.16667V6ZM12.5 15.3333C11.5778 15.3333 10.7917 15.0083 10.1417 14.3583C9.49167 13.7083 9.16667 12.9222 9.16667 12C9.16667 11.0778 9.49167 10.2917 10.1417 9.64167C10.7917 8.99167 11.5778 8.66667 12.5 8.66667C13.4222 8.66667 14.2083 8.99167 14.8583 9.64167C15.5083 10.2917 15.8333 11.0778 15.8333 12C15.8333 12.9222 15.5083 13.7083 14.8583 14.3583C14.2083 15.0083 13.4222 15.3333 12.5 15.3333Z" fill="white"/>
        </g>
        </svg>
    )
}

export default submitquestion;

