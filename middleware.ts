import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(req: NextRequest) {
  const accessToken = req.cookies.get('accessToken')?.value;
  const { pathname } = req.nextUrl;

  const publicRoutes = ['/login', '/sign-up', '/forget-password', '/verify-otp'];
  const isPublic = publicRoutes.some((publicPath) => pathname.startsWith(publicPath));

  if (!accessToken && !isPublic) {
    const loginUrl = new URL('/login', req.url);
    return NextResponse.redirect(loginUrl);
  }

  if (accessToken && isPublic) {
    const homeUrl = new URL('/', req.url);
    return NextResponse.redirect(homeUrl);
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/',
    '/login',
    '/sign-up',
    '/forget-password',
    '/verify-otp',
    '/(dashboard|student|counselor|university)(/.*)?',
  ],
};
