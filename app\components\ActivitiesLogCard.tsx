import React from 'react';
import { EntryCardProps } from '@/types';
import Book from '@/app/assets/svg/book';
import Flag from '@/app/assets/svg/flag';
import Bookmark from '@/app/assets/svg/bookmark';
import Telephone from '@/app/assets/svg/telephone';
import CalendarSmall from '@/app/assets/svg/calendar-small';
import GraduationCap from '@/app/assets/svg/graduation-cap';
import InPerson from '../assets/svg/InPerson';
import OverPhone from '../assets/svg/OverPhone';

const ActivitiesLogCard: React.FC<EntryCardProps> = ({ entry }) => {
    return (
        <div className='md:px-[30px] px-4 md:py-[18px] py-4 bg-primaryOne border-primaryColor/20 rounded-[12px] border flex md:flex-row flex-col md:items-center items-start gap-4 justify-between'>
            <div className='flex flex-col gap-2.5'>
                <div className='flex md:flex-row flex-col md:items-center items-start gap-2.5'>
                    <p className='font-medium text-sm leading-[17px] text-primaryColor'>{entry.name}</p>
                    <div className='flex md:flex-row flex-col md:items-center items-start md:gap-[33px] gap-2.5'>
                        {entry.phone && (
                            <span className='text-xs leading-[15px] font-medium text-grayFour inline-flex items-center gap-2.5'>
                                <Telephone />
                                {entry.phone}
                            </span>
                        )}
                        {entry.location && (
                            <span className='text-xs leading-[15px] font-medium text-grayFour inline-flex items-center gap-2.5'>
                                <Flag />
                                {entry.location}
                            </span>
                        )}
                    </div>
                </div>
                <div className='flex md:flex-row flex-col md:items-center items-start md:gap-[33px] gap-2.5'>
                    <span className='text-xs leading-[15px] font-medium text-grayFour inline-flex items-center gap-2.5'>
                        <CalendarSmall />
                        {entry.startDate}
                    </span>
                    {entry.program && <span className='text-xs leading-[15px] font-medium text-grayFour inline-flex items-center gap-2.5'>
                        <Bookmark />
                        {entry.program}
                    </span>}
                    {entry.degree && <span className='text-xs leading-[15px] font-medium text-grayFour inline-flex items-center gap-2.5'>
                        <Book />
                        {entry.degree}
                    </span>}
                    {entry.university && (
                        <span className='text-xs leading-[15px] font-medium text-grayFour inline-flex items-center gap-2.5'>
                            <GraduationCap />
                            {entry.university}
                        </span>
                    )}
                </div>
            </div>
            <div className='flex items-center gap-5'>
                <div>
                    {entry.buttonText === 'Counseling' && (
                        entry.isInPerson ? <InPerson /> : <OverPhone />
                    )}
                    
                </div>
                
                <div className='pl-4 border-l border-primaryColor/20'>
                    <button
                        className={` border border-primaryColor/20 px-3 py-2 rounded-[50px] font-semibold text-xs leading-[15px] ${
                            entry.buttonText === 'Student Enrollment'
                                ? ' bg-primaryOne text-primaryColor'
                                : entry.buttonText === 'Application'
                                ? ' bg-primaryThree text-primaryColor'
                                : ' bg-secondaryColor/10 text-secondaryColor'
                        }`}
                    >
                        {entry.buttonText}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default ActivitiesLogCard;
