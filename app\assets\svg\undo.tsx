import React from 'react';
import { xIconProps } from '@/types';

const undo: React.FC<xIconProps> = ({ className }) => {
    return (
        <svg
            className={className}
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="mask0_2560_27523"
                style={{maskType:'alpha'}}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="18"
                height="18"
            >
                <rect width="18" height="18" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_2560_27523)">
                <path
                    d="M6.00156 14.2496C5.78906 14.2496 5.61094 14.1777 5.46719 14.034C5.32344 13.8902 5.25156 13.7121 5.25156 13.4996C5.25156 13.2871 5.32344 13.109 5.46719 12.9652C5.61094 12.8215 5.78906 12.7496 6.00156 12.7496H10.5766C11.3641 12.7496 12.0484 12.4996 12.6297 11.9996C13.2109 11.4996 13.5016 10.8746 13.5016 10.1246C13.5016 9.37461 13.2109 8.74961 12.6297 8.24961C12.0484 7.74961 11.3641 7.49961 10.5766 7.49961H5.85156L7.27656 8.92461C7.41406 9.06211 7.48281 9.23711 7.48281 9.44961C7.48281 9.66211 7.41406 9.83711 7.27656 9.97461C7.13906 10.1121 6.96406 10.1809 6.75156 10.1809C6.53906 10.1809 6.36406 10.1121 6.22656 9.97461L3.52656 7.27461C3.45156 7.19961 3.39844 7.11836 3.36719 7.03086C3.33594 6.94336 3.32031 6.84961 3.32031 6.74961C3.32031 6.64961 3.33594 6.55586 3.36719 6.46836C3.39844 6.38086 3.45156 6.29961 3.52656 6.22461L6.22656 3.52461C6.36406 3.38711 6.53906 3.31836 6.75156 3.31836C6.96406 3.31836 7.13906 3.38711 7.27656 3.52461C7.41406 3.66211 7.48281 3.83711 7.48281 4.04961C7.48281 4.26211 7.41406 4.43711 7.27656 4.57461L5.85156 5.99961H10.5766C11.7891 5.99961 12.8297 6.39336 13.6984 7.18086C14.5672 7.96836 15.0016 8.94961 15.0016 10.1246C15.0016 11.2996 14.5672 12.2809 13.6984 13.0684C12.8297 13.8559 11.7891 14.2496 10.5766 14.2496H6.00156Z"
                    fill="currentColor"
                />
            </g>
        </svg>
    );
};

export default undo;
