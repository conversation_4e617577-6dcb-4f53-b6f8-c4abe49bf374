import Link from 'next/link';
import Image from 'next/image';
import { CompareButtonProps } from '@/types';
import Book from '@/app/assets/svg/book.svg';

const CompareButton: React.FC<CompareButtonProps> = ({ 
    selectedCourseNumber 
}) => {
    return (
        <>
        <Link 
            className='flex gap-2 text-white text-opacity-60 font-semibold text-base leading-5 items-center justify-center w-full overflow-y-auto text-center bg-tertiary py-4 rounded-t-[10px] z-50' 
            href={'/compare-course'}
        >
            <Image src={Book} alt='Book icon' />
            Compare Programs 
            <span className='font-medium text-xs leading-6 text-primaryColor rounded-[50px] py-1 px-2.5 bg-white'>
                {selectedCourseNumber} / 4
            </span>
        </Link>
        </>
    )
}

export default CompareButton;