'use client'
import React, { useState, useRef, useEffect } from 'react';
import { SquarePlus, SquareMinus } from 'lucide-react';
import DeleteDeptOrTeam from './DeleteDeptOrTeam';
import DepartmentEdit from '@/app/assets/svg/DepartmentEdit';
import DepartmentDelete from '@/app/assets/svg/DepartmentDelete';

interface TreeItemProps {
    label: string;
    children?: React.ReactNode;
    defaultOpen?: boolean;
    depth?: number;
}

const bgColors = [
    'bg-primaryTwo',
    'bg-primaryFour',
    'bg-grayEight',
    'bg-grayOne',
    'bg-primaryOne'
];

const TreeItem: React.FC<TreeItemProps> = ({
    label,
    children,
    defaultOpen = false,
    depth = 0,
}) => {
    const [open, setOpen] = useState(defaultOpen);
    const [showDeleteModal, setShowDeleteModal] = useState(false);

    const [borderHeight, setBorderHeight] = useState(0);
    const [isHovered, setIsHovered] = useState(false);
    const childrenContainerRef = useRef<HTMLDivElement>(null);
    const hasChildren = React.Children.count(children) > 0;
    const bgColor = bgColors[depth] || bgColors[bgColors.length - 1];
    
    // Convert children to array to work with indices
    const childrenArray = React.Children.toArray(children);
    const validChildren = childrenArray.filter(child => 
        React.isValidElement(child) &&
        (child.type === TreeItem || (child.type as any).displayName === 'TreeItem')
    );

    // Calculate border height based on actual rendered content
    useEffect(() => {
        const container = childrenContainerRef.current;
        if (!open || !hasChildren || !container) {
            setBorderHeight(0);
            return;
        }

        const resizeObserver = new ResizeObserver(() => {
            if (container.lastElementChild) {
                const lastChild = container.lastElementChild as HTMLElement;
                // The horizontal connector is positioned 28px from the top of the child wrapper
                const horizontalLineOffset = 28;
                const endPosition = lastChild.offsetTop + horizontalLineOffset;
                
                // The vertical line starts at -19px from its container, so we adjust the height
                const calculatedHeight = endPosition + 19; 
                setBorderHeight(Math.max(0, calculatedHeight));
            }
        });

        resizeObserver.observe(container);

        return () => {
            resizeObserver.disconnect();
        };
    }, [open, hasChildren]);

    return (
        <>
            <div
                className={`flex items-center cursor-pointer ${bgColor} rounded py-[18px] px-5 relative mb-5`}
                onClick={() => hasChildren && setOpen(!open)}
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
            >
                <div className={`flex items-center gap-2.5 rounded-[10px]  w-full`}>
                    {hasChildren &&
                        (open ? (
                            <SquareMinus className='w-[22px] h-[22px] text-grayFive' />
                        ) : (
                            <SquarePlus className='w-[22px] h-[22px] text-grayFive' />
                        ))}
                    <span className='text-lg font-medium leading-none text-grayFive'>{label}</span>
                </div>
                {isHovered && (
                    <div className='absolute right-5 top-4'>
                        <div className='flex items-center gap-2.5'>
                            <DepartmentEdit />
                            <button onClick={e => { e.stopPropagation(); setShowDeleteModal(true); }}>
                                <DepartmentDelete />
                            </button>
                        </div>
                    </div>
                )}
            </div>
            
            {open && hasChildren && (
                <div className='relative ml-[30px]'>
                    {/* Dynamic border that adjusts to actual content height */}
                    <div 
                        className="absolute left-0 -top-[19px] border-l border-dashed border-grayTwo" 
                        style={{
                            height: `${borderHeight}px`
                        }}
                    />
                    <div ref={childrenContainerRef} className='pl-[35px] relative z-10 space-y-5'>
                        {validChildren.map((child, index) => {
                            
                            if (React.isValidElement(child)) {
                                return (
                                    <div key={index} className="relative mb-5">
                                        {depth >= 0 && (
                                            <div className="absolute left-[-35px] top-7 h-0.5 w-[35px] border-t border-dashed border-gray-300" />
                                        )}
                                        {React.cloneElement(child as React.ReactElement<any>, { 
                                            depth: depth + 1 
                                        })}
                                    </div>
                                );
                            }
                            return child;
                        })}
                    </div>
                </div>
            )}
            <DeleteDeptOrTeam open={showDeleteModal} onClose={() => setShowDeleteModal(false)} />
            {/* <ModalLayout open={showDeleteModal} onCancel={() => setShowDeleteModal(false)} onDelete={() => {}} /> */}

        </>
    );
};

export default TreeItem;