import axios from 'axios';
import { 
    LoginInput, 
    OTPResponse, 
    SendOTPInput, 
    LoginResponse, 
    RegisterInput, 
    SSOLoginProps,
    VerifyOTPInput,
} from '@/types';

const AUTH_BASE_URL = process.env.NEXT_PUBLIC_AUTH_BASE_URL;

export const registerUser = async (data: RegisterInput) => {
    const response = await axios.post(`${AUTH_BASE_URL}/auth/register`, data);
    console.log('response', data);
    return response.data;
};

export const loginUser = async (data: LoginInput): Promise<LoginResponse> => {
    try {
        const response = await axios.post(`${AUTH_BASE_URL}/auth/login`, data, {
            headers: {
                'Content-Type': 'application/json',
            }
        });
        
        console.log('Login API response:', response.data);
        return response.data;
    } catch (error) {
        console.error('Login API error:', error);
        throw error;
    }
};

export const sendOTP = async (data: SendOTPInput): Promise<OTPResponse> => {
    try {
        const response = await axios.post(`${AUTH_BASE_URL}/auth/generate-otp`, data, {
            headers: {
                'Content-Type': 'application/json',
            },
        });
        
        console.log('Send OTP response:', response.data);
        return response.data;
    } catch (error) {
        console.error('Send OTP error:', error);
        throw error;
    }
};

export const verifyOTP = async (data: VerifyOTPInput): Promise<OTPResponse> => {
    try {
        const response = await axios.post(`${AUTH_BASE_URL}/auth/verify-otp`, data, {
            headers: {
                'Content-Type': 'application/json',
            },
        });
        
        console.log('Verify OTP response:', response.data);
        return response.data;
    } catch (error) {
        console.error('Verify OTP error:', error);
        throw error;
    }
};

export const logoutUser = async (token: string): Promise<boolean> => {
    try {
        const response = await axios.post(`${AUTH_BASE_URL}/auth/logout`,{},
            {
                headers: {
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json',
                }
            }
        );
        return response.request;
    } catch (error: any) {
        console.error('Logout API error:', error?.response?.status || error.message);
        return false;
    }
};

export const uploadImage = async (file: File): Promise<string> => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await axios.post(`${AUTH_BASE_URL}/upload?type=image`,formData,
        {
            headers: {
            'Content-Type': 'multipart/form-data',
        }
    });

    return response.data?.url;
};

// export const uploadFile = async (file: File): Promise<string> => {
//     const formData = new FormData();
//     formData.append('file', file);

//     const response = await axios.post(
//         'http://auth-api.localhost/api/upload?type=file',
//         formData,
//         {
//         headers: {
//             'Content-Type': 'multipart/form-data',
//         },
//         }
//     );

//     return response.data?.url; // assumes API returns { url: "..." }
// };

// lib/api/uploadMultipleFiles.ts

export const uploadMultipleFiles = async (files: File[]): Promise<string[]> => {
  const formData = new FormData();
  files.forEach((file) => {
    formData.append('files', file); // use 'files' if your backend supports multiple with same key
  });

  const response = await axios.post(
    'http://auth-api.localhost/api/upload?type=file',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  );

  return response.data?.urls || [];
};

export const googleSSOLogin = async (data: SSOLoginProps) => {
  const response = await axios.post(`${AUTH_BASE_URL}/auth/sso`, data);

  return response.data;
};