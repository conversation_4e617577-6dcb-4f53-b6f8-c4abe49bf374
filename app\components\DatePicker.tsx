'use client'

import * as React from 'react';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import CalendarIcon from '@/app/assets/svg/calendar';
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'

interface DatePickerProps {
    value?: Date;
    title?: string;
    onChange?: (date: Date | undefined) => void;
    label?: string;
}

export function DatePicker({ value, onChange, title='Date of Birth' }: DatePickerProps) {

const [date, setDate] = React.useState<Date>()

return (
    <div className='flex flex-col'>
        <Label className='font-medium text-sm text-grayFive mb-1.5'>
            {title}
        </Label>
        <Popover>
            <PopoverTrigger asChild>
                <Button
                    variant={'outline'}
                    className={cn(
                        'border-tertiary border-opacity-20 justify-start py-2.5 w-full text-left font-normal',
                        !value && 'text-muted-foreground'
                  )}
                >
                    <CalendarIcon />
                    {value ? format(value, 'PPP') : <span className='font-normal text-base text-grayTwo'>MM/ DD/ YYYY</span>}
                </Button>
            </PopoverTrigger>
            <PopoverContent className='!w-full p-0' align='start'>
                <Calendar
                    className='w-full'
                    mode='single'
                    selected={value}
                    onSelect={onChange}
                    initialFocus
                />
            </PopoverContent>
        </Popover>
    </div>
)
}
