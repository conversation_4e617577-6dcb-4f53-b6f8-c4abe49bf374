'use client';

import Link from 'next/link';
import React, { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import Compare from '@/app/assets/svg/compare';
import ArrowUp from '@/app/assets/svg/arrowUp';
import { Button } from '@/components/ui/button';
import ArrowDown from '@/app/assets/svg/arrowDown';
import RightIcon from '@/app/assets/svg/righticon';   
import AddDocumentOutline from '@/app/assets/svg/add-document-outline';
import {
    Accordion,
    AccordionItem,
    AccordionTrigger,
    AccordionContent,
} from '@/components/ui/accordion';

type AccordionItem = {
    title: string;
    details: { label: string; value: string }[];
};

type AccordionProps = {
    data: AccordionItem[];
};


const AccordionLayout: React.FC<AccordionProps> = ({ data }) => {
    // const [showAll, setShowAll] = useState(false);

    // const handleShowMore = () => {
    //     setShowAll(true);
    // };
    const { toast } = useToast()
    const [addedApplications, setAddedApplications] = useState<string[]>([]);

    const handleAddApplication = (item: AccordionItem) => {
        if (!addedApplications.includes(item.title)) {
            setAddedApplications((prev) => [...prev, item.title]);
            // console.log('Application added:', item); // Simulated backend mechanism
        }
    };

    return (
        <>
            <Accordion 
                type='single'
                collapsible
                className='w-full space-y-4 bg-white '
                defaultValue={data.length > 0 ? 'item-0' : undefined} // Default open the first item
            >
                {data.slice(0, 5).map((item, index) => (
                    <AccordionItem 
                        key={index} 
                        value={`item-${index}`} 
                        className='border border-[#1952BB33] rounded-[14px]'
                    >
                        <AccordionTrigger 
                            closeIcon={ArrowDown} 
                            IconClass='!text-graySix' 
                            openIcon={ArrowUp} 
                            className='text-graySix flex justify-between items-center px-5 py-4 text-base font-medium hover:no-underline'
                        >
                            {item.title}
                        </AccordionTrigger>
                        <AccordionContent className='px-4 py-4 rounded-xl'>
                            <div className='grid md:grid-cols-2 grid-cols-1 md:gap-4 gap-3 mb-5'>
                                {item.details.map((detail, index) => (
                                    <div className='flex flex-col text-graySix gap-1' key={index}>
                                        <span className='font-medium text-base'>{detail.value}</span>
                                        <span className='text-xs font-normal leading-[18px]'>
                                            {detail.label}
                                        </span>
                                    </div>
                                ))}
                            </div>
                            <div className='grid md:grid-cols-2 grid-cols-1 md:gap-6 gap-3'>
                                <Button
                                    onClick={() => {
                                        toast({
                                            variant: 'default',
                                            description: 'Your message has been sent.',
                                        })
                                        handleAddApplication(item)
                                    }}
                                    className={`flex gap-3 items-center justify-center px-4 py-2.5 bg-primaryColor text-white rounded-lg text-sm leading-[17px]  ${
                                        addedApplications.includes(item.title)
                                            ? 'bg-tertiary'
                                            : 'bg-primaryColor'
                                    }`}
                                >   
                                    {addedApplications.includes(item.title) ?
                                    <>
                                        <RightIcon />
                                        <span>Added</span>
                                    </>
                                    :
                                    <>
                                        <AddDocumentOutline />
                                        <span>Add Application</span>
                                    </>
                                }
                                </Button>
                                <Link
                                    rel='noopener noreferrer' 
                                    target='_blank'
                                    href='/compare-course'
                                    className='flex gap-3 items-center justify-center px-4 py-2.5 bg-primaryOne hover:bg-primaryTwo text-primaryColor rounded-lg text-sm leading-[17px] text-center'
                                >
                                    <Compare />
                                    <span>Compare</span>
                                </Link>
                            </div>
                        </AccordionContent>
                    </AccordionItem>
                ))}
            </Accordion>
            
        </>
    );
};

export default AccordionLayout;