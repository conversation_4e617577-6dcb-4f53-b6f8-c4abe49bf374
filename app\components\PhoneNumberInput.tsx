// 'use client';

// import { useState } from 'react';
// import 'react-phone-number-input/style.css';
// import { Label } from '@/components/ui/label';
// import { PhoneNumberInputProps } from '@/types';
// import PhoneInput, { isPossiblePhoneNumber, isValidPhoneNumber } from 'react-phone-number-input';


// const PhoneNumberInput:React.FC<PhoneNumberInputProps> = ({ 
//     label,
//     className, 
//     NumberInputClassName,
//     ...rest
// }) => {
//     const [value, setValue] = useState<string>('');

//     return (
//         <div>
//             {label && (
//                 <Label className='font-medium text-sm text-grayFive mb-1.5'>
//                     {label}
//                 </Label>
//             )}
//             <div className={` border focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 focus:!border-none border-tertiary border-opacity-20 placeholder:text-grayThree ${className}`}>
//                 <PhoneInput
//                     defaultCountry='BD'
//                     value={value}
//                     onChange={(value) => {setValue(value || '')}}
//                     placeholder='Enter phone number'
//                     className=''
//                     numberInputProps={{ className: `outline-none ${NumberInputClassName}` }}
//                     countrySelectProps={{ className: ' ' }}
//                     international
//                     smartCaret
//                     {...rest}
//                 />
//             </div>
//         </div>
//     );
// }

// export default PhoneNumberInput



'use client';

import { useState, useEffect } from 'react';
import 'react-phone-number-input/style.css';
import { Label } from '@/components/ui/label';
import { PhoneNumberInputProps } from '@/types';
import PhoneInput, { isPossiblePhoneNumber, isValidPhoneNumber } from 'react-phone-number-input';


const PhoneNumberInput:React.FC<PhoneNumberInputProps> = ({ 
    label,
    className, 
    NumberInputClassName,
    value: propValue,
    onChange: propOnChange,
    ...rest
}) => {
    const [internalValue, setInternalValue] = useState<string>(propValue || '');
    
    useEffect(() => {
        if (propValue !== undefined) {
            setInternalValue(propValue);
        }
    }, [propValue]);

    const handleChange = (newValue: string | undefined) => {
        const value = newValue || '';
        setInternalValue(value);
        
        if (propOnChange) {
            propOnChange(value);
        }
    };

    return (
        <div>
            {label && (
                <Label className='font-medium text-sm text-grayFive mb-1.5'>
                    {label}
                </Label>
            )}
            <div className={`border focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 focus:!border-none border-tertiary border-opacity-20 placeholder:text-grayThree ${className}`}>
                <PhoneInput
                    defaultCountry='BD'
                    value={internalValue}
                    onChange={handleChange}
                    placeholder='Enter phone number'
                    className=''
                    numberInputProps={{ className: `outline-none ${NumberInputClassName}` }}
                    countrySelectProps={{ className: ' ' }}
                    international
                    smartCaret
                    {...rest}
                    error={internalValue ? (isValidPhoneNumber(internalValue) ? undefined : 'Invalid phone number') : 'Phone number required'}
                />
            </div>
            {/* For debugging - remove these lines in production */}
            {/* Is possible: {internalValue && isPossiblePhoneNumber(internalValue) ? 'true' : 'false'}
            Is valid: {internalValue && isValidPhoneNumber(internalValue) ? 'true' : 'false'}
            <div>Current Value: {internalValue}</div>  */}
        </div>
    );
}

export default PhoneNumberInput