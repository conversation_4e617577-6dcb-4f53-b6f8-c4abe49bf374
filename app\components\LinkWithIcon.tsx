import React, { Children } from 'react';
import Link from 'next/link';
import { LinkWithIconProps } from '@/types';

const LinkWithIcon: React.FC<LinkWithIconProps> = ({
    url,
    children,
    className,
}) => {
    return (
        <Link   
            className={`flex bg-tertiary gap-3 items-center justify-center rounded-lg py-3 px-5 border font-semibold text-sm leading-4 text-white ${className}`} 
            href={url}
        >
            {children}
        </Link>
    )
}

export default LinkWithIcon