import { authApi } from './api/authApi';
import authReducer from './slices/authSlice';
import { configureStore } from '@reduxjs/toolkit';
import { departmentApi } from './api/departmentApi';
import { rolesApi } from './api/rolesApi';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    [authApi.reducerPath]: authApi.reducer,
    [departmentApi.reducerPath]: departmentApi.reducer,
    [rolesApi.reducerPath]: rolesApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware()
    .concat(authApi.middleware)
    .concat(departmentApi.middleware)
    .concat(rolesApi.middleware),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
