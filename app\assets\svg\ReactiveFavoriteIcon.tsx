'use client';

import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import Favorite from '@/app/assets/svg/favorite';
const ReactiveFavoriteIcon = () => {
    const [isFavorited, setIsFavorited] = useState<boolean>(false);

    const { toast } = useToast();

    const toggleFavorite = () => {
        const newValue = !isFavorited;
        setIsFavorited(newValue);
        toast({
          variant: 'default',
          description: newValue
            ? 'This university has been added to your favorites'
            : 'This university has been removed from your favorites',
          className: 'bg-primaryColor border-none text-white',
        });
      };
    return (
        <div 
            onClick={toggleFavorite} 
            className='cursor-pointer'
        >
                <Favorite isFavorited={isFavorited} />
        </div>
    )
}

export default ReactiveFavoriteIcon
