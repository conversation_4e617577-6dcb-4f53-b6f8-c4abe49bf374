import React from 'react';
import ModalLayout from '../layout/ModalLayout';
import FeaturedIcon from '@/app/assets/svg/FeaturedIcon';


interface DeleteDeptOrTeamProps {
    open: boolean;
    onClose: () => void;
}

const DeleteDeptOrTeam = ({ open, onClose }: DeleteDeptOrTeamProps) => {
    return (
        <ModalLayout  open={open} onClose={onClose}>
            <div className="bg-white rounded-[12px] p-6 w-[400px] shadow-lg text-center">
                <div className="mb-4 mx-auto flex items-center justify-center">
                    <FeaturedIcon />
                </div>
                <h2 className="font-semibold text-lg leading-7 text-graySix mb-2">Delete dept/team</h2>
                <p className="text-normal text-sm leading-5 text-grayFive mb-8">Are you sure you want to delete this dept/team? This action cannot be undone.</p>
            
                <div className="flex justify-between gap-3">
                    <button
                        className="flex-1 py-2.5 rounded-[8px] border border-[#1952BB33] text-grayFive font-medium text-base leading-6"
                        onClick={onClose}
                    >
                        Cancel
                    </button>
                    <button
                        className="flex-1 py-2.5 rounded-[8px] bg-primaryColor text-white font-medium text-base leading-6"
                        onClick={onClose}
                    >
                        Delete
                    </button>
                </div>
            </div>
        </ModalLayout>
    )
}

export default DeleteDeptOrTeam