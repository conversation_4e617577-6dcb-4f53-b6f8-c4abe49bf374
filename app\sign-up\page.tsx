'use client';

import React from 'react';
import StudentSignup from '../components/StudentSignup';
import AuthLayout from '../components/layout/AuthLayout';
import RecruitementPartnerSignup from '../components/RecruitementPartnerSignup';
import { 
    Tabs, 
    TabsList, 
    TabsContent, 
    TabsTrigger 
} from '@/components/ui/tabs';
import { 
    signupTabLists, 
    signupTabImages,
    signupTabDescriptions
} from '@/common';

const Page = () => {
    const [activeTab, setActiveTab] = React.useState<'Student' | 'RecruitmentPartner' >('Student');

    const handleTabChange = (value: 'Student' | 'RecruitmentPartner' ) => {
        setActiveTab(value);
    };

    const currentImage = signupTabImages[activeTab];
    const currentDescription = signupTabDescriptions[activeTab];

    return (
        <>
            <AuthLayout
                imageSrc={currentImage()}
                description={currentDescription}
                title='Sign up'
                heading='Enter your details and start journey with us.'
            >
                <Tabs defaultValue='Student' className='max-w-[440px] mx-auto'>
                    <TabsList className='flex bg-primaryOne h-fit justify-between border-2 border-[#1952BB33] rounded-full p-0 my-6'>
                        {signupTabLists.map((tab, index) => (
                            <TabsTrigger
                                key={index}
                                value={tab.value} 
                                className={`w-full m-0.5 text-base text-center font-semibold leading-none py-3.5  data-[state=active]:shadow-none data-[state=active]:rounded-full   ${activeTab === tab.value ? 'rounded-full data-[state=active]:bg-primaryColor data-[state=active]:text-white' : 'text-grayFour'}`}
                                onClick={() => handleTabChange(tab.value as 'Student' | 'RecruitmentPartner')}
                            >
                                {tab.label}
                            </TabsTrigger>
                        ))}
                    </TabsList>
                    <TabsContent value='Student'>
                        <StudentSignup />
                    </TabsContent>
                    <TabsContent value='RecruitmentPartner'>
                        <RecruitementPartnerSignup />
                    </TabsContent>
                </Tabs>
            </AuthLayout>
        </>
    );
};

export default Page;
