import React from 'react';
import { IconProps } from '@/types';

const check: React.FC<IconProps> = ({ className }) => {
    return (
        <svg className={className} width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_3637_5500" style={{maskType:'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="21" height="21">
            <rect x="0.882812" y="0.290039" width="20" height="20" fill="currentColor"/>
            </mask>
            <g mask="url(#mask0_3637_5500)">
            <path d="M8.84026 12.915L15.9028 5.85254C16.0694 5.68587 16.2639 5.60254 16.4861 5.60254C16.7083 5.60254 16.9028 5.68587 17.0694 5.85254C17.2361 6.01921 17.3194 6.21712 17.3194 6.44629C17.3194 6.67546 17.2361 6.87337 17.0694 7.04004L9.42359 14.7067C9.25693 14.8734 9.06248 14.9567 8.84026 14.9567C8.61804 14.9567 8.42359 14.8734 8.25693 14.7067L4.67359 11.1234C4.50693 10.9567 4.42707 10.7588 4.43401 10.5296C4.44095 10.3005 4.52776 10.1025 4.69443 9.93587C4.86109 9.76921 5.05901 9.68587 5.28818 9.68587C5.51734 9.68587 5.71526 9.76921 5.88193 9.93587L8.84026 12.915Z" fill="currentColor"/>
            </g>
        </svg>
    )}

export default check
