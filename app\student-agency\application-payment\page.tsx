import React from 'react';
import canada from '@/app/assets/svg/canada';
import Heading from '@/app/components/Heading';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import { Tabs, TabsContent, TabsList, TabsTrigger} from '@/components/ui/tabs';

const page = () => {
    const tableHeadData = [
        'AppID',
        'University',
        'Program',
        'Deadline',
        'Ammount'
    ];
    const unpaidApplicationLists = [
        {
            appId: '262',
            university: canada,
            program: '2 year Undergraduate Diploma',
            deadline: '21 Oct 2024',
            amount: '$150.00'
        },
        {
            appId: '159',
            university: canada,
            program: '2 year Undergraduate Diploma',
            deadline: '21 Oct 2024',
            amount: '$150.00'
        }
    ];
    const tableHeadPaid = [
        'AppID',
        'University',
        'Program',
        'Payment Date',
        'Status'
    ];
    const paidApplicationLists = [
        {
            appId: '262',
            university: canada,
            program: '2 year Undergraduate Diploma',
            deadline: '21 Oct 2024',
            status: 'Accepted'
        },
        {
            appId: '159',
            university: canada,
            program: '2 year Undergraduate Diploma',
            deadline: '21 Oct 2024',
            status: 'Canceled'
        }
    ];

    const statusColor = (status: string) => {
        switch (status) {
            case 'Accepted':
                return 'text-[#12B76A]';
            case 'Canceled':
                return 'text-red-500';
            default:
                return 'Unknown status.';
        }
    };

    return (
        <DashboardLayout>
            <div className='py-5'>
                <Heading level='h1'>
                    Application Payment
                </Heading>
            </div>
            <Tabs defaultValue='unpaid' className='w-full'>
                <TabsList className='bg-[#E9F0FF] h-20 rounded-[16px] w-full'>
                    <div className='mx-1.5 bg-primaryOne grid  grid-cols-2 w-full rounded-xl'>
                        <TabsTrigger 
                            className='data-[state=active]:shadow-none rounded-none rounded-l-xl data-[state=active]:text-primaryColor font-semibold text-base leading-5 text-grayFive py-5' 
                            value='unpaid'
                        >
                            Unpaid
                        </TabsTrigger>
                        <TabsTrigger 
                            className='data-[state=active]:shadow-none rounded-none rounded-r-xl data-[state=active]:text-primaryColor font-semibold text-base leading-5 text-grayFive py-5' 
                            value='paid'
                        >
                            Paid
                        </TabsTrigger>
                    </div>
                </TabsList>
                <TabsContent className='bg-white mt-6 rounded-[16px]' value='unpaid'>
                    <div className='overflow-x-auto'>
                        <table className='bg-white min-w-full rounded-[20px] text-sm text-left'>
                            <thead>
                                <tr>
                                    {tableHeadData.map((thData, index) => (
                                        <th 
                                            key={index} 
                                            className='p-4 font-bold text-xs tracking-[0.4px] text-grayFive'
                                        >
                                            {thData}
                                        </th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody className='border-t border-grayOne divide-y divide-grayOne'>
                                {unpaidApplicationLists.map((list, index) => (
                                    <tr key={index}>
                                        <td className='text-xs leading-5 text-graySix p-4 font-normal'>{list.appId}</td>
                                        <td className='text-xs leading-5 text-graySix p-4 font-normal'>{<list.university />}</td>
                                        <td className='text-xs leading-5 text-graySix p-4 font-normal'>{list.program}</td>
                                        <td className='text-xs leading-5 text-graySix p-4 font-normal'>{list.deadline}</td>
                                        <td className='text-xs leading-5 text-graySix p-4 font-normal'>{list.amount}</td>
                                    </tr>
                                ))}
                                <tr>
                                    <td colSpan={3}></td>
                                    <td className='p-4 font-semibold text-xs leading-none text-graySix'>Total</td>
                                    <td className='p-4 font-semibold text-xs leading-none text-graySix'>$372.00</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </TabsContent>
                <TabsContent className='bg-white mt-6 rounded-[16px] px-4' value='paid'>
                    <div className='overflow-x-auto'>
                        <table className='bg-white min-w-full rounded-[20px] text-sm text-left'>
                            <thead>
                                <tr>
                                    {tableHeadPaid.map((thData, index) => (
                                        <th 
                                            key={index} 
                                            className='p-4 font-bold text-xs tracking-[0.4px] text-grayFive'
                                        >
                                            {thData}
                                        </th>
                                    ))}
                                </tr>
                            </thead>
                            <tbody className='border-t border-grayOne divide-y divide-grayOne'>
                                {paidApplicationLists.map((list, index) => (
                                    <tr key={index}>
                                        <td className='text-xs leading-5 text-graySix p-4 font-normal'>{list.appId}</td>
                                        <td className='text-xs leading-5 text-graySix p-4 font-normal'>
                                            {<list.university />}
                                        </td>
                                        <td className='text-xs leading-5 text-graySix p-4 font-normal'>{list.program}</td>
                                        <td className='text-xs leading-5 text-graySix p-4 font-normal'>{list.deadline}</td>
                                        <td className={`text-xs leading-5 text-graySix p-4 ${statusColor(list.status)} font-normal`}>{list.status}</td>
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                </TabsContent>
            </Tabs>
        </DashboardLayout>
    )
}

export default page