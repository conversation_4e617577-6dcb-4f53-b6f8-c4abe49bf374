import React from 'react';

interface ArrowDownProps {
    className?: string
}

const arrowUp:React.FC<ArrowDownProps> = ({className}) => {
    return (
        <svg className={className} width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M15.75 12.5L10.75 7.5L5.75 12.5" stroke="currentColor" strokeWidth="1.66667" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>

    )
}

export default arrowUp