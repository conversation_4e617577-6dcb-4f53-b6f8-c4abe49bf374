import React from 'react';
import FeeFilter from './ui/FeeFilter';
import InputField from './InputField';
import { FiltersProps } from '@/types';
import SelectField from './SelectField';
import MultiSelect from './MultiSelect';
import Search from '@/app/assets/svg/search';
import InputFieldWithIcon from './InputFieldWithIcon';
import FilterLists from '@/app/components/FilterLists';
import SelectAndSearchCombobox from './SelectAndSearchCombobox';

const CoursesFilter: React.FC<FiltersProps> = ({
    intakes,
    programs,
    countries,
    className,
    tuitionFees,
    searchCourse,
    sortByOptions,
    onFilterChange,
}) => {
    const handleInputChange = (key: string, value: any) => {
        onFilterChange((prev: any) => ({
          ...prev,
          [key]: value,
        }));
    };

    const filterLists =['New York Institute of Technology - Vancouver...', 'Oct 2025', '11 weeks left', '2 days left'];
    return (
        <div className={`${className} drop-shadow-[0_2px_2px_rgba(30, 98, 224, 0.1)]`}>
            {searchCourse && (
                <InputFieldWithIcon 
                    placeholder='Search Courses'
                    icon={<Search />}
                    type='email'
                    // register={register('email', { required: 'Email is required' })}
                    className='mb-5'
                />
            )}
            <div className='grid grid-cols-1 md:grid-cols-4 gap-3'>
                <div>
                    <InputField 
                        id='course'
                        // value={filters.courseTitle} 
                        onChange={(e) => handleInputChange('courseTitle', e.target.value)}
                        placeholder='Course Title' 
                        type='text' 
                        label='Course' 
                    />
                </div>
                <SelectField 
                    label='Program'
                    options={programs}
                    placeholder=''
                    onChange={(value) => handleInputChange('program', value)}
                />
                <div>
                    <MultiSelect
                        options={intakes}
                        selectedValues={[]}
                        onChange={(value) => handleInputChange('intake', value)}
                        label='Intake'
                    />
                </div>
                <div>
                    <SelectAndSearchCombobox 
                        options={countries} 
                        label='Field of Study'
                        type='select'
                    />
                </div>
                <div>
                     <InputField 
                        id='tuition_fees'
                        value={''}
                        onChange={(e) => handleInputChange('country', e.target.value)}
                        placeholder='Enter Country/State' 
                        type='text' 
                        // errorMessage={'!name.trim() && error'}
                        label='Country/ State' 
                    />
                </div>
                <div>
                     <FeeFilter
                        label='Tuition Fees' 
                    />
                </div>
                <SelectField 
                    label='Sort by'
                    options={sortByOptions}
                    placeholder=''
                    onChange={(value) => handleInputChange('sortBy', value)}
                />
            </div>
            <div className='mt-5'>
                <FilterLists lists={filterLists} />
            </div>
        </div>
    )
}

export default CoursesFilter