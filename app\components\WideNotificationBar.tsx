'use client';

import { useState, useEffect } from 'react';
import ExclamationRounded from '../assets/svg/ExclamationRounded';

interface NotificationProps {
    title: string;
    message: string;
    storageKey?: string; // Optional custom storage key
}

export default function WideNotificationBar({
    title,
    message,
    storageKey = 'notification-dismissed',
}: NotificationProps) {
    const [isVisible, setIsVisible] = useState(false);
    const [isMounted, setIsMounted] = useState(false);

    useEffect(() => {
        // Check localStorage only after component mounts (client-side)
        setIsMounted(true);
        const dismissed = localStorage.getItem(storageKey);
        if (dismissed !== 'true') {
            setIsVisible(true);
        }
    }, [storageKey]);

    const handleUnderstand = () => {
        setIsVisible(false);
        localStorage.setItem(storageKey, 'true');
    };

    const handleClose = () => {
        setIsVisible(false);
        // Don't set localStorage here, so it will reappear on reload
    };

    if (!isMounted || !isVisible) return null;

    return (
        <div className=" flex justify-between w-full max-w-full p-5 bg-primaryColor text-white rounded-[14px]">
            <div className='flex items-center gap-5'>
                <ExclamationRounded />
                <div className="flex flex-col items-start gap-2 ">
                    <h3 className=" font-semibold">{title}</h3>
                    <p className='text-sm'>{message}</p>
                </div>
            </div>
            
            <div className=" flex items-center gap-5">
                <button
                    onClick={handleUnderstand}
                    className="px-4 py-2 rounded-full bg-primaryColor border border-white/40 text-sm font-medium hover:bg-tertiary focus:outline-none "
                >
                    I understand
                </button>
                <div className='border-l border-white/40 h-full'></div>
                <button
                    onClick={handleClose}
                    className=" focus:outline-none"
                    aria-label="Close notification"
                >
                    <svg
                        className="h-6 w-6"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M6 18L18 6M6 6l12 12"
                        />
                    </svg>
                </button>
            </div>
        </div>
    );
}
