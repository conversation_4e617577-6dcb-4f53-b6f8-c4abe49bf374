import React from 'react';
import { xIconProps } from '@/types';

const Italic: React.FC<xIconProps> = ({ className }) => {
    return (
        <svg
            className={className}
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="mask0_2560_27552"
                style={{maskType:'alpha'}}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="18"
                height="18"
            >
                <rect width="18" height="18" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_2560_27552)">
                <path
                    d="M4.6875 14.25C4.425 14.25 4.20312 14.1594 4.02188 13.9781C3.84063 13.7969 3.75 13.575 3.75 13.3125C3.75 13.05 3.84063 12.8281 4.02188 12.6469C4.20312 12.4656 4.425 12.375 4.6875 12.375H6.75L9 5.625H6.9375C6.675 5.625 6.45312 5.53438 6.27187 5.35313C6.09063 5.17188 6 4.95 6 4.6875C6 4.425 6.09063 4.20312 6.27187 4.02188C6.45312 3.84063 6.675 3.75 6.9375 3.75H12.5625C12.825 3.75 13.0469 3.84063 13.2281 4.02188C13.4094 4.20312 13.5 4.425 13.5 4.6875C13.5 4.95 13.4094 5.17188 13.2281 5.35313C13.0469 5.53438 12.825 5.625 12.5625 5.625H10.875L8.625 12.375H10.3125C10.575 12.375 10.7969 12.4656 10.9781 12.6469C11.1594 12.8281 11.25 13.05 11.25 13.3125C11.25 13.575 11.1594 13.7969 10.9781 13.9781C10.7969 14.1594 10.575 14.25 10.3125 14.25H4.6875Z"
                    fill="currentColor"
                />
            </g>
        </svg>
    );
};

export default Italic;
