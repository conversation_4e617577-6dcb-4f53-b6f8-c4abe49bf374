import React from 'react'

const visibility_off = () => {
  return (
    <svg width="20" height="20" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
        <mask id="mask0_2722_122758" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="21" height="21">
        <rect x="0.362305" y="0.636719" width="20" height="20" fill="#D9D9D9"/>
        </mask>
        <g mask="url(#mask0_2722_122758)">
        <path d="M13.0085 7.57422C13.4112 7.977 13.7064 8.43533 13.8939 8.94922C14.0814 9.46311 14.1474 9.99089 14.0918 10.5326C14.0918 10.7409 14.0154 10.918 13.8626 11.0638C13.7099 11.2096 13.5293 11.2826 13.321 11.2826C13.1126 11.2826 12.9355 11.2096 12.7897 11.0638C12.6439 10.918 12.571 10.7409 12.571 10.5326C12.6404 10.1714 12.6196 9.82422 12.5085 9.49089C12.3974 9.15755 12.2237 8.87283 11.9876 8.63672C11.7515 8.40061 11.4668 8.22005 11.1335 8.09505C10.8001 7.97005 10.446 7.94227 10.071 8.01172C9.86263 8.01172 9.68555 7.93533 9.53971 7.78255C9.39388 7.62977 9.32096 7.44922 9.32096 7.24089C9.32096 7.03255 9.39388 6.85547 9.53971 6.70964C9.68555 6.5638 9.86263 6.49089 10.071 6.49089C10.5987 6.43533 11.1196 6.5013 11.6335 6.6888C12.1474 6.8763 12.6057 7.17144 13.0085 7.57422ZM10.3626 5.63672C10.0987 5.63672 9.8418 5.64714 9.5918 5.66797C9.3418 5.6888 9.0918 5.727 8.8418 5.78255C8.60569 5.82422 8.39388 5.7895 8.20638 5.67839C8.01888 5.56727 7.89041 5.40061 7.82096 5.17839C7.75152 4.95616 7.77583 4.74089 7.89388 4.53255C8.01194 4.32422 8.18207 4.19922 8.4043 4.15755C8.72374 4.08811 9.04666 4.0395 9.37305 4.01172C9.69944 3.98394 10.0293 3.97005 10.3626 3.97005C12.2654 3.97005 14.005 4.47005 15.5814 5.47005C17.1578 6.47005 18.3626 7.81727 19.196 9.51172C19.2515 9.62283 19.2932 9.73741 19.321 9.85547C19.3487 9.97352 19.3626 10.0951 19.3626 10.2201C19.3626 10.3451 19.3522 10.4666 19.3314 10.5846C19.3105 10.7027 19.2724 10.8173 19.2168 10.9284C18.9668 11.4839 18.6578 12.0048 18.2897 12.4909C17.9217 12.977 17.5154 13.4214 17.071 13.8242C16.9043 13.977 16.7099 14.0395 16.4876 14.0117C16.2654 13.9839 16.0849 13.8728 15.946 13.6784C15.8071 13.4839 15.748 13.2721 15.7689 13.043C15.7897 12.8138 15.8835 12.6228 16.0501 12.4701C16.3835 12.1506 16.689 11.8034 16.9668 11.4284C17.2446 11.0534 17.4876 10.6506 17.696 10.2201C17.0015 8.81727 15.998 7.70269 14.6855 6.8763C13.373 6.04991 11.9321 5.63672 10.3626 5.63672ZM10.3626 16.4701C8.50152 16.4701 6.80013 15.9666 5.25846 14.9596C3.7168 13.9527 2.50152 12.6298 1.61263 10.9909C1.54319 10.8798 1.4911 10.7582 1.45638 10.6263C1.42166 10.4944 1.4043 10.3589 1.4043 10.2201C1.4043 10.0812 1.41819 9.94922 1.44596 9.82422C1.47374 9.69922 1.52235 9.57422 1.5918 9.44922C1.86957 8.89366 2.19249 8.36241 2.56055 7.85547C2.9286 7.34852 3.34874 6.88672 3.82096 6.47005L2.0918 4.72005C1.93902 4.55339 1.8661 4.35547 1.87305 4.1263C1.87999 3.89714 1.95985 3.70616 2.11263 3.55339C2.26541 3.40061 2.45985 3.32422 2.69596 3.32422C2.93207 3.32422 3.12652 3.40061 3.2793 3.55339L17.446 17.7201C17.5987 17.8728 17.6786 18.0638 17.6855 18.293C17.6925 18.5221 17.6126 18.7201 17.446 18.8867C17.2932 19.0395 17.0987 19.1159 16.8626 19.1159C16.6265 19.1159 16.4321 19.0395 16.2793 18.8867L13.3626 16.0117C12.8765 16.1645 12.3835 16.2791 11.8835 16.3555C11.3835 16.4319 10.8765 16.4701 10.3626 16.4701ZM4.98763 7.63672C4.58485 7.99783 4.2168 8.39366 3.88346 8.82422C3.55013 9.25477 3.26541 9.72005 3.0293 10.2201C3.72374 11.6228 4.72721 12.7374 6.03971 13.5638C7.35221 14.3902 8.79319 14.8034 10.3626 14.8034C10.6404 14.8034 10.9112 14.786 11.1751 14.7513C11.439 14.7166 11.7099 14.6784 11.9876 14.6367L11.2376 13.8451C11.0849 13.8867 10.939 13.918 10.8001 13.9388C10.6612 13.9596 10.5154 13.9701 10.3626 13.9701C9.32096 13.9701 8.43555 13.6055 7.70638 12.8763C6.97721 12.1471 6.61263 11.2617 6.61263 10.2201C6.61263 10.0673 6.62305 9.92144 6.64388 9.78255C6.66471 9.64366 6.69596 9.49783 6.73763 9.34505L4.98763 7.63672Z" fill="#9FA0A6"/>
        </g>
    </svg>
  )
}

export default visibility_off