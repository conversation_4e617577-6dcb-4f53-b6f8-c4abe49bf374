import Nav from './Nav';
import React from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { AsideProps } from '@/types';
import ApplyGoalLogo from '@/app/assets/svg/ApplyGoalLogo';
import ApplyGoalLogoSmall from '@/app/assets/svg/ApplyGoalLogoSmall';

const Aside: React.FC<AsideProps> = ({ 
    isExpanded, 
    expandSidbar, 
    toggleSidebar
}) => {
    return (
        <aside
            className={cn(' bg-white transition-all absolute h-screen overflow-hidden z-30 duration-300 drop-shadow-[0_1px_4px_rgba(0,0,0,0.2)]',
            isExpanded || toggleSidebar ? 'w-64' : 'w-20 hidden md:block')}
            onMouseEnter={expandSidbar}
            onMouseLeave={expandSidbar}
        >
            <div className='flex items-center justify-center py-12'>
                <Link href={'/'} className='flex'>
                    {isExpanded || toggleSidebar ? (
                        <>
                        <ApplyGoalLogo />
                        </>
                    ) : (
                        <ApplyGoalLogoSmall />
                    )}
                </Link>
            </div>

            <Nav 
                toggleSidebar={toggleSidebar} 
                isExpanded={isExpanded} 
            />
        </aside>
    )
}

export default Aside