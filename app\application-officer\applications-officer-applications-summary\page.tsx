"use client"

import { courses } from '@/common';
import React, { useState } from 'react';
import Heading from '@/app/components/Heading';
import Pagination from '@/app/components/Pagination';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import ApplicationSummaryTab from '@/app/components/ApplicationSummaryTab';

const Page = () => {
    const itemsPerPage = 2;
    const [currentPage, setCurrentPage] = useState(1);
    const totalPages = Math.ceil(courses.length / itemsPerPage);

    // const currentCources = courses.slice(
    //     (currentPage - 1) * itemsPerPage,
    //     currentPage * itemsPerPage
    // );

    const goToPage = (page: number) => {
        setCurrentPage(page);
    };
    return (
        <DashboardLayout>
            <div className='py-5'>
                <Heading level='h1'>
                    Applications Summary
                </Heading>
            </div>
            <div className='mb-12'>
                <ApplicationSummaryTab />
            </div>
            <div className='pb-24'>
                <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={goToPage}
                />
            </div>
        </DashboardLayout>
    )
}

export default Page