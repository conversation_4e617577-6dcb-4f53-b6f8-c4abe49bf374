'use client'

import React from 'react'; 
import { useRouter } from 'next/navigation'
import AccessRestricted from '../assets/svg/AccessRestricted';
import DashboardLayout from '../components/layout/DashboardLayout';

const page = () => {
    const router = useRouter();

    return (
        <DashboardLayout>
            <div className='bg-white flex flex-col justify-center items-center rounded-[20px] py-[205px]'>
                <AccessRestricted />
                <div className='text-center mt-10'>
                    <h1 className='font-semibold text-[28px] leading-none text-graySix'>Your current role does not grant access to this page.</h1>
                    <p className='pt-5 font-normal text-xl leading-7 text-grayFive'>Need access? Contact your administrator.</p>
                    <div className='mt-10'>
                        <button 
                            onClick={() => router.back()}
                            className='font-semibold text-base leading-6 text-white rounded-full py-2.5 px-6 bg-primaryColor drop-shadow-[0_1px_4px_rgba(0,0,0,0.05)]'
                        >
                            Go back
                        </button>
                    </div>
                </div>
            </div>
        </DashboardLayout>
    )
}

export default page