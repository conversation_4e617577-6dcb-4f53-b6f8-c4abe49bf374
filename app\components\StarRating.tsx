import React from 'react';
import EmptyStar from '../assets/svg/EmptyStar';
import HalfStar from '../assets/svg/HalfStar';
import RatingStar from '../assets/svg/RatingStar';

interface StarRatingProps {
    rating: number;
}

const StarRating: React.FC<StarRatingProps> = ({ rating }) => {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);

  return (
        <div className='flex space-x-1'>
            {[...Array(fullStars)].map((_, i) => (
                <div key={i}>
                    <RatingStar />
                </div>
            ))}

            {hasHalfStar && (
                <div className='relative'>
                    <HalfStar />
                </div>
            )}

            {[...Array(emptyStars)].map((_, i) => (
                <div key={i + fullStars + 1}>
                    <EmptyStar />
                </div>
            ))}
        </div>
    );
};

export default StarRating;
