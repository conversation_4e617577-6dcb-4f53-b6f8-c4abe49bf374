import { CheckCircle } from 'lucide-react';
import Download from '../assets/svg/download';
import Upload from '../assets/svg/upload';
import RightIcon from '@/app/assets/svg/righticon';
import React from 'react'

interface DocumentItemProps {
    title: string;
    issued?: boolean;
    downloadable?: boolean;
    uploadable?: boolean;
    fileName?: string;
  }

export const DocumentItem: React.FC<DocumentItemProps> = ({
    title,
    issued = false,
    downloadable = false,
    uploadable = false,
    fileName,
    }) => {
        return (
            <div className={`flex items-center justify-between px-4 py-5 rounded-[10px] mb-4 ${issued ? 'bg-primaryThree ': 'border border-primaryThree'}`}>
                <div className="flex items-center">
                {issued && 
                <div className='w-5 h-5 flex justify-center items-center rounded-full bg-primaryColor  text-white mr-3'>
                    <RightIcon />
                </div>
                }
                <span className="text-primaryColor font-medium">{title}</span>
                </div>
                {downloadable && fileName && (
                <button className="flex items-center bg-primaryColor text-xs font-medium text-white px-3.5 py-2.5 rounded-full hover:bg-tertiary transition">
                    {fileName}
                    <Download className="ml-1" />
                </button>
                )}
                {uploadable && (
                <button className="flex items-center text-xs font-medium bg-white text-primaryColor border border-primaryColor px-4 py-2 rounded-full hover:bg-primaryFour transition">
                    <Upload className="mr-1 w-4 h-4" />
                    Upload
                </button>
                )}
            </div>
        );
    };
