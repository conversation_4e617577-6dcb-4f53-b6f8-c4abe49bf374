import React from 'react'

const Content = () => {
    return (
        <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_7253_3455" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="14" height="15">
                <rect y="0.5" width="14" height="14" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_7253_3455)">
                <path d="M0.583008 10.5335C0.583008 10.2029 0.668077 9.89912 0.838216 9.62204C1.00836 9.34495 1.2344 9.1335 1.51634 8.98766C2.11912 8.68627 2.73162 8.46023 3.35384 8.30954C3.97606 8.15884 4.60801 8.0835 5.24967 8.0835C5.89134 8.0835 6.52329 8.15884 7.14551 8.30954C7.76773 8.46023 8.38023 8.68627 8.98301 8.98766C9.26495 9.1335 9.49099 9.34495 9.66113 9.62204C9.83127 9.89912 9.91634 10.2029 9.91634 10.5335V11.0002C9.91634 11.321 9.8021 11.5956 9.57363 11.8241C9.34516 12.0526 9.07051 12.1668 8.74967 12.1668H1.74967C1.42884 12.1668 1.15419 12.0526 0.925716 11.8241C0.697244 11.5956 0.583008 11.321 0.583008 11.0002V10.5335ZM12.2497 12.1668H10.7622C10.8691 11.9918 10.9493 11.8047 11.0028 11.6054C11.0563 11.4061 11.083 11.2043 11.083 11.0002V10.4168C11.083 9.98905 10.9639 9.57829 10.7257 9.18454C10.4875 8.79079 10.1497 8.45294 9.71217 8.171C10.208 8.22933 10.6747 8.32898 11.1122 8.46995C11.5497 8.61093 11.958 8.7835 12.3372 8.98766C12.6872 9.18211 12.9545 9.39843 13.1393 9.63662C13.324 9.87482 13.4163 10.1349 13.4163 10.4168V11.0002C13.4163 11.321 13.3021 11.5956 13.0736 11.8241C12.8452 12.0526 12.5705 12.1668 12.2497 12.1668ZM5.24967 7.50016C4.60801 7.50016 4.0587 7.27169 3.60176 6.81475C3.14481 6.3578 2.91634 5.8085 2.91634 5.16683C2.91634 4.52516 3.14481 3.97586 3.60176 3.51891C4.0587 3.06197 4.60801 2.8335 5.24967 2.8335C5.89134 2.8335 6.44065 3.06197 6.89759 3.51891C7.35454 3.97586 7.58301 4.52516 7.58301 5.16683C7.58301 5.8085 7.35454 6.3578 6.89759 6.81475C6.44065 7.27169 5.89134 7.50016 5.24967 7.50016ZM11.083 5.16683C11.083 5.8085 10.8545 6.3578 10.3976 6.81475C9.94065 7.27169 9.39134 7.50016 8.74967 7.50016C8.64273 7.50016 8.50662 7.48801 8.34134 7.4637C8.17606 7.4394 8.03995 7.41266 7.93301 7.3835C8.19551 7.07238 8.39724 6.72725 8.53822 6.34808C8.67919 5.96891 8.74967 5.57516 8.74967 5.16683C8.74967 4.7585 8.67919 4.36475 8.53822 3.98558C8.39724 3.60641 8.19551 3.26127 7.93301 2.95016C8.06912 2.90155 8.20523 2.86995 8.34134 2.85537C8.47745 2.84079 8.61356 2.8335 8.74967 2.8335C9.39134 2.8335 9.94065 3.06197 10.3976 3.51891C10.8545 3.97586 11.083 4.52516 11.083 5.16683ZM1.74967 11.0002H8.74967V10.5335C8.74967 10.4266 8.72294 10.3293 8.66947 10.2418C8.61599 10.1543 8.54551 10.0863 8.45801 10.0377C7.93301 9.77516 7.40315 9.57829 6.86842 9.44704C6.3337 9.31579 5.79412 9.25016 5.24967 9.25016C4.70523 9.25016 4.16565 9.31579 3.63092 9.44704C3.0962 9.57829 2.56634 9.77516 2.04134 10.0377C1.95384 10.0863 1.88336 10.1543 1.82988 10.2418C1.77641 10.3293 1.74967 10.4266 1.74967 10.5335V11.0002ZM5.24967 6.3335C5.57051 6.3335 5.84516 6.21926 6.07363 5.99079C6.3021 5.76232 6.41634 5.48766 6.41634 5.16683C6.41634 4.846 6.3021 4.57134 6.07363 4.34287C5.84516 4.1144 5.57051 4.00016 5.24967 4.00016C4.92884 4.00016 4.65419 4.1144 4.42572 4.34287C4.19724 4.57134 4.08301 4.846 4.08301 5.16683C4.08301 5.48766 4.19724 5.76232 4.42572 5.99079C4.65419 6.21926 4.92884 6.3335 5.24967 6.3335Z" fill="#57585E" />
            </g>
        </svg>

    )
}

export default Content