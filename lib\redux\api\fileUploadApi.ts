import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

export const fileUploadApi = createApi({
  reducerPath: 'fileUploadApi',
  baseQuery: fetchBaseQuery({ baseUrl: process.env.NEXT_PUBLIC_UPLOAD_BASE_URL }),
  endpoints: (builder) => ({
    uploadImage: builder.mutation<string, File>({
      query: (file) => {
        const formData = new FormData();
        formData.append('file', file);

        return {
          url: '/upload',
          method: 'POST',
          body: formData,
        };
      },
    }),
  }),
});

export const { useUploadImageMutation } = fileUploadApi;
