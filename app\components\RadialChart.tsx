'use client'

import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Radial<PERSON><PERSON><PERSON><PERSON> } from 'recharts'
import DateRangeSelect from './DateRangeSelect';
import { ApplicationSourceData } from '@/common';
import {
    Card,
    CardContent,
    CardHeader,
    CardTitle,
} from '@/components/ui/card'
import {
    ChartConfig,
    ChartContainer,
    ChartTooltip,
    ChartTooltipContent,
} from '@/components/ui/chart'


const chartConfig = {
    visitors: {
        label: 'Visitors',
    },
    chrome: {
        label: 'Agencies',
        color: '#144296',
    },
    safari: {
        label: 'Referrals',
        color: '#1952BB',
    },
    firefox: {
        label: 'Direct from Students',
        color: '#1E62E0',
    },
} satisfies ChartConfig
export function RadialChart() {
    const [selectedRange, setSelectedRange] = useState<keyof typeof ApplicationSourceData>('This Year');
    const chartData = ApplicationSourceData[selectedRange];
    const options = ['This Year', 'This Month', 'This Week', 'Today'];
    return (
        <Card className='flex flex-col h-full drop-shadow-none shadow-none border-none'>
            <CardHeader className=''>
                <CardTitle className=' flex md:flex-row flex-col gap-4 justify-between'>
                    <div className='flex items-center gap-2.5 md:text-xl text-base md:leading-[29px] leading-5 text-grayFive font-bold'>
                        <h2>Applications Source</h2>
                    </div>
                    <div className='text-primaryColor md:text-sm text-[10px] md:leading-[17px] leading-3 font-semibold md:block hidden'>
                        <DateRangeSelect
                            options={options}
                            selectedValue={selectedRange}
                            onChange={(value) => setSelectedRange(value as keyof typeof ApplicationSourceData)}
                            placeholder='Select Time Range'
                        />
                    </div>
                </CardTitle>
                {/* <CardDescription>January - June 2024</CardDescription> */}
            </CardHeader>
            <CardContent className='flex-1 pb-0 grid md:grid-cols-2 grid-col-1 gap-4'>
                <div className='flex justify-center'>
                    <ChartContainer
                        config={chartConfig}
                        className='mx-auto aspect-square max-h-[360px] w-full'
                    >
                        <RadialBarChart 
                            endAngle={360} 
                            startAngle={90} 
                            innerRadius={50} 
                            data={chartData} 
                            outerRadius={140} 
                            barCategoryGap={'16%'} 
                        >
                            <ChartTooltip
                                cursor={false}
                                content={<ChartTooltipContent hideLabel nameKey='browser' />}
                            />
                            <RadialBar 
                                dataKey='visitors' 
                                background cornerRadius={10} 
                            />
                        </RadialBarChart>
                    </ChartContainer>
                </div>
                <div className='flex md:flex-col justify-center md:gap-[18px] gap-2.5'>
                    <div className='flex gap-3'>
                        <div className='w-3.5 h-3.5 mt-1 rounded-full bg-[#184EB3]'></div>
                        <div className='flex flex-col gap-2'>
                            <span className='md:text-base text-xs md:leading-5 leading-3 text-grayFour font-medium'>
                                Agencies
                            </span>
                            <span className='text-lg leading-[22px] font-semibold text-graySix'>
                                215+
                            </span>
                        </div>
                    </div>
                    <div className='flex  gap-3'>
                        <div className='w-3.5 h-3.5 mt-1 rounded-full bg-[#1E62E0]'></div>
                        <div className='flex flex-col gap-2'>
                            <span className='md:text-base text-xs md:leading-5 leading-3 text-grayFour font-medium'>
                            Referrals
                            </span>
                            <span className='text-lg leading-[22px] font-semibold text-graySix'>
                                45+
                            </span>
                        </div>
                    </div>
                    <div className='flex gap-3'>
                        <div className='w-3.5 h-3.5 mt-1 rounded-full bg-[#E7EFFF]'></div>
                        <div className='flex flex-col gap-2'>
                            <span className='md:text-base text-xs md:leading-5 leading-3 text-grayFour font-medium'>
                            Direct from Students
                            </span>
                            <span className='text-lg leading-[22px] font-semibold text-graySix'>
                                120+
                            </span>
                        </div>
                    </div>
                </div>
            </CardContent>
            {/* <CardFooter className='flex-col gap-2 text-sm'>
            
            </CardFooter> */}
        </Card>
    )
}
