'use client'
 
import * as React from 'react';
import Pdf from '@/app/assets/svg/pdf';
import Excel from '@/app/assets/svg/excel';
import { Button } from '@/components/ui/button';
import ArrowDown from '@/app/assets/svg/arrowDown';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
 
const DropDownButton = () => {
    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button className='text-primaryColor rounded-[50px] px-4 py-2' variant='outline'>
                    Download <ArrowDown className='text-primaryColor' />
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className='w-32'>
                <Button className='font-normal text-xs text-grayFive hover:bg-primaryOne w-full flex justify-between'>
                    PDF 
                    <Pdf/>
                </Button>
                <Button className='font-normal text-xs text-grayFive hover:bg-primaryOne w-full flex justify-between'>
                    Excel 
                    <Excel/>
                </Button>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}

export default DropDownButton;