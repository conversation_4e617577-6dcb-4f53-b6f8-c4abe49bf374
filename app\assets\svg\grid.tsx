import React from 'react'

const grid = () => {
    return (
        <svg width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_2560_36403" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="21" height="21">
            <rect x="0.882812" y="0.290039" width="20" height="20" fill="#D9D9D9"/>
            </mask>
            <g mask="url(#mask0_2560_36403)">
            <path d="M5.04948 9.45671C4.59115 9.45671 4.19878 9.29351 3.8724 8.96712C3.54601 8.64073 3.38281 8.24837 3.38281 7.79004V4.45671C3.38281 3.99837 3.54601 3.60601 3.8724 3.27962C4.19878 2.95323 4.59115 2.79004 5.04948 2.79004H8.38281C8.84115 2.79004 9.23351 2.95323 9.5599 3.27962C9.88628 3.60601 10.0495 3.99837 10.0495 4.45671V7.79004C10.0495 8.24837 9.88628 8.64073 9.5599 8.96712C9.23351 9.29351 8.84115 9.45671 8.38281 9.45671H5.04948ZM5.04948 17.79C4.59115 17.79 4.19878 17.6268 3.8724 17.3005C3.54601 16.9741 3.38281 16.5817 3.38281 16.1234V12.79C3.38281 12.3317 3.54601 11.9393 3.8724 11.613C4.19878 11.2866 4.59115 11.1234 5.04948 11.1234H8.38281C8.84115 11.1234 9.23351 11.2866 9.5599 11.613C9.88628 11.9393 10.0495 12.3317 10.0495 12.79V16.1234C10.0495 16.5817 9.88628 16.9741 9.5599 17.3005C9.23351 17.6268 8.84115 17.79 8.38281 17.79H5.04948ZM13.3828 9.45671C12.9245 9.45671 12.5321 9.29351 12.2057 8.96712C11.8793 8.64073 11.7161 8.24837 11.7161 7.79004V4.45671C11.7161 3.99837 11.8793 3.60601 12.2057 3.27962C12.5321 2.95323 12.9245 2.79004 13.3828 2.79004H16.7161C17.1745 2.79004 17.5668 2.95323 17.8932 3.27962C18.2196 3.60601 18.3828 3.99837 18.3828 4.45671V7.79004C18.3828 8.24837 18.2196 8.64073 17.8932 8.96712C17.5668 9.29351 17.1745 9.45671 16.7161 9.45671H13.3828ZM13.3828 17.79C12.9245 17.79 12.5321 17.6268 12.2057 17.3005C11.8793 16.9741 11.7161 16.5817 11.7161 16.1234V12.79C11.7161 12.3317 11.8793 11.9393 12.2057 11.613C12.5321 11.2866 12.9245 11.1234 13.3828 11.1234H16.7161C17.1745 11.1234 17.5668 11.2866 17.8932 11.613C18.2196 11.9393 18.3828 12.3317 18.3828 12.79V16.1234C18.3828 16.5817 18.2196 16.9741 17.8932 17.3005C17.5668 17.6268 17.1745 17.79 16.7161 17.79H13.3828ZM5.04948 7.79004H8.38281V4.45671H5.04948V7.79004ZM13.3828 7.79004H16.7161V4.45671H13.3828V7.79004ZM13.3828 16.1234H16.7161V12.79H13.3828V16.1234ZM5.04948 16.1234H8.38281V12.79H5.04948V16.1234Z" fill="#57585E"/>
            </g>
        </svg>
    )
}

export default grid