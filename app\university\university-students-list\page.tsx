"use client"

import React, {useState, useEffect} from 'react';
import { courses, intakes } from '@/common';
import canada from '@/app/assets/svg/canada';
import Heading from '@/app/components/Heading';
import SortIcon from '@/app/assets/svg/SortIcon';
import InputField from '@/app/components/InputField';
import Pagination from '@/app/components/Pagination';
import SelectField from '@/app/components/SelectField';
import MultiSelect from '@/app/components/MultiSelect';
import FilterLists from '@/app/components/FilterLists';
import DropDownButton from '@/app/components/DropDownButton';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import MultiSelectWithGroup from '@/app/components/MultiSelectWithGroup';

const page = () => {
    const itemsPerPage = 2;
    const [currentPage, setCurrentPage] = useState(1);
    const totalPages = Math.ceil(courses.length / itemsPerPage);

    // const currentCources = courses.slice(
    //     (currentPage - 1) * itemsPerPage,
    //     currentPage * itemsPerPage
    // );

    const [filters, setFilters] = useState({
        search: '',
        intake: [] as string[],
        program: [] as string[],
    });

    const goToPage = (page: number) => {
        setCurrentPage(page);
    };

    const filterLists =['2 year Undergraduate Diploma', 'Dec 2024', 'Pending', 'USA'];
    const programs = [
        { value: '2-year-undergraduate-diploma', label: '2 year Undergraduate Diploma' },
        { value: '3-year-undergraduate-diploma', label: '3 year Undergraduate Diploma' },
        { value: '3-year-bachelor’s-degree', label: '3 year Bachelor’s Degree' }
    ];

    const tableHeadData = [
        'SID',
        'Name',
        'Program',
        'Intake',
        'Enrollment Date',
        'Nationality',
        'Email',
        'Mobile',
        'Admission Type',
        'Status',
        'Last Updated',
    ];

    const studentLists = [
        {
            SID: 262,
            name: 'Nolan Westervelt',
            program: '2 year Undergraduate Diploma',
            intake: 'Oct 2024',
            enrollmentDate: '22 Oct 2023',
            nationality: canada,
            email: '<EMAIL>',
            mobile: '+8801962-446543',
            admissionType: 'Full-Time',
            status: 'On Hold',
            lastUpdated: '22 Oct 2023'
        }
    ];

    const statusColor = (status: string) => {
        switch (status) {
            case 'On Hold':
                return 'bg-[#F7E1C140] text-[#F2A735]';
            case 'Withdrawn':
                return 'bg-[#EFF0F0] text-[#9FA0A6]';
            case 'Graduated':
                return 'bg-[#ECFDF3] text-[#027A48]';
            case 'Active':
                return 'bg-[#1E62E01A] text-[#1E62E0]';
            case 'Transferred':
                return 'bg-[#AB47BC1A] text-[#AB47BC]';
            default:
                return 'Unknown status.';
        }
    };

    
    // const handleInputChange = (key: string, value: any) => {
    //     setFilters((prev) => ({
    //         ...prev,
    //         [key]: value,
    //     }));
    // };

    // const [activeFilters, setActiveFilters] = useState<string[]>(['2 year Undergraduate Diploma', 'Dec 2024', 'Pending', 'USA']);
     // Handle filter changes
     const handleFilterChange = (key: string, value: any) => {
        setFilters(prev => ({
            ...prev,
            [key]: value,
        }));
        
        // Update active filters if needed
        // if (key === 'program') {
        //     // Remove old program filters and add new ones
        //     const updatedFilters = activeFilters.filter(filter => 
        //         !prev.program.includes(filter) || value.includes(filter)
        //     );
            
        //     // Add new selected programs to filters
        //     const newFilters = [...updatedFilters, ...value.filter(
        //         (item: string) => !updatedFilters.includes(item)
        //     )];
            
        //     setActiveFilters(newFilters);
        // }
    };
    
    // Program options structured for MultiSelectWithGroup
    const programGroups = [
        {
            label: 'Undergraduate',
            selectItem: [
                '2 year Undergraduate Diploma',
                '3 year Undergraduate Diploma',
                `3 year Bachelor's Degree`,
                `4 year Bachelor's Degree`
            ]
        },
        {
            label: 'Post-graduate',
            selectItem: [
                `1 year Master's Degree`,
                `2 year Master's Degree`
            ]
        },
        {
            label: 'Other',
            selectItem: [
                'Doctoral',
                'PhD',
                'Non-credential'
            ]
        }
    ];
    
    const handleProgramChange = (selectedValues: string[]) => {
        handleFilterChange('program', selectedValues);
    };

    return (
        <DashboardLayout>
            <div>
                <Heading level='h1'>
                    Students
                </Heading>
            </div>
            <div className='grid grid-cols-6 gap-3 mt-5'>
                <div>
                    <InputField 
                        type='text'
                        id='id/name/mobile'
                        label='ID/Name/Mobile'
                        placeholder='+880 1843789043'
                    />
                </div>
                <div>
                    <MultiSelect
                        options={intakes}
                        selectedValues={filters.intake}
                        onChange={(value) => handleFilterChange('intake', value)}
                        label='Intake'
                    />
                </div>
                <div>
                    <MultiSelectWithGroup 
                        selectedLists={programGroups}
                        label="Program"
                        placeholder="Select program"
                        onChange={handleProgramChange}
                        initialValues={filters.program}
                    />
                </div>
                {/* <SelectField 
                    label='Program'
                    options={programs}
                /> */}
            </div>
            <div className='mt-5'>
                <FilterLists lists={filterLists} />
            </div>
            <div className='mt-10 mb-5 flex justify-between'>
                <Heading level='h2'>
                    All Students
                </Heading>
                <DropDownButton />
            </div>
            <div className='overflow-x-auto'>
                <table className='bg-white min-w-full rounded-[20px] text-sm text-left'>
                    <thead>
                        <tr>
                            {tableHeadData.map((thead, index) => (
                                <th 
                                    key={index}
                                    className={`${ index === 0 ? 'flex items-center gap-0.5 cursor-pointer': ''} py-3.5 px-6 font-bold text-xs tracking-[0.4px] text-grayFive`}
                                >
                                    {thead}
                                    {index === 0 && (
                                        <SortIcon />
                                    )}
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody className='border-t border-grayOne divide-y divide-grayOne'>
                        {studentLists.map((list, index) => (
                            <tr key={index}>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{list.SID}</td>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{list.name}</td>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{list.program} </td>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{list.intake}</td>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{list.enrollmentDate}</td>
                                <td className='text-xs leading-5 text-graySix px-10 py-3.5'>{<list.nationality />}</td>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{list.email}</td>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{list.mobile}</td>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{list.admissionType}</td>
                                <td className='px-6'>
                                    <span className={`rounded-[16px] font-medium text-xs ${statusColor(list.status)} leading-4 py-[2px] px-2`}>{list.status}</span>
                                </td>
                                <td className='text-xs leading-5 text-graySix px-6 py-3.5 font-normal'>{list.lastUpdated}</td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            <div className='pb-24 pt-12'>
                <Pagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={goToPage}
                />
            </div>
        </DashboardLayout>
    )
}

export default page
