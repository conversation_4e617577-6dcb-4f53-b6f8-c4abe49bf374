"use client";

import React, { useState } from "react";

const cn = (...classes: (string | boolean | undefined)[]) => {
  return classes.filter(Boolean).join(" ");
};

const defaultData = [
  { label: "Lead", value: 11856 },
  { label: "Visitors", value: 7568 },
  { label: "Prospect", value: 4555 },
  { label: "Student/Applicant", value: 3268 },
];

interface LeadDropOffsChartProps {
  title?: string;
  data?: { label: string; value: number }[];
  height?: number;
  colors?: string[][];
  unit?: string;
  valueFormatter?: (value: number) => string;
  periodOptions?: string[];
  defaultPeriod?: string;
}

export default function LeadDropOffsChart({
  title = "Lead drop-offs",
  data,
  height = 240,
  colors = [
    ["#93C5FD", "#3B82F6"],
    ["#60A5FA", "#2563EB"],
    ["#3B82F6", "#1D4ED8"],
    ["#2563EB", "#1E40AF"],
    ["#2563EB", "#1E40AF"],
    ["#2563EB", "#1E40AF"],
  ],
  unit = "",
  valueFormatter = (v) => v.toLocaleString(),
  periodOptions = ["Daily", "Weekly", "Monthly", "Quarterly", "Yearly"],
  defaultPeriod = "Monthly"
}: LeadDropOffsChartProps) {
  const [selectedPeriod, setSelectedPeriod] = useState(defaultPeriod);
  const chartData = data && data.length > 0 ? data : defaultData;
  const max = Math.max(...chartData.map(d => d.value));

  const getHeight = (value: number) => {
    return (value / max) * (height - 50);
  };

  const getGradientId = (index: number) => `blue-grad-${index}`;

  const segmentWidth = 100 / chartData.length;

  const segments = chartData.map((item, index) => {
    const segmentHeight = getHeight(item.value);
    const startX = index * segmentWidth;
    const endX = (index + 1) * segmentWidth;

    let startHeight;
    if (index === 0) {
      startHeight = segmentHeight * 1.25;
    } else {
      startHeight = getHeight(chartData[index - 1].value);
    }

    const endHeight = segmentHeight;
    const middleX = (startX + endX) / 2;
    const controlPointY = height - (startHeight + endHeight) / 1.9 + 10;

    return {
      index,
      item,
      path: `
        M ${startX} ${height - startHeight}
        Q ${middleX} ${controlPointY} ${endX} ${height - endHeight}
        L ${endX} ${height}
        L ${startX} ${height}
        Z
      `
    };
  });

  return (
    <div className="p-6 bg-white rounded-3xl shadow-sm">
      <div className="flex items-center justify-between mb-8">
        <h2 className="text-2xl font-medium text-gray-700">{title}</h2>
      </div>

      <div className="flex justify-self-center gap-4 mb-4 w-full">
        {chartData.map((item, index) => (
          <div key={item.label} className="flex flex-col" style={{ width: `${segmentWidth}%` }}>
            <div className="text-gray-600 text-sm">{item.label}</div>
            <div className="text-gray-800 text-3xl font-semibold mt-1">
              {unit}{valueFormatter(item.value)}
            </div>
          </div>
        ))}
      </div>

      <div className="relative w-full mt-8" style={{ height }}>
        <svg width="100%" height={height} viewBox={`0 0 100 ${height}`} preserveAspectRatio="none">
          <defs>
            {colors.map(([from, to], index) => (
              <linearGradient id={getGradientId(index)} key={index} x1="0%" y1="0%" x2="0%" y2="100%">
                <stop offset="0%" stopColor={from} />
                <stop offset="100%" stopColor={to} />
              </linearGradient>
            ))}
          </defs>

          {segments.map((seg) => (
            <path
              key={seg.index}
              d={seg.path}
              fill={`url(#${getGradientId(seg.index)})`}
              className="transition-all duration-500 ease-in-out"
            />
          ))}
        </svg>
      </div>
    </div>
  );
}
