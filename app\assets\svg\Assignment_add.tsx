import React from 'react';

const Assignment_add = () => {
    return (
        <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_8510_25714" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="14" height="15">
                <rect y="0.5" width="14" height="14" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_8510_25714)">
                <path d="M2.91667 12.7502C2.59583 12.7502 2.32118 12.6359 2.09271 12.4075C1.86424 12.179 1.75 11.9043 1.75 11.5835V3.41683C1.75 3.096 1.86424 2.82134 2.09271 2.59287C2.32118 2.3644 2.59583 2.25016 2.91667 2.25016H5.36667C5.49306 1.90016 5.70451 1.61822 6.00104 1.40433C6.29757 1.19044 6.63056 1.0835 7 1.0835C7.36944 1.0835 7.70243 1.19044 7.99896 1.40433C8.29549 1.61822 8.50694 1.90016 8.63333 2.25016H11.0833C11.4042 2.25016 11.6788 2.3644 11.9073 2.59287C12.1358 2.82134 12.25 3.096 12.25 3.41683V6.34808C12.25 6.51336 12.1941 6.6519 12.0823 6.7637C11.9705 6.87551 11.8319 6.93141 11.6667 6.93141C11.5014 6.93141 11.3628 6.87551 11.251 6.7637C11.1392 6.6519 11.0833 6.51336 11.0833 6.34808V3.41683H2.91667V11.5835H5.83333C5.99861 11.5835 6.13715 11.6394 6.24896 11.7512C6.36076 11.863 6.41667 12.0016 6.41667 12.1668C6.41667 12.3321 6.36076 12.4706 6.24896 12.5825C6.13715 12.6943 5.99861 12.7502 5.83333 12.7502H2.91667ZM2.91667 11.5835V3.41683V6.96058V6.91683V11.5835ZM4.66667 10.4168H6.125C6.29028 10.4168 6.42882 10.3609 6.54063 10.2491C6.65243 10.1373 6.70833 9.99877 6.70833 9.8335C6.70833 9.66822 6.65243 9.52968 6.54063 9.41787C6.42882 9.30607 6.29028 9.25016 6.125 9.25016H4.66667C4.50139 9.25016 4.36285 9.30607 4.25104 9.41787C4.13924 9.52968 4.08333 9.66822 4.08333 9.8335C4.08333 9.99877 4.13924 10.1373 4.25104 10.2491C4.36285 10.3609 4.50139 10.4168 4.66667 10.4168ZM4.66667 8.0835H7.58333C7.74861 8.0835 7.88715 8.02759 7.99896 7.91579C8.11076 7.80398 8.16667 7.66544 8.16667 7.50016C8.16667 7.33489 8.11076 7.19634 7.99896 7.08454C7.88715 6.97273 7.74861 6.91683 7.58333 6.91683H4.66667C4.50139 6.91683 4.36285 6.97273 4.25104 7.08454C4.13924 7.19634 4.08333 7.33489 4.08333 7.50016C4.08333 7.66544 4.13924 7.80398 4.25104 7.91579C4.36285 8.02759 4.50139 8.0835 4.66667 8.0835ZM4.66667 5.75016H9.33333C9.49861 5.75016 9.63715 5.69426 9.74896 5.58245C9.86076 5.47065 9.91667 5.33211 9.91667 5.16683C9.91667 5.00155 9.86076 4.86301 9.74896 4.7512C9.63715 4.6394 9.49861 4.5835 9.33333 4.5835H4.66667C4.50139 4.5835 4.36285 4.6394 4.25104 4.7512C4.13924 4.86301 4.08333 5.00155 4.08333 5.16683C4.08333 5.33211 4.13924 5.47065 4.25104 5.58245C4.36285 5.69426 4.50139 5.75016 4.66667 5.75016ZM7 2.97933C7.12639 2.97933 7.2309 2.93801 7.31354 2.85537C7.39618 2.77273 7.4375 2.66822 7.4375 2.54183C7.4375 2.41544 7.39618 2.31093 7.31354 2.22829C7.2309 2.14565 7.12639 2.10433 7 2.10433C6.87361 2.10433 6.7691 2.14565 6.68646 2.22829C6.60382 2.31093 6.5625 2.41544 6.5625 2.54183C6.5625 2.66822 6.60382 2.77273 6.68646 2.85537C6.7691 2.93801 6.87361 2.97933 7 2.97933Z" fill="#57585E" />
                <path d="M10.5036 13.91C10.1001 13.91 9.72094 13.8334 9.36608 13.6803C9.01122 13.5272 8.70254 13.3194 8.44004 13.0569C8.17754 12.7944 7.96973 12.4857 7.8166 12.1308C7.66348 11.776 7.58691 11.3968 7.58691 10.9933C7.58691 10.5899 7.66348 10.2107 7.8166 9.85583C7.96973 9.50097 8.17754 9.19229 8.44004 8.92979C8.70254 8.66729 9.01122 8.45947 9.36608 8.30635C9.72094 8.15322 10.1001 8.07666 10.5036 8.07666C10.9071 8.07666 11.2862 8.15322 11.6411 8.30635C11.9959 8.45947 12.3046 8.66729 12.5671 8.92979C12.8296 9.19229 13.0374 9.50097 13.1906 9.85583C13.3437 10.2107 13.4202 10.5899 13.4202 10.9933C13.4202 11.3968 13.3437 11.776 13.1906 12.1308C13.0374 12.4857 12.8296 12.7944 12.5671 13.0569C12.3046 13.3194 11.9959 13.5272 11.6411 13.6803C11.2862 13.8334 10.9071 13.91 10.5036 13.91ZM10.4963 13.035C10.5643 13.035 10.6239 13.0095 10.6749 12.9584C10.726 12.9074 10.7515 12.8478 10.7515 12.7798V12.6704C10.9946 12.6267 11.2036 12.5319 11.3786 12.386C11.5536 12.2402 11.6411 12.0239 11.6411 11.7371C11.6411 11.5329 11.5827 11.3458 11.4661 11.1756C11.3494 11.0055 11.1161 10.8572 10.7661 10.7308C10.4744 10.6336 10.2727 10.5485 10.1609 10.4756C10.0491 10.4027 9.99316 10.303 9.99316 10.1767C9.99316 10.0503 10.0381 9.95062 10.1281 9.8777C10.218 9.80479 10.348 9.76833 10.5182 9.76833C10.6154 9.76833 10.7005 9.78534 10.7734 9.81937C10.8463 9.8534 10.9071 9.89958 10.9557 9.95791C11.0043 10.0162 11.059 10.0563 11.1197 10.0782C11.1805 10.1001 11.2376 10.0989 11.2911 10.0746C11.364 10.0454 11.4138 9.99558 11.4406 9.9251C11.4673 9.85461 11.4612 9.7902 11.4223 9.73187C11.3446 9.62006 11.2485 9.52527 11.1343 9.44749C11.0201 9.36972 10.8973 9.32597 10.7661 9.31624V9.20687C10.7661 9.13881 10.7406 9.07926 10.6895 9.02822C10.6385 8.97718 10.5789 8.95166 10.5109 8.95166C10.4428 8.95166 10.3833 8.97718 10.3322 9.02822C10.2812 9.07926 10.2557 9.13881 10.2557 9.20687V9.31624C10.0126 9.36972 9.82303 9.47666 9.68691 9.63708C9.5508 9.79749 9.48275 9.97735 9.48275 10.1767C9.48275 10.4051 9.54959 10.5899 9.68327 10.7308C9.81695 10.8718 10.0272 10.9933 10.314 11.0954C10.6202 11.2072 10.8329 11.3069 10.952 11.3944C11.0711 11.4819 11.1307 11.5961 11.1307 11.7371C11.1307 11.8975 11.0735 12.0154 10.9593 12.0907C10.8451 12.1661 10.7077 12.2037 10.5473 12.2037C10.4209 12.2037 10.3067 12.1734 10.2046 12.1126C10.1025 12.0518 10.0175 11.9607 9.94941 11.8392C9.91053 11.7711 9.85948 11.7249 9.79629 11.7006C9.73309 11.6763 9.6699 11.6763 9.60671 11.7006C9.53865 11.7249 9.48882 11.7711 9.45723 11.8392C9.42563 11.9072 9.42441 11.9728 9.45358 12.036C9.53136 12.2013 9.63587 12.3362 9.76712 12.4407C9.89837 12.5452 10.0564 12.6169 10.2411 12.6558V12.7798C10.2411 12.8478 10.2666 12.9074 10.3176 12.9584C10.3687 13.0095 10.4282 13.035 10.4963 13.035Z" fill="#57585E" />
            </g>
        </svg>

    )
}

export default Assignment_add