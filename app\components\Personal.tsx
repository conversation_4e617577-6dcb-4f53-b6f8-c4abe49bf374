import Image from 'next/image';
import { FormData } from '@/types';
import InputField from './InputField';
import SelectField from './SelectField';
import MultiSelect from './MultiSelect';
import Plus from '@/app/assets/svg/plus';
import { DatePicker } from './DatePicker';
import NoteTextarea from './NoteTextarea';
import SelectedItem from './SelectedItem';
import 'react-phone-number-input/style.css';
import { CheckboxGroup } from './CheckboxGroup';
import PhoneInput from 'react-phone-number-input';
import PhoneNumberInput from './PhoneNumberInput';
import Email from '@/app/assets/svg/email.svg';
import React, { useState, useEffect } from 'react';
import SectionLayout from './layout/SectionLayout';
// import PlusIcon from '@/app/assets/svg/plus.svg';
import InputFieldWithIcon from './InputFieldWithIcon';
import EmailInbox from '@/app/assets/svg/emailInbox';
import InputfieldWithButton from './InputfieldWithButton';
import { Country, State, City } from 'country-state-city';
import LinkedinIcon from '@/app/assets/svg/linkedin';
import SelectAndSearchCombobox from './SelectAndSearchCombobox';
import ArrowOutward from '@/app/assets/svg/ArrowOutward';
import { useForm, SubmitHandler, Controller  } from 'react-hook-form';
import { 
    subjects, 
    maritalStatus, 
    gender, 
    frameworks, 
    personalInfo, 
    socialPlatform,
    Reference,
    prefferedCountry ,
    socialLinks
} from '@/common';
import {
    Avatar,
    AvatarFallback,
    AvatarImage,
  } from '@/components/ui/avatar';

const Personal = () => {
    const [selected, setSelected] = React.useState("")
    const [showAddSocialLink, setShowAddSocialLink] = useState(false)
    const [selectedCountries, setSelectedCountries] = useState<string[]>([]);
    const [selectedSubject, setSelectedSubject] = useState<string[]>([]);
    const countriesList = Country.getAllCountries();
    const [states, setStates] = useState(State.getStatesOfCountry(countriesList[0].isoCode));
    const { 
        register, 
        control, 
        getValues, 
        setValue, 
        watch, 
        handleSubmit, 
        formState: { errors } 
    } = useForm<FormData>({
        defaultValues: {
            present_country: countriesList[0].isoCode,
            present_state: states[0]?.isoCode,
            permanent_country: countriesList[0].isoCode,
            permanent_state: states[0]?.isoCode,
            prefferedCountry: [],
            preferred_subject: [],
            // socialLinks: [],
            selectedPlatform: socialPlatform[0].value,
        },
    });
    const selectedGender = watch('gender');
    const selectedPresentCountry = watch('present_country');
    const selectedPermanentCountry = watch('permanent_country');

    useEffect(() => {
        if (selectedPresentCountry) {
            const fetchedStates = State.getStatesOfCountry(selectedPresentCountry);
            setStates(fetchedStates);

            if (fetchedStates.length > 0) {
                setValue('present_state', fetchedStates[0].isoCode);
            } else {
                setValue('present_state', ''); 
            }
        }
    }, [selectedPresentCountry, setValue]);

    useEffect(() => {
        if (selectedPermanentCountry) {
            const fetchedStates = State.getStatesOfCountry(selectedPermanentCountry);
            setStates(fetchedStates);

            if (fetchedStates.length > 0) {
                setValue('permanent_state', fetchedStates[0].isoCode);
            } else {
                setValue('permanent_state', ''); 
            }
        }
    }, [selectedPermanentCountry, setValue]);

    const onSubmit: SubmitHandler<FormData> = (data) => {
        console.log(data);
        return data;
    };
    
    type FieldName = keyof FormData;

    const removeOption = (fieldName: FieldName, valueToRemove: string) => {
        const currentValues = getValues(fieldName) as string[]; 
        if (Array.isArray(currentValues)) {
            const updatedValues = currentValues.filter((item) => item !== valueToRemove);
            setValue(fieldName, updatedValues);
        }
        // console.log('remove')
    };

    const handleMultiSelectChange = (fieldName: FieldName, newValues: string[]) => {
        setValue(fieldName, newValues);
        setSelectedCountries(newValues)
    };
    

    return (
        <SectionLayout heading={'Personal Info'}>
            <form onSubmit={handleSubmit(onSubmit)}>   
                <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
                    <div>
                        <InputField 
                            type='text' 
                            id='last_name'
                            placeholder='Last Name' 
                            label='Last Name / Surname' 
                            register={register('last_name', { required: 'Last name is required' })}
                            errorMessage={errors.last_name?.message}
                        />
                    </div>
                    <div>
                        <InputField 
                            id='first_name'
                            placeholder='First Name' 
                            type='text' 
                            register={register('first_name', { required: 'First name is required' })}
                            label='First Name / Given Name' 
                        />
                    </div>
                    <div>
                        <InputField 
                            id='language'
                            type='text' 
                            label='Name in Native Language (Bangla / Others)' 
                            register={register('language')}
                        />
                    </div>
                    <div>
                        <InputFieldWithIcon 
                            label='Email' 
                            placeholder='Email'
                            icon={<EmailInbox />}
                            type='email'
                            register={register('email', { required: 'Email is required' })}
                        />
                    </div>                    
                    <div>
                        <Controller
                            name='dob'
                            control={control}
                            render={({ field }) => (
                                <DatePicker 
                                    value={field.value}
                                    onChange={field.onChange}  
                                />
                            )}
                        />
                    </div>
                    <div>
                        <Controller
                            name='gender'
                            control={control}
                            render={({ field }) => (
                            <CheckboxGroup
                                options={gender}
                                value={field.value}
                                onChange={field.onChange}
                            />
                            )}
                        />
                    </div>
                    <div>
                        <InputField 
                            id='fathers_name'
                            placeholder='Father’s Name' 
                            type='text' 
                            register={register('fathers_name')}
                            label='Father’s Name' 
                        />
                    </div>
                    <div>
                        <InputField 
                            id='mothers_name'
                            register={register('mothers_name')}
                            placeholder='Mother’s Name' 
                            type='text' 
                            label='Mother’s Name' 
                        />
                    </div>
                    <div>
                        <InputField 
                            id='national_id'
                            label='National ID (NID)'
                            placeholder='Enter your national id number' 
                            type='number' 
                            register={register('national_id')}
                        />
                    </div>
                    <div>
                        <InputField 
                            id='passport_number'
                            placeholder='Enter your passport number' 
                            type='number' 
                            register={register('passport_number')}
                            label='Passport Number' 
                        />
                    </div>
                    <div className='flex flex-col md:flex-row gap-6'>
                        <div className='md:w-[30%] w-full'>
                            <Controller
                                name='marital_status'
                                control={control}
                                defaultValue=''
                                render={({ field }) => (
                                    <SelectField
                                        label='Marital Status'
                                        options={maritalStatus}
                                        value={field.value}
                                        onChange={field.onChange}
                                    />
                                )}
                            />
                        </div>
                        <div className='md:w-[70%] w-full'>
                            <InputField 
                                id='spouse_name'
                                register={register('spouse_name')}
                                placeholder='Enter spouse name' 
                                type='text' 
                                label='Spouse Name' 
                            />
                        </div>
                    </div>
                    <div>
                        <InputField 
                            id='spouse_passport_number'
                            placeholder='Enter spouse passport number' 
                            type='number' 
                            register={register('spouse_passport_number')}
                            label='Spouse Passport Number' 
                        />
                    </div>
                    <div className=''>
                        <InputField 
                            id='present_address'
                            type='text' 
                            register={register('present_address')}
                            label='Present Address' 
                        />
                        <div className='flex flex-col md:flex-row gap-6 md:mt-6'>
                            <div className='w-full md:w-[70%]'>
                                {/* <Controller
                                    name='present_country'
                                    control={control}
                                    render={({ field }) => (
                                        // <SelectAndSearchCombobox
                                        //     selectLists={countriesList}
                                        //     label='Country'
                                        //     type='select'
                                        //     field={field}
                                        // />
                                        <SelectAndSearchCombobox
                                            options={countriesList}
                                            label='Country'
                                            type='select'
                                            selectedValue={selected}
                                            onChange={setSelected}
                                            placeholder="Choose a university"
                                        />
                                    )}
                                /> */}
                                {/* <SelectAndSearchCombobox
                                    options={countriesList}
                                    label='Country'
                                    type='select'
                                    selectedValue={selected}
                                    onChange={setSelected}
                                    placeholder="Choose a university"
                                /> */}
                            </div>
                            <div className='w-full md:w-[30%]'>
                                {/* <Controller
                                    name='present_state'
                                    control={control}
                                    render={({ field }) => (
                                        <SelectAndSearchCombobox
                                            selectLists={states}
                                            label='State'
                                            type='select'
                                            field={field}
                                        />
                                    )}
                                /> */}
                                {/* <SelectAndSearchCombobox
                                    options={states}
                                    label='Country'
                                    type='select'
                                    selectedValue={selected}
                                    onChange={setSelected}
                                    placeholder="Choose a university"
                                /> */}
                            </div>
                        </div>
                        <div className='flex flex-col md:flex-row gap-6'>
                            <div className='w-full md:w-[70%]'>
                                <InputField 
                                    id='city'
                                    type='text' 
                                    register={register('present_address_city')}
                                    label='City' 
                                    placeholder='dhaka' 
                                />
                            </div>
                            <div className='w-full md:w-[30%]'>
                                <InputField 
                                    id='zip_code'
                                    type='number' 
                                    register={register('present_address_zip_code')}
                                    label='Zip Code' 
                                    placeholder='3700' 
                                />
                            </div>
                        </div>
                    </div>
                    <div className=''>
                        <InputField 
                            id='permanent_address'
                            register={register('permanent_address')}
                            type='text' 
                            label='Permanent Address' 
                            switcher={true}
                        />
                        <div className='flex flex-col md:flex-row gap-6 md:mt-6'>
                            <div className='md:w-[70%] w-full'>
                                {/* <Controller
                                    name='permanent_country'
                                    control={control}
                                    render={({ field }) => (
                                        <SelectAndSearchCombobox
                                            selectLists={countriesList}
                                            label='State'
                                            type='select'
                                            field={field}
                                        />
                                    )}
                                /> */}
                            </div>
                            <div className='md:w-[30%] w-full'>
                                {/* <Controller
                                    name='permanent_state'
                                    control={control}
                                    render={({ field }) => (
                                        <SelectAndSearchCombobox
                                            selectLists={states}
                                            label='State'
                                            type='select'
                                            field={field}
                                        />
                                    )}
                                /> */}
                            </div>
                        </div>
                    
                        <div className='flex flex-col md:flex-row gap-6'>
                            <div className='w-full md:w-[70%]'>
                                <InputField 
                                    id='city'
                                    register={register('permanent_address_city')}
                                    placeholder='dhaka' 
                                    type='text' 
                                    label='City' 
                                />
                            </div>
                            <div className='w-full md:w-[30%]'>
                                <InputField 
                                    id='zip_code'
                                    placeholder='3700' 
                                    type='number' 
                                    register={register('permanent_address_zip_code')}
                                    label='Zip Code'
                                />
                            </div>
                        </div>
                    </div>
                    <div>
                        <InputField 
                            id='sponsor_name'
                            register={register('sponsor_name')}
                            placeholder='name' 
                            type='text' 
                            label='Sponsor Name' 
                        />
                    </div>
                    <div>
                        <InputField 
                            id='relationship'
                            register={register('relationship')}
                            placeholder='Enter spouse name' 
                            type='text' 
                            label='Relationship' 
                        />
                    </div>
                    
                    <div>
                        <Controller
                            name='phoneNumber'
                            control={control}
                            rules={{ required: 'Phone number is required' }}
                            render={({ field }) => (
                                <PhoneNumberInput
                                    {...field}
                                    label='Phone Number'
                                    className='border border-tertiary border-opacity-20 focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 py-2.5 px-3.5 rounded-lg w-full'
                                />
                            )}
                        />
                    </div>

                    <div>
                        <Controller
                            name='gurdian_phone_number'
                            control={control}
                            rules={{ required: 'Phone number is required' }}
                            render={({ field }) => (
                                <PhoneNumberInput
                                    {...field}
                                    label='Gurdian Phone Number'
                                    className='border border-tertiary border-opacity-20 focus-within:outline-none focus-within:ring-1 focus-within:ring-primaryColor focus-within:ring-opacity-60 py-2.5 px-3.5 rounded-lg w-full'
                                />
                            )}
                        />
                    </div>
                    <div>
                        <Controller
                            name='preferred_subject'
                            control={control}
                            render={({ field }) => (
                            <MultiSelect
                                options={subjects}
                                selectedValues={field.value || []}
                                onChange={(values) =>
                                    handleMultiSelectChange('preferred_subject', values)
                                }
                                placeholder='Select items'
                                label='Preferred Subject'
                            />
                        )}
                        />
                        <SelectedItem 
                            selectedOptions={getValues('preferred_subject') || []}
                            subjects={subjects}
                            removeOption={(value: string) =>
                                removeOption('preferred_subject', value)
                            }
                        />
                    </div>
                    <div>
                        <Controller
                            name='prefferedCountry'
                            control={control}
                            render={({ field }) => (
                            <MultiSelect
                                options={prefferedCountry}
                                selectedValues={field.value || []}
                                onChange={(values) =>
                                    handleMultiSelectChange(field.name, values)
                                }
                                placeholder='Select items'
                                label='Preffered Country'
                            />
                            )}
                        />
                        <SelectedItem 
                            selectedOptions={getValues('prefferedCountry') || []}
                            subjects={prefferedCountry}
                            removeOption={(value: string) =>
                                removeOption('prefferedCountry', value)
                            }
                        />
                    </div>
                    <div className='flex flex-col md:flex-row w-full gap-6'>
                        <div className='md:w-[25%] w-full'>
                            <Controller
                                name='selectedPlatform'
                                control={control}
                                defaultValue=''
                                render={({ field }) => (
                                    <SelectField
                                        label='Social Links'
                                        options={socialPlatform}
                                        value={field.value}
                                        onChange={field.onChange}
                                    />
                                )}
                            />
                        </div>
                        <div className='md:w-[75%] w-full'>
                            <InputfieldWithButton
                                name="socialLink"
                                setValue={setValue}
                                getValues={getValues}
                                placeholder="Profile link paste here"
                            />
                        </div>
                    </div>
                    {/* <ul className="list-disc list-inside text-gray-700">
                        {socialLinks.map((link, index) => (
                            <li key={index}>
                                <strong>{link.platform}:</strong> {link.url}
                            </li>
                        ))}
                    </ul> */}
                    <div>
                        <Controller
                            name='reference'
                            control={control}
                            render={({ field }) => (
                            <MultiSelect
                                options={Reference}
                                selectedValues={field.value || []}
                                onChange={(values) =>
                                    handleMultiSelectChange(field.name, values)
                                }
                                placeholder='Select items'
                                label='Reference'
                            />
                            )}
                        />
                        <SelectedItem 
                            selectedOptions={getValues('reference') || []}
                            subjects={Reference}
                            removeOption={(value: string) =>
                                removeOption('reference', value)
                            }
                        />
                    </div>
                    <div className='col-span-1 md:col-span-2'>
                        <NoteTextarea
                            register={register('note')}
                        />
                    </div>
                 
                 <button
                    type='submit'
                    className='w-full bg-indigo-600 text-white text-sm font-medium py-2 px-4 rounded-md hover:bg-indigo-700 transition focus:outline-none focus:ring-2 focus:ring-indigo-500'
                >
                    Submit
                </button>
                </div>
            </form>
            <h3 className='pb-6 font-semibold text-xl leading-6 text-graySix mt-10'>Personal  Information</h3>

            <div className='rounded-lg border p-5 border-tertiary border-opacity-20'>
                <div className='grid grid-cols-1 sm:grid-cols-2 gap-y-4'>
                    {personalInfo.map((info, index) => (
                        <div key={index} className='flex'>
                            <span className='font-normal text-base leading-5 text-graySix w-1/3'>{info.label}</span>
                            <span className='font-normal text-base leading-5 text-graySix w-2/3'>: 
                                <span className='ml-3'>{info.value}</span>
                            </span>
                        </div>
                    ))}
                </div>
            </div>
            <div className='flex py-6 items-center gap-6'>
                <h3 className='font-semibold text-xl leading-6 text-graySix'>Social Links</h3>
                <div>
                    <button 
                        onClick={() => setShowAddSocialLink(true)} 
                        className='flex gap-[2px] items-center font-semibold text-xs leading-4 text-tertiary'
                    >
                        <Plus />
                        Add social link
                    </button>
                </div>
            </div>
            {showAddSocialLink && (
                <div className='mb-6 flex flex-col md:flex-row w-full gap-6'>
                    <div className='md:w-[50%] w-full'>
                        <Controller
                            name='selectedPlatform'
                            control={control}
                            defaultValue=''
                            render={({ field }) => (
                                <SelectField
                                    // label='Social Links'
                                    options={socialPlatform}
                                    value={field.value}
                                    onChange={field.onChange}
                                />
                            )}
                        />
                    </div>
                    <div className='md:w-[50%] w-full'>
                        <InputfieldWithButton
                            name="socialLink"
                            setValue={setValue}
                            getValues={getValues}
                            placeholder="Profile link paste here"
                        />
                    </div>
                </div>
            )}
            <div className='grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6'>
                {socialLinks.map((social, index) => (
                    <a
                        key={index}
                        href={social.link}
                        target='_blank'
                        rel='noopener noreferrer'
                        className='flex border border-tertiary border-opacity-20 items-center px-5 py-3 rounded-lg transition'
                    >
                        <social.icon />
                        <span className='ml-3.5 flex-1 text-gray-800 font-medium'>{social.username}</span>
                        <ArrowOutward />
                        
                    </a>
                ))}
            </div>
        </SectionLayout>
        
    )
}

export default Personal