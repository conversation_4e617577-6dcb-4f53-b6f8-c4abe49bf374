import React from 'react'

const MarineBiology = () => {
    return (
        <svg width="42" height="42" viewBox="0 0 42 42" fill="none" xmlns="http://www.w3.org/2000/svg" xlinkHref="http://www.w3.org/1999/xlink">
            <rect width="42" height="42" fill="url(#pattern0_7112_30678)" />
            <defs>
                <pattern id="pattern0_7112_30678" patternContentUnits="objectBoundingBox" width="1" height="1">
                    <use xlinkHref="#image0_7112_30678" transform="scale(0.0041841)" />
                </pattern>
                <image id="image0_7112_30678" width="239" height="239" preserveAspectRatio="none" xlinkHref="data:image/png;base64,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" />
            </defs>
        </svg>


    )
}

export default MarineBiology