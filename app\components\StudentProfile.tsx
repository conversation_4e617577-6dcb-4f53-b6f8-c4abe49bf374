import React from 'react';
import Heading from './Heading';
import { studentDetails } from '@/common';
import StudentProfileCover from '@/app/assets/img/Avatar.png';
import {
    Avatar,
    AvatarFallback,
    AvatarImage,
  } from '@/components/ui/avatar';

const StudentProfile = () => {
    const getStatusAndEmailValue = (value: string) => {
        switch (value) {
            case 'Email':
                return 'underline text-primaryColor';
            case 'Status':
                return 'text-graySix';
            default:
                return '';
        }
    };
    
    return (
        <div className='flex gap-4 p-6 bg-white rounded-[20px] drop-shadow-[0_1px_2px_rgba(0, 0, 0, 0.05)]'>
            <div className='w-12 h-12 rounded-full overflow-hidden'>
                <Avatar>
                    <AvatarImage src='https://github.com/shadcn.png' alt='@shadcn' />
                    <AvatarFallback>CN</AvatarFallback>
                </Avatar>
            </div>    
            <div className='flex flex-col gap-3.5'>
                <div>
                    <Heading level='h2'>
                        <PERSON><PERSON>
                    </Heading>
                </div>
                <div className='flex space-x-32'>
                    {studentDetails.map((student, index) => (
                        <div 
                            key={index} 
                            className='space-y-1.5 text-sm leading-4 text-grayFour'
                        >
                            <p className='font-normal'>{student.label}</p>
                            <p className={`font-semibold ${getStatusAndEmailValue(student.label)}`}>{student.value}</p>
                    </div>
                    ))}
                </div>
            </div>      
        </div>
    )
}

export default StudentProfile