import React from 'react'
import DashboardLayout from '../components/layout/DashboardLayout'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import StatCardLayout from '../components/StatCardLayout'
import StatInfoCardSmall from '../components/StatInfoCardSmall'
import MultipleBarChart from '../components/MultipleBarChart'
import CustomFunnel<PERSON>hart from '../components/CustomFunnelChart';
import CounselorBarChartIcon from '../assets/svg/counselor-bar-chart-icon'
import StatCardVisitorIcon from '@/app/assets/svg/stat-card-visitor-icon.svg';
import StatCardStudentIcon from '@/app/assets/svg/stat-card-student-icon.svg';
import StatCardAcceptedIcon from '@/app/assets/svg/stat-card-accepted-icon.svg';
import StatCardRejectedIcon from '@/app/assets/svg/stat-card-rejected-icon.svg';
import StatCardApplicationIcon from '@/app/assets/svg/stat-card-application-icon.svg';
import { 
    ProgramPopularityData, 
    StudentEnrollmentData, 
    StudentDemographicsData, 
    StudentStatusPieChartData, 
    ApplicationStatusPieChartData,
    PopularCoursesChartData, 
    AwardedStudentsListTableHead,
    ScholarshipAwardStudentList,
    StudentListTableData,
    StudentsListTableHead,
    ComissionsListTableHead,
    ComissionsListTableData
} from '@/common';
import CounselorActivities from '../components/CounselorActivities'
import UpcomingEvent from '../components/UpcomingEvent'
import { PieCharts } from '../components/PieCharts'
import { AreaChartGradient } from '../components/AreaChartGradient'
import { SingleBarChart } from '../components/SingleBarChart'
import DatePickerWithRange from '../components/DatepickerWithRange'
import { DonutChart } from '../components/DonutChart'
import { ChartConfig } from '@/components/ui/chart'
import HorizontalBarChart from '../components/HorizontalBarChart'
import HorizontalChart from '../components/HorizontalChart'
import Link from 'next/link'
import ArrowForward from '../assets/svg/arrow_forward'
import ApplicationStatus from '../components/ApplicationStatus'

const revenueChartConfig = {
    value: {
        label: 'Value',
    },
    'Overall Revenue': {
        label: 'Overall Revenue',
        color: '#D2E3FC',
    },
    'Net Profit': {
        label: 'Net Profit',
        color: '#1E62E0',
    },
} satisfies ChartConfig;

const StudentCompositionData = [
        { category: "Male", value: 65, fill: "#1E62E0" },
        { category: "Female", value: 35, fill: "#E7EFFF" },
];

const StudentCompositionOptions = ['This Year', 'This Month'];

const StudentCompositionchartConfig = {
    application: {
        label: 'Applications',
        color: '#184EB3',
    },
    accepted: {
        label: 'Accepted',
        color: '#1E62E0',
    },
    rejected: {
        label: 'Rejected',
        color: '#E7EFFF',
    },
};

const StudentDemoGraphicChartConfig = {
    performance: { 
        label: 'Students', 
        color: '#1E62E0', 
        name: 'Students' 
    },
}

const TopDestinationChartConfig = {
    percentage: { label: 'Percentage', color: '#1E62E0', name: 'Percentage' },
}



const page = () => {
    return (
        <DashboardLayout>
            <div className="flex flex-col gap-10 py-5">
                <div className="w-full flex justify-between items-center">
                    <h1 className="text-[28px] leading-[34px] font-bold text-graySix">
                        Welcome Michael!
                    </h1>
                    <DatePickerWithRange />
                </div>
                <div className='flex flex-col gap-10 py-10'>
                    <div className='grid md:grid-flow-col md:auto-cols-fr gap-6'>
                        <StatCardLayout>
                            <StatInfoCardSmall
                                imageSrc={StatCardVisitorIcon}
                                description='Students'
                                number={1523}
                            />
                        </StatCardLayout>
                        <StatCardLayout>
                            <StatInfoCardSmall
                                imageSrc={StatCardStudentIcon}
                                description='Applications'
                                number={2120}
                            />
                        </StatCardLayout>
                        <StatCardLayout>
                            <StatInfoCardSmall
                                imageSrc={StatCardApplicationIcon}
                                description='VISA Success'
                                number={3134}
                            />
                        </StatCardLayout>
                        <StatCardLayout>
                            <StatInfoCardSmall
                                imageSrc={StatCardAcceptedIcon}
                                description='Enrollment'
                                number={4196}
                            />
                        </StatCardLayout>
                        <StatCardLayout>
                            <StatInfoCardSmall
                                imageSrc={StatCardRejectedIcon}
                                description='Rejected'
                                number={75254}
                            />
                        </StatCardLayout>
                    </div>
                    <div className='flex items-stretch gap-6'>
                        <StatCardLayout className='h-full w-[55%]'>
                            <SingleBarChart
                                title='Program Popularity'
                                chartConfig={TopDestinationChartConfig}
                                data={ProgramPopularityData}
                                chartHeightClass='max-h-[370px]'
                                xAxisLabelAngle={-90}
                                xAxisHeight={190}
                                xAxisTickMargin={10}
                            />
                        </StatCardLayout>
                    
                        <StatCardLayout className='h-full w-[45%]'>
                            <AreaChartGradient 
                                title='Student Enrollment'
                                data={StudentEnrollmentData}
                                lines={[
                                    { label: 'Visitors', color: '#3B82F6', dataKey: 'visitors' },
                                ]}
                                xAxisKey='month'
                                chartContainerClassName='max-h-[500px]'
                            />
                        </StatCardLayout>
                    </div>
                    <div className='grid md:grid-cols-2 grid-cols-1 gap-6'>
                        <StatCardLayout>
                            <CustomFunnelChart 
                            title="Application Funnel"
                            data={[
                                { label: "Application Submitted", value: 400 },
                                { label: "Documents Verified", value: 350 },
                                { label: "Offer Letter Issued", value: 300 },
                                { label: "Payment Completed", value: 200 },
                                { label: "Visa Approved", value: 180 },
                                { label: "Enrolled", value: 170 },
                            ]}
                            />
                        </StatCardLayout>
                        <PieCharts 
                            title="Application Status" 
                            data={ApplicationStatusPieChartData}
                            // customLabels={customLabels}
                            showLabelsOnPie={true}
                            showLegendLayout='right'
                        />
                        <StatCardLayout className='h-full'>
                            <PieCharts 
                                title="Student Status" 
                                data={StudentStatusPieChartData}
                                // customLabels={customLabels}
                                showLabelsOnPie={true}
                                showLegendLayout='right'
                            />
                        </StatCardLayout>
                        <StatCardLayout className='h-full'>
                            <SingleBarChart
                                title='Student Demographics'
                                chartConfig={StudentDemoGraphicChartConfig}
                                data={StudentDemographicsData}
                                chartHeightClass='max-h-[343px]'
                                barRadius={50}
                            />
                        </StatCardLayout>
                    </div>
                    <div className='grid grid-cols-5 gap-6'>
                        <StatCardLayout className='h-full col-span-3 p-5'>
                            <div className='flex justify-between items-center'>
                                <span className='font-bold text-lg text-grayFive'>Scholarship Awarded Students List</span>
                                <Link           
                                    href={'view-all'} 
                                    className='flex gap-1 items-center font-medium text-[10px] text-secondaryColor rounded-[16px] py-2.5 px-4 bg-primaryOne'
                                >
                                    View All
                                    <ArrowForward className='w-3 h-3 text-secondaryColor' />
                                </Link>
                            </div>
                            <div className='overflow-x-auto border-[0.5px] border-grayOne rounded-[20px] mt-[22px]'>
                                <table className='bg-white min-w-full text-sm text-left'>
                                    <thead>
                                        <tr>
                                            {AwardedStudentsListTableHead.map((thead, index) => (
                                                <th 
                                                    key={index}
                                                    className={`p-2.5 font-bold text-xs tracking-[0.4px] leading-5 text-grayFive ${
                                                        index === 2 || index === 5 ? 'text-center' : ''
                                                    }`}
                                                >
                                                    {thead}
                                                </th>
                                            ))}
                                        </tr>
                                    </thead>
                                    <tbody className='border-t border-grayOne divide-y divide-grayOne'>
                                        {ScholarshipAwardStudentList.map((success, index) => (
                                            <tr key={index}>
                                                <td className='text-xs leading-5 text-graySix p-2.5 font-normal'>{success.sid}</td>
                                                <td className='text-xs leading-5 text-graySix p-2.5 font-normal flex gap-2.5'>{success.name}</td>
                                                <td className='text-xs leading-5 text-graySix p-2.5 font-normal'>
                                                    <div className='flex justify-around'>
                                                        {<success.Nationality.logo />}
                                                    </div>
                                                </td>
                                                <td className='text-xs leading-5 text-graySix p-2.5 font-normal'>{success.program}</td>
                                                <td className='text-xs leading-5 text-graySix p-2.5 font-normal'>{success.scholership}</td>
                                                <td className='text-xs leading-5 text-graySix p-2.5 font-normal'>
                                                    <div className='flex justify-center'>
                                                        {success.grantYear}
                                                    </div>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </StatCardLayout>
                        <StatCardLayout className='col-span-2 h-full'>
                            <DonutChart 
                                chartConfig={StudentCompositionchartConfig}
                                data={StudentCompositionData} // Pass the array directly, not the object
                                title="Revenue Breakdown"
                                legendPosition='bottom'
                                chartClassName='max-h-[500px]'
                                innerCircleRedius={80}
                            />
                        </StatCardLayout>
                    </div>
                    <div className='grid grid-cols-2 gap-6'>
                        <StatCardLayout className='h-full'>
                            <HorizontalChart data={PopularCoursesChartData} />
                        </StatCardLayout>
                        <StatCardLayout className='h-full p-5'>
                            <div className='flex justify-between items-center'>
                                <span className='font-bold text-lg text-grayFive'>Student list</span>
                                <Link           
                                    href={'view-all'} 
                                    className='flex gap-1 items-center font-medium text-[10px] text-secondaryColor rounded-[16px] py-2.5 px-4 bg-primaryOne'
                                >
                                    View All
                                    <ArrowForward className='w-3 h-3 text-secondaryColor' />
                                </Link>
                            </div>
                            <div className='overflow-x-auto border-[0.5px] border-grayOne rounded-[20px] mt-[22px]'>
                                <table className='bg-white min-w-full text-sm text-left'>
                                    <thead>
                                        <tr>
                                            {StudentsListTableHead?.map((thead, index) => (
                                                <th 
                                                    key={index}
                                                    className={`p-2.5 font-bold text-xs tracking-[0.4px] leading-5 text-grayFive ${
                                                        index === 2 || index === 4 ? 'text-center' : ''
                                                    }`}
                                                >
                                                    {thead}
                                                </th>
                                            ))}
                                        </tr>
                                    </thead>
                                    <tbody className='border-t border-grayOne divide-y divide-grayOne'>
                                        {(StudentListTableData).map((success, index) => (
                                            <tr key={index}>
                                                <td className='text-xs leading-5 text-graySix p-2.5 font-normal'>{success.sid}</td>
                                                <td className='text-xs leading-5 text-graySix p-2.5 font-normal'>{success.name}</td>
                                                <td className='text-xs leading-5 text-graySix p-2.5 font-normal'>
                                                    <div className='flex justify-around'>
                                                        {<success.Nationality.logo />}
                                                    </div>
                                                </td>
                                                <td className='text-xs leading-5 text-graySix p-2.5 font-normal'>{success.program}</td>
                                                <td className='text-xs leading-5 text-graySix p-2.5 font-normal'>
                                                    <div className='flex justify-around py-1 text-sm'>
                                                        <ApplicationStatus status={success.status} />
                                                    </div>
                                                </td>
                                                
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </StatCardLayout>
                    </div>
                    <div className='grid grid-cols-2 gap-6'>
                        <StatCardLayout className='h-full p-5'>
                            <div className='flex justify-between items-center'>
                                <span className='font-bold text-lg text-grayFive'>Comissions List</span>
                                <Link           
                                    href={'view-all'} 
                                    className='flex gap-1 items-center font-medium text-[10px] text-secondaryColor rounded-[16px] py-2.5 px-4 bg-primaryOne'
                                >
                                    View All
                                    <ArrowForward className='w-3 h-3 text-secondaryColor' />
                                </Link>
                            </div>
                            <div className='overflow-x-auto border-[0.5px] border-grayOne rounded-[20px] mt-[22px]'>
                                <table className='bg-white min-w-full text-sm text-left'>
                                    <thead>
                                        <tr>
                                            {ComissionsListTableHead?.map((thead, index) => (
                                                <th 
                                                    key={index}
                                                    className={`p-2.5 font-bold text-xs tracking-[0.4px] leading-5 text-grayFive ${
                                                        index === 4 || index === 5 ? 'text-center' : ''
                                                    }`}
                                                >
                                                    {thead}
                                                </th>
                                            ))}
                                        </tr>
                                    </thead>
                                    <tbody className='border-t border-grayOne divide-y divide-grayOne'>
                                        {(ComissionsListTableData).map((success, index) => (
                                            <tr key={index}>
                                                <td className='text-xs leading-5 text-graySix px-2.5 py-1 font-normal'>{success.sid}</td>
                                                <td className='text-xs leading-5 text-graySix px-2.5 py-1 font-normal'>{success.name}</td>
                                                <td className='text-xs leading-5 text-graySix px-2.5 py-1 font-normal'>{success.ComissionType}</td>
                                                <td className='text-xs leading-5 text-graySix px-2.5 py-1 font-normal'>{success.ComissionDeadline}</td>
                                                <td className='text-xs leading-5 text-graySix px-2.5 py-1 font-normal'>
                                                    <div className='flex justify-around'>
                                                        {success.Comission}
                                                    </div>
                                                </td>
                                                <td className='text-xs leading-5 text-graySix px-2.5 py-1 font-normal'>
                                                    <div className='flex justify-around py-1 text-sm'>
                                                        <ApplicationStatus status={success.status} />
                                                    </div>
                                                </td>
                                                
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        </StatCardLayout>
                        <StatCardLayout className='h-full'>
                            <SingleBarChart
                                title='Job Placements'
                                chartConfig={StudentDemoGraphicChartConfig}
                                data={StudentDemographicsData}
                                chartHeightClass='max-h-[343px]'
                                barRadius={50}
                                barSize={10}
                                showLegend={false}
                            />
                        </StatCardLayout>
                        
                    </div>
                </div>
            </div>
        </DashboardLayout>
    )
}

export default page
