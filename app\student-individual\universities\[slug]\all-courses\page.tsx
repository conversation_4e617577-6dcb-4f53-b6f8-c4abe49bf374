// 'use client'

import Image from 'next/image';
import React from 'react';
import { useToast } from '@/hooks/use-toast';
import Switcher from '@/app/components/Switcher';
import { CourseFinderFiltersProps } from '@/types';
import Distance from '@/app/assets/svg/distance';
import CourseCard from '@/app/components/CourseCard';
import Pagination from '@/app/components/Pagination';
import CoursesFilter from '@/app/components/CoursesFilter';
import HarvardLogo from '@/app/assets/svg/harvard-uni-logo.svg';
import DashboardLayout from '@/app/components/layout/DashboardLayout';
import { 
    courses, 
    universityList,
    countries, 
    programs, 
    sortByOptions, 
    intakes 
} from '@/common';

type Params = Promise<{ slug: string }>

export async function generateStaticParams() {
    return universityList.map((university) => ({
      slug: university.slug,
    }));
};

const page = async (props: { params: Params }) => {
    const params = await props.params;
    const course = courses.find(u => u.slug === params.slug);
    // const universityCourses = courses.filter(c => c.courses === university?.id);
        // console.log(university?.courses)
        console.log(course)
        if (!course) {
            return <div>course not found</div>;
        }
    
    // const [isChecked, setIsChecked] = useState(false);
    // const [searchCourse, setSearchCourse] = useState(true)
    // const [selectedCards, setSelectedCards] = useState<number[]>([]);
    // const [filters, setFilters] = useState<CourseFinderFiltersProps>({
    //     program: '',
    //     intake: '',
    //     country: [],
    //     tuitionFees: '',
    //     sortBy: '',
    // });
    const { toast } = useToast();

    // const handleSelect = (id: number) => {
    //     setSelectedCards((prevSelectedCards) => {
    //         if (prevSelectedCards.includes(id)) {
    //             return prevSelectedCards.filter((cardId) => cardId !== id);
    //         } else if (prevSelectedCards.length < 4) {
    //             return [...prevSelectedCards, id];
    //         } else {
    //             toast({
    //                 description: 'Sorry, you can compare up to 04 courses!',
    //                 variant: 'destructive',
    //             });
    //             return prevSelectedCards;
    //         }
    //     });
    // };
    // const filteredCourses = courses.filter((course) => {
    //     if (filters.program && course.program !== filters.program) return false;
    //     if (filters.intake && course.intake !== filters.intake) return false;
    //     if (filters.country.length > 0 && !filters.country.includes(course.country)) return false;
    //     if (
    //         filters.tuitionFees &&
    //         (course.tuitionFees[1] < parseInt(filters.tuitionFees) ||
    //         course.tuitionFees[0] > parseInt(filters.tuitionFees))
    //     )
    //         return false;
    //     return true;
    //     }).map((course) => ({
    //     ...course,
    //     tuitionFees: [course.tuitionFees[0], course.tuitionFees[1]] as [number, number],
    // }));   
    
    // const handleToggle = (checked: boolean) => {
    //     setIsChecked(checked);
    //     setSelectedCards([])
    // };

    // const itemsPerPage = 2;
    // const [currentPage, setCurrentPage] = useState(1);
    // const totalPages = Math.ceil(courses.length / itemsPerPage);

    // const currentCources = courses.slice(
    //     (currentPage - 1) * itemsPerPage,
    //     currentPage * itemsPerPage
    // );

    // const goToPage = (page: number) => {
    //     setCurrentPage(page);
    // };
    return (
        <DashboardLayout>
            <div className='mt-5'>
                <div className='flex gap-5 items-center py-5'>
                    <div className=''>
                        <Image
                            src={HarvardLogo}
                            alt='uni logo'
                            className='md:max-w-16 max-w-12 h-fit'
                        />
                    </div>
                    <div className='flex flex-col gap-2'>
                        <h2 className='font-semibold md:text-[28px] md:leading-[42px] text-2xl leading-[29px] text-graySix'>
                            The University of Chicago
                        </h2>
                        <div className='flex md:flex-row flex-col gap-2 text-grayFour font-normal text-sm leading-[17px]'>
                            <div className='flex gap-1.5 items-center'>
                                <Distance />
                                <p>5801 S Ellis Ave, Chicago, IL 60637,United States</p>
                            </div>
                            <div className='ml-6'>
                                <ul className='list-disc flex gap-2'>
                                    <li>Private</li>
                                    <li className='ml-4'>Non-profit</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                {/* <CoursesFilter 
                    programs={programs}
                    intakes={intakes}
                    countries={countries}
                    tuitionFees={filters.tuitionFees}
                    sortByOptions={sortByOptions}
                    onFilterChange={setFilters}
                    searchCourse={searchCourse}
                    className='rounded-2xl border bg-white border-[#1E62E0] border-opacity-20 py-[30px] px-5'
                /> */}
                </div>
                <div className='flex flex-col md:flex-row gap-5 py-[22.5px] items-center'>
                    <div className='flex gap-7'>
                        <h2 className='font-semibold text-xl md:text-2xl leading-[29.05px] text-graySix'>Explore Courses</h2>
                        <div className='flex gap-3 items-center'>
                            <span className='font-normal text-base leading-6 text-grayFive'>Compare</span>
                            {/* <Switcher   
                                isChecked={isChecked}       
                                handleToggle={handleToggle} 
                                className='border border-grayFive' 
                            /> */}
                        </div>
                    </div>
                    <p className='font-semibold text-sm leading-4 text-primaryColor'>(Select maximum 04 courses for comparison)</p>
                </div>
                {/* <div className='pb-[170px] grid grid-cols-1 md:grid-cols-4 gap-6'>
                    {filteredCourses.map((course) => (
                        <CourseCard 
                            key={course.id} 
                            course={course} 
                            isSelected={selectedCards.includes(course.id)}
                            onSelect={handleSelect}
                            isChecked={isChecked}
                        />
                    ))}
                </div> */}
                {/* <div className='pb-[101px]'>
                    <Pagination
                        currentPage={currentPage}
                        totalPages={totalPages}
                        onPageChange={goToPage}
                    />
                </div> */}
        </DashboardLayout>
    )
}

export default page