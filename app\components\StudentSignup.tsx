'use client';

import { z } from 'zod';
import Link from 'next/link';
import React, { useState } from 'react';
import SelectField from './SelectField';
import { countryOptions } from '@/common';
import { useForm } from 'react-hook-form';
import GoogleSignIn from './GoogleSignIn';
import { useRouter } from 'next/navigation';
import { signUpFormSchema } from '@/schema';
import { Button } from '@/components/ui/button';
import PhoneNumberInput from './PhoneNumberInput';
import Visibility from '@/app/assets/svg/visibility';
import { zodResolver } from '@hookform/resolvers/zod';
import VisibilityOff from '@/app/assets/svg/visibility_off';
import { useRegisterMutation } from '@/lib/redux/api/authApi';
import {
  Form,
  FormItem,
  FormField,
  FormLabel,
  FormControl,
  FormMessage,
} from '@/components/ui/form';

const StudentSignup = () => {
  const router = useRouter();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const togglePasswordVisibility = () => setShowPassword((prev) => !prev);
  const toggleConfirmPasswordVisibility = () => setShowConfirmPassword((prev) => !prev);

  type SignUpFormValues = z.infer<typeof signUpFormSchema>;

  const form = useForm<SignUpFormValues>({
    resolver: zodResolver(signUpFormSchema),
    defaultValues: {
      name: '',
      nationality: '',
      email: '',
      phone: '',
      password: '',
      confirmPassword: '',
    },
  });

  const [registerUser, { isLoading, error, isError }] = useRegisterMutation();

  const onSubmit = async (values: SignUpFormValues) => {
    try {
      const response = await registerUser({
        ...values,
        roleName: 'Student',
      }).unwrap();

      if (!response.success || response.message?.includes('already')) {
        form.setError('email', {
          type: 'manual',
          message: 'Existing account found. Choose another email.',
        });
        return;
      }

      sessionStorage.setItem('verificationEmail', values.email);
      form.reset();
      router.push('/verify-otp');
    } catch (err: any) {
      console.error('Registration error:', err?.data?.message || err.message);
    }
  };

  return (
    <div>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)}>
          {/* Name Field */}
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem className="space-y-1.5">
                <FormLabel className="text-sm font-medium text-graySix">Name*</FormLabel>
                <FormControl>
                  <input
                    type="text"
                    placeholder="Enter your name"
                    className="flex w-full rounded-[8px] outline-none focus:ring-1 border border-tertiary border-opacity-20 py-2.5 px-4 placeholder:text-grayThree"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Nationality Field */}
          <FormField
            control={form.control}
            name="nationality"
            render={({ field }) => (
              <FormItem className="space-y-1.5 mt-5">
                <FormLabel className="text-sm font-medium text-graySix">Nationality*</FormLabel>
                <FormControl>
                  <SelectField
                    placeholder="Select"
                    options={countryOptions}
                    value={field.value}
                    onChange={field.onChange}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Email Field */}
          <FormField
            control={form.control}
            name="email"
            render={({ field, fieldState }) => (
              <FormItem className="space-y-1.5 mt-5">
                <FormLabel className="text-sm font-medium text-graySix">Email*</FormLabel>
                <FormControl>
                  <input
                    type="email"
                    placeholder="Enter your email"
                    className={`flex w-full rounded-[8px] outline-none border py-2.5 px-4 placeholder:text-grayThree ${
                      fieldState.error ? 'border-[#FF3B30]' : 'border-tertiary border-opacity-20'
                    }`}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Phone Field */}
          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem className="space-y-1.5 mt-5">
                <FormLabel className="text-sm font-medium text-graySix">Phone*</FormLabel>
                <FormControl>
                  <PhoneNumberInput
                    NumberInputClassName="pl-2 py-2.5"
                    className="rounded-[8px] pl-4"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Password Field */}
          <FormField
            control={form.control}
            name="password"
            render={({ field }) => (
              <FormItem className="space-y-1.5 mt-5">
                <FormLabel className="text-sm font-medium text-graySix">Password*</FormLabel>
                <FormControl>
                  <div className="relative">
                    <input
                      type={showPassword ? 'text' : 'password'}
                      placeholder="Enter your password"
                      className="flex w-full rounded-[8px] outline-none border py-2.5 px-4 placeholder:text-grayThree border-tertiary border-opacity-20"
                      {...field}
                    />
                    <button
                      type="button"
                      onClick={togglePasswordVisibility}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2"
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility className="text-grayThree" />}
                    </button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Confirm Password Field */}
          <FormField
            control={form.control}
            name="confirmPassword"
            render={({ field }) => (
              <FormItem className="space-y-1.5 mt-5">
                <FormLabel className="text-sm font-medium text-graySix">Confirm Password*</FormLabel>
                <FormControl>
                  <div className="relative">
                    <input
                      type={showConfirmPassword ? 'text' : 'password'}
                      placeholder="Confirm your password"
                      className="flex w-full rounded-[8px] outline-none border py-2.5 px-4 placeholder:text-grayThree border-tertiary border-opacity-20"
                      {...field}
                    />
                    <button
                      type="button"
                      onClick={toggleConfirmPasswordVisibility}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2"
                    >
                      {showConfirmPassword ? (
                        <VisibilityOff />
                      ) : (
                        <Visibility className="text-grayThree" />
                      )}
                    </button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {isError && (
            <p className="text-red-500 text-sm mt-2">
              {(error as any)?.data?.message || 'Registration failed.'}
            </p>
          )}

          <Button
            type="submit"
            disabled={isLoading}
            className="w-full bg-primaryColor rounded-[8px] py-2.5 mt-6 mb-5 hover:bg-tertiary text-white text-base font-semibold"
          >
            {isLoading ? 'Creating account...' : 'Create account'}
          </Button>
        </form>
      </Form>

      <div className="w-full">
        <GoogleSignIn />
      </div>

      <div className="flex justify-center gap-1 mt-8">
        <span className="text-sm font-normal text-grayFive">Already have an account?</span>
        <Link className="text-sm font-semibold text-primaryColor" href="/login">
          Log in
        </Link>
      </div>
    </div>
  );
};

export default StudentSignup;
