import React, { useState } from 'react';
import { UniversityTableProps } from '@/types';
import TooltipCountry from './ui/TooltipCountry';

const UniversityTable: React.FC<UniversityTableProps> = ({ 
    universityList, 
    isChecked 
}) => {
    const [data, setData] = useState(universityList);
    const [selectedRows, setSelectedRows] = useState<number[]>([]);
    const [selectAll, setSelectAll] = useState(false);
    const [filters, setFilters] = useState({
        type: '',
        country: '',
        province: '',
        city: '',
        sortBy: '',
    });
    const handleSelectAll = () => {
        if (selectAll) {
            setSelectedRows([]);
        } else {
            const allIndexes = filteredData.map((_, index) => index);
            setSelectedRows(allIndexes);
        }
        setSelectAll(!selectAll);
    };

    const handleRowSelect = (index: number) => {
        if (selectedRows.includes(index)) {
            setSelectedRows(selectedRows.filter((rowIndex) => rowIndex !== index));
        } else {
            setSelectedRows([...selectedRows, index]);
        }
    };


const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement | HTMLInputElement>) => {
    const { name, value } = e.target;
    setFilters({ ...filters, [name]: value });
};

const filteredData = data
    .filter((item) =>
        filters.type ? item.type.toLowerCase().includes(filters.type.toLowerCase()) : true
    )
    .filter((item) =>
        filters.country ? item.country.label.toLowerCase().includes(filters.country.toLowerCase()) : true
    )
    .filter((item) =>
        filters.province ? item.province.toLowerCase().includes(filters.province.toLowerCase()) : true
    )
    .filter((item) =>
        filters.city ? item.city.toLowerCase().includes(filters.city.toLowerCase()) : true
    )
    .sort((a, b) => {
        if (filters.sortBy === 'rank') {
            return a.rank - b.rank;
        }
        if (filters.sortBy === 'tuition') {
            const tuitionA = parseInt(a.tuitionFees.split('-')[0].replace('$', '').replace(',', ''));
            const tuitionB = parseInt(b.tuitionFees.split('-')[0].replace('$', '').replace(',', ''));
            return tuitionA - tuitionB;
        }

        return 0;
    });

    const tableHeadData = [
        'University',
        'Location',
        'Country',
        'University Type',
        'Tuition Fees (yr)',
        'Rank'
    ];

    return (
        <div className='overflow-x-auto'>
            <table className='bg-white min-w-full rounded-[20px] text-sm text-left'>
                <thead className=''>
                    <tr>
                        {isChecked && (
                            <th className='p-4'>
                                <input 
                                    checked={selectAll}
                                    onChange={handleSelectAll} 
                                    type='checkbox' 
                                    className='h-4 w-4 border-grayOne' 
                                />
                            </th>
                        )}
                        {tableHeadData.map((thead, index) => (
                            <th 
                                key={index}
                                className='p-4 font-bold text-xs tracking-[0.4px] text-grayFive'
                            >
                                {thead}
                            </th>
                        ))}
                    </tr>
                </thead>
                <tbody className='border-t border-grayOne divide-y divide-grayOne'>
                    {filteredData.map((university, index) => (
                        <tr
                            key={index}
                            className={`${
                                selectedRows.includes(index) ? 'bg-[#E9F0FF] border-grayOn' : ''
                            }`}
                        >
                            {isChecked && (
                                <td className='p-4'>
                                    <input 
                                        type='checkbox' 
                                        className='h-4 w-4 border-grayOne' 
                                        checked={selectedRows.includes(index)}
                                        onChange={() => handleRowSelect(index)}
                                    />
                                </td>
                            )}
                            <td className='text-xs leading-5 text-graySix p-4 font-normal'>{university.universityName}</td>
                            <td className='text-xs leading-5 text-graySix p-4 font-normal'>{university.location}</td>
                            <td className='text-xs leading-5 text-graySix p-4 flex items-center gap-2 font-normal'>
                                <TooltipCountry 
                                    logo={<university.country.logo />} 
                                    label={university.country.label} 
                                />
                            </td>
                            <td className='text-xs leading-5 text-graySix p-4 font-normal'>{university.type}</td>
                            <td className='text-xs leading-5 text-graySix p-4 font-normal'>{university.tuitionFees}</td>
                            <td className='text-xs leading-5 text-graySix p-4'>{university.rank}</td>
                        </tr>
                    ))}
                </tbody>
            </table>
        </div>
    );
};

export default UniversityTable;