import React from 'react';

const AvgTutionFeeIcon = () => {
    return (
        <svg
            width="19"
            height="18"
            viewBox="0 0 19 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="mask0_3612_19908"
                style={{maskType:'alpha'}}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="19"
                height="18"
            >
                <rect x="0.669922" width="18" height="18" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_3612_19908)">
                <path
                    d="M3.66992 15C3.25742 15 2.9043 14.8531 2.61055 14.5594C2.3168 14.2656 2.16992 13.9125 2.16992 13.5V4.5C2.16992 4.0875 2.3168 3.73438 2.61055 3.44063C2.9043 3.14688 3.25742 3 3.66992 3H15.6699C16.0824 3 16.4355 3.14688 16.7293 3.44063C17.023 3.73438 17.1699 4.0875 17.1699 4.5V13.5C17.1699 13.9125 17.023 14.2656 16.7293 14.5594C16.4355 14.8531 16.0824 15 15.6699 15H3.66992ZM3.66992 13.5H15.6699V4.5H3.66992V13.5ZM8.16992 10.5H5.91992C5.70742 10.5 5.5293 10.5719 5.38555 10.7156C5.2418 10.8594 5.16992 11.0375 5.16992 11.25C5.16992 11.4625 5.2418 11.6406 5.38555 11.7844C5.5293 11.9281 5.70742 12 5.91992 12H6.66992C6.66992 12.2125 6.7418 12.3906 6.88555 12.5344C7.0293 12.6781 7.20742 12.75 7.41992 12.75C7.63242 12.75 7.81055 12.6781 7.9543 12.5344C8.09805 12.3906 8.16992 12.2125 8.16992 12H8.91992C9.13242 12 9.31055 11.9281 9.4543 11.7844C9.59805 11.6406 9.66992 11.4625 9.66992 11.25V9C9.66992 8.7875 9.59805 8.60938 9.4543 8.46562C9.31055 8.32188 9.13242 8.25 8.91992 8.25H6.66992V7.5H8.91992C9.13242 7.5 9.31055 7.42812 9.4543 7.28438C9.59805 7.14062 9.66992 6.9625 9.66992 6.75C9.66992 6.5375 9.59805 6.35938 9.4543 6.21562C9.31055 6.07187 9.13242 6 8.91992 6H8.16992C8.16992 5.7875 8.09805 5.60938 7.9543 5.46563C7.81055 5.32188 7.63242 5.25 7.41992 5.25C7.20742 5.25 7.0293 5.32188 6.88555 5.46563C6.7418 5.60938 6.66992 5.7875 6.66992 6H5.91992C5.70742 6 5.5293 6.07187 5.38555 6.21562C5.2418 6.35938 5.16992 6.5375 5.16992 6.75V9C5.16992 9.2125 5.2418 9.39063 5.38555 9.53438C5.5293 9.67813 5.70742 9.75 5.91992 9.75H8.16992V10.5ZM12.8012 12.0562L13.8512 11.0063C13.9137 10.9437 13.9293 10.875 13.898 10.8C13.8668 10.725 13.8074 10.6875 13.7199 10.6875H11.6199C11.5324 10.6875 11.473 10.725 11.4418 10.8C11.4105 10.875 11.4262 10.9437 11.4887 11.0063L12.5387 12.0562C12.5762 12.0938 12.6199 12.1125 12.6699 12.1125C12.7199 12.1125 12.7637 12.0938 12.8012 12.0562ZM11.6199 7.5H13.7199C13.8074 7.5 13.8668 7.4625 13.898 7.3875C13.9293 7.3125 13.9137 7.24375 13.8512 7.18125L12.8012 6.13125C12.7637 6.09375 12.7199 6.075 12.6699 6.075C12.6199 6.075 12.5762 6.09375 12.5387 6.13125L11.4887 7.18125C11.4262 7.24375 11.4105 7.3125 11.4418 7.3875C11.473 7.4625 11.5324 7.5 11.6199 7.5Z"
                    fill="currentColor"
                />
            </g>
        </svg>
    );
};

export default AvgTutionFeeIcon;
