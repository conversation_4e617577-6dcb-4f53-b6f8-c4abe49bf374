import React from 'react'

const ApplygoalCertificate = () => {
    return (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_1642_10220" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="20">
                <rect width="20" height="20" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_1642_10220)">
                <path d="M9.12484 10.5833L7.9165 9.39581C7.76373 9.24303 7.57275 9.16664 7.34359 9.16664C7.11442 9.16664 6.9165 9.24998 6.74984 9.41664C6.59706 9.56942 6.52067 9.76387 6.52067 9.99998C6.52067 10.2361 6.59706 10.4305 6.74984 10.5833L8.5415 12.375C8.70817 12.5416 8.90262 12.625 9.12484 12.625C9.34706 12.625 9.5415 12.5416 9.70817 12.375L13.2498 8.83331C13.4165 8.66664 13.4964 8.4722 13.4894 8.24998C13.4825 8.02775 13.4026 7.83331 13.2498 7.66664C13.0832 7.49998 12.8853 7.41317 12.6561 7.40623C12.4269 7.39928 12.229 7.47914 12.0623 7.64581L9.12484 10.5833ZM6.7915 18.125L5.58317 16.0833L3.2915 15.5833C3.08317 15.5416 2.9165 15.434 2.7915 15.2604C2.6665 15.0868 2.61789 14.8958 2.64567 14.6875L2.87484 12.3333L1.31234 10.5416C1.17345 10.3889 1.104 10.2083 1.104 9.99998C1.104 9.79164 1.17345 9.61109 1.31234 9.45831L2.87484 7.66664L2.64567 5.31248C2.61789 5.10414 2.6665 4.91317 2.7915 4.73956C2.9165 4.56595 3.08317 4.45831 3.2915 4.41664L5.58317 3.91664L6.7915 1.87498C6.90262 1.69442 7.05539 1.57289 7.24984 1.51039C7.44428 1.44789 7.63873 1.45831 7.83317 1.54164L9.99984 2.45831L12.1665 1.54164C12.3609 1.45831 12.5554 1.44789 12.7498 1.51039C12.9443 1.57289 13.0971 1.69442 13.2082 1.87498L14.4165 3.91664L16.7082 4.41664C16.9165 4.45831 17.0832 4.56595 17.2082 4.73956C17.3332 4.91317 17.3818 5.10414 17.354 5.31248L17.1248 7.66664L18.6873 9.45831C18.8262 9.61109 18.8957 9.79164 18.8957 9.99998C18.8957 10.2083 18.8262 10.3889 18.6873 10.5416L17.1248 12.3333L17.354 14.6875C17.3818 14.8958 17.3332 15.0868 17.2082 15.2604C17.0832 15.434 16.9165 15.5416 16.7082 15.5833L14.4165 16.0833L13.2082 18.125C13.0971 18.3055 12.9443 18.4271 12.7498 18.4896C12.5554 18.5521 12.3609 18.5416 12.1665 18.4583L9.99984 17.5416L7.83317 18.4583C7.63873 18.5416 7.44428 18.5521 7.24984 18.4896C7.05539 18.4271 6.90262 18.3055 6.7915 18.125ZM7.87484 16.625L9.99984 15.7083L12.1665 16.625L13.3332 14.625L15.6248 14.0833L15.4165 11.75L16.9582 9.99998L15.4165 8.20831L15.6248 5.87498L13.3332 5.37498L12.1248 3.37498L9.99984 4.29164L7.83317 3.37498L6.6665 5.37498L4.37484 5.87498L4.58317 8.20831L3.0415 9.99998L4.58317 11.75L4.37484 14.125L6.6665 14.625L7.87484 16.625Z" fill="#1952BB" />
            </g>
        </svg>
    )
}

export default ApplygoalCertificate