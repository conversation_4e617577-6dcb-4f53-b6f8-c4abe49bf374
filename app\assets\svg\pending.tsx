import React from 'react';

const Pending = () => {
    return (
        <svg
            width="19"
            height="19"
            viewBox="0 0 19 19"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="mask0_2560_25512"
                style={{maskType:'alpha'}}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="19"
                height="19"
            >
                <rect
                    x="0.253906"
                    y="0.5"
                    width="18"
                    height="18"
                    fill="#D9D9D9"
                />
            </mask>
            <g mask="url(#mask0_2560_25512)">
                <path
                    d="M5.50586 10.625C5.81836 10.625 6.08398 10.5156 6.30273 10.2969C6.52148 10.0781 6.63086 9.8125 6.63086 9.5C6.63086 9.1875 6.52148 8.92187 6.30273 8.70312C6.08398 8.48437 5.81836 8.375 5.50586 8.375C5.19336 8.375 4.92773 8.48437 4.70898 8.70312C4.49023 8.92187 4.38086 9.1875 4.38086 9.5C4.38086 9.8125 4.49023 10.0781 4.70898 10.2969C4.92773 10.5156 5.19336 10.625 5.50586 10.625ZM9.25586 10.625C9.56836 10.625 9.83398 10.5156 10.0527 10.2969C10.2715 10.0781 10.3809 9.8125 10.3809 9.5C10.3809 9.1875 10.2715 8.92187 10.0527 8.70312C9.83398 8.48437 9.56836 8.375 9.25586 8.375C8.94336 8.375 8.67773 8.48437 8.45898 8.70312C8.24023 8.92187 8.13086 9.1875 8.13086 9.5C8.13086 9.8125 8.24023 10.0781 8.45898 10.2969C8.67773 10.5156 8.94336 10.625 9.25586 10.625ZM13.0059 10.625C13.3184 10.625 13.584 10.5156 13.8027 10.2969C14.0215 10.0781 14.1309 9.8125 14.1309 9.5C14.1309 9.1875 14.0215 8.92187 13.8027 8.70312C13.584 8.48437 13.3184 8.375 13.0059 8.375C12.6934 8.375 12.4277 8.48437 12.209 8.70312C11.9902 8.92187 11.8809 9.1875 11.8809 9.5C11.8809 9.8125 11.9902 10.0781 12.209 10.2969C12.4277 10.5156 12.6934 10.625 13.0059 10.625ZM9.25586 17C8.21836 17 7.24336 16.8031 6.33086 16.4094C5.41836 16.0156 4.62461 15.4812 3.94961 14.8062C3.27461 14.1312 2.74023 13.3375 2.34648 12.425C1.95273 11.5125 1.75586 10.5375 1.75586 9.5C1.75586 8.4625 1.95273 7.4875 2.34648 6.575C2.74023 5.6625 3.27461 4.86875 3.94961 4.19375C4.62461 3.51875 5.41836 2.98437 6.33086 2.59062C7.24336 2.19687 8.21836 2 9.25586 2C10.2934 2 11.2684 2.19687 12.1809 2.59062C13.0934 2.98437 13.8871 3.51875 14.5621 4.19375C15.2371 4.86875 15.7715 5.6625 16.1652 6.575C16.559 7.4875 16.7559 8.4625 16.7559 9.5C16.7559 10.5375 16.559 11.5125 16.1652 12.425C15.7715 13.3375 15.2371 14.1312 14.5621 14.8062C13.8871 15.4812 13.0934 16.0156 12.1809 16.4094C11.2684 16.8031 10.2934 17 9.25586 17ZM9.25586 15.5C10.9309 15.5 12.3496 14.9187 13.5121 13.7562C14.6746 12.5937 15.2559 11.175 15.2559 9.5C15.2559 7.825 14.6746 6.40625 13.5121 5.24375C12.3496 4.08125 10.9309 3.5 9.25586 3.5C7.58086 3.5 6.16211 4.08125 4.99961 5.24375C3.83711 6.40625 3.25586 7.825 3.25586 9.5C3.25586 11.175 3.83711 12.5937 4.99961 13.7562C6.16211 14.9187 7.58086 15.5 9.25586 15.5Z"
                    fill="currentColor"
                />
            </g>
        </svg>
    );
};

export default Pending;
