'use client';

import { useState } from 'react';

interface ImageUploaderProps {
    label: string;
    multiple?: boolean;
    maxFiles?: number;
    value: File | File[] | null;
    onChange: (files: File | File[] | null) => void;
    className?: string;
}

export default function ImageUploader({
    label,
    multiple = false,
    maxFiles = 5,
    value,
    onChange,
    className = '',
}: ImageUploaderProps) {
    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (!e.target.files || e.target.files.length === 0) return;

        if (multiple) {
            const newFiles = Array.from(e.target.files);

            const currentFiles = Array.isArray(value) ? value : [];

            // Check if adding these files would exceed maxFiles
            if (currentFiles.length + newFiles.length > maxFiles) {
                alert(`Maximum ${maxFiles} files allowed`);
                const allowedNewFiles = newFiles.slice(
                    0,
                    maxFiles - currentFiles.length
                );
                onChange([...currentFiles, ...allowedNewFiles]);
            } else {
                onChange([...currentFiles, ...newFiles]);
            }
        } else {
            // For single file, just take the first one
            onChange(e.target.files[0]);
        }

        // Reset the input value so the same file can be selected again if removed
        e.target.value = '';
    };

    const removeFile = (filename: string) => {
        if (multiple && Array.isArray(value)) {
            onChange(value.filter((file) => file.name !== filename));
        } else {
            onChange(null);
        }
    };

    return (
        <div className={className}>
            <label className="block text-sm text-graySix mb-1.5">{label}</label>
            <div className="flex flex-wrap items-center border border-primaryColor/20 rounded px-3.5 py-[13px] w-full gap-2">
                <label className="text-primaryColor border border-primaryColor px-2 py-0.5 rounded cursor-pointer mr-3.5">
                    Choose File
                    <input
                        type="file"
                        className="hidden"
                        onChange={handleFileChange}
                        multiple={multiple}
                    />
                </label>

                {/* Display files */}
                {multiple && Array.isArray(value)
                    ? // Multiple files display
                      value.map((file) => (
                          <div
                              key={file.name}
                              className="bg-primaryOne text-secondaryColor px-2 py-1 rounded-full flex items-center gap-2"
                          >
                              {file.name}
                              <button
                                  onClick={() => removeFile(file.name)}
                                  className="text-sm font-bold"
                                  type="button"
                              >
                                  ×
                              </button>
                          </div>
                      ))
                    : // Single file display
                      !multiple &&
                      value && (
                          <div className="bg-primaryOne text-secondaryColor px-2 py-1 rounded-full flex items-center gap-1">
                              {(value as File).name}
                              <button
                                  onClick={() =>
                                      removeFile((value as File).name)
                                  }
                                  className="text-sm font-bold"
                                  type="button"
                              >
                                  ×
                              </button>
                          </div>
                      )}
            </div>
        </div>
    );
}
