'use client';

import FaqItem from './FaqItem';
import { FaqProps } from '@/types';
import React, { useState } from 'react';

const Faq:React.FC<FaqProps> = ({
    faqData,
    className,
}) => {
    const [openIndex, setOpenIndex] = useState(0);
    
    const handleToggle = (index: number): void => {
        setOpenIndex(openIndex === index ? -1 : index);
    };
    return (
        <div>

            <div className={className}>
                {faqData?.map((faq, index) => (
                    <FaqItem
                        key={index}
                        answer={faq.answer}
                        listAns={faq.listAns}
                        question={faq.question}
                        isOpen={openIndex === index} 
                        listAnsText={faq.listAnsText}
                        boldListAns={faq.boldListAns}
                        onToggle={() => handleToggle(index)}
                    />
                ))}
            </div>
        </div>
    )
}

export default Faq;