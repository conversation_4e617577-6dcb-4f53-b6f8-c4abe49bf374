'use client';

import Image from 'next/image';
import { X } from 'lucide-react';
import React, { useState } from 'react';
import { LoaderCircle } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { RequirementCardProps } from '@/types';
import { Button } from '@/components/ui/button';
import Missing from '@/app/assets/svg/missing';
import Pending from '@/app/assets/svg/pending';
import HelpIcon from '@/app/assets/svg/help.svg';
import Download from '@/app/assets/svg/download';
import UploadIcon from '@/app/assets/svg/upload';
import NoStatus from '@/app/assets/svg/no-status';
import Completed from '@/app/assets/svg/completed';
import NotApproved from '@/app/assets/svg/not-approved';
import SubmitQuestion from '@/app/assets/svg/submitquestion';
import ArrowUp from '@/app/assets/svg/keyboard_arrow_up.svg';
import DescriptionsWithSeeMore from './DescriptionsWithSeeMore';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';

const RequirementCard: React.FC<RequirementCardProps> = ({
    title,
    description,
    status = 'No Status',
    required = false,
    uploadedFiles = [],
    allowUpload = false,
    allowAnswer = false,
    file,
    RejectionNote,
    ResubmissionDate,
    targetedSection = 'PDFSection',
}) => {
    const [files, setFiles] = useState<(File | string)[]>(uploadedFiles);
    const [selectedFile, setSelectedFile] = useState<string | null>(null);
    const [isCollapsed, setIsCollapsed] = useState(true);
    const [charCount, setCharCount] = useState(0);
    const maxLength = 700;

    const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
        setCharCount(e.target.value.length);
    };
    const toggleCollapse = () => {
        setIsCollapsed(!isCollapsed);
    };

    const handleUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files) {
            const newFiles = [...files, ...Array.from(e.target.files)];
            setFiles(newFiles);
        }
    };

    const handleFileClick = (file: File | string) => {
        const fileUrl =
            typeof file === 'string' ? file : URL.createObjectURL(file);
        setSelectedFile((prevSelected) =>
            prevSelected === fileUrl ? null : fileUrl
        );
    };

    React.useEffect(() => {
        if (selectedFile) {
            const element = document.getElementById(targetedSection);
            if (element) {
                element.style.scrollMarginTop = "80px";
                element.scrollIntoView({ behavior: 'smooth' });
            }
        }
    }, [selectedFile, targetedSection]);

    return (
        <div className='border border-primaryColor/30 rounded-[10px] pt-4 bg-white md:spa'>
            <div className='flex flex-col gap-1'>
                <div className='flex flex-col md:flex-row justify-between md:items-center items-start px-4'>
                    <h3 className='font-semibold text-base text-graySix leading-5 flex md:gap-2 gap-1 flex-wrap '>
                        {allowAnswer ? (
                            <Image src={HelpIcon} alt='help icon' />
                        ) : null}
                        {title}
                        <div className='relative text-[10px] md:leading-[12px] font-medium'>
                            {required ? (
                                <span className='text-primaryColor py-[3px] px-1.5 bg-primaryColor/[8%] rounded-full md:absolute top-0'>
                                    Required
                                </span>
                            ) : (
                                <span className='text-primaryColor/70 py-0.5 px-1 bg-primaryColor/[8%] rounded-full md:absolute top-0'>
                                    Optional
                                </span>
                            )}
                        </div>
                    </h3>
                    {status && (
                        <span
                            className={`flex gap-2 font-semibold text-base leading-5 py-2.5 px-3 ${
                                status === 'Reviewing'
                                    ? 'text-primaryColor/60'
                                    : status === 'Missing'
                                    ? 'text-[#F2A735]'
                                    : status === 'Completed'
                                    ? 'text-primaryColor'
                                    : status === 'Rejected'
                                    ? 'text-grayThree'
                                    : ' text-grayTwo'
                            }`}
                        >
                            <div className='w-[18px] h-[18px]'>
                                {status === 'Reviewing' ? (
                                    <Pending />
                                ) : status === 'Completed' ? (
                                    <Completed />
                                ) : status === 'Missing' ? (
                                    <Missing />
                                ) : status === 'Rejected' ? (
                                    <NotApproved />
                                ) : (
                                    <NoStatus />
                                )}
                            </div>
                            {status}
                        </span>
                    )}
                </div>

                <div className='flex justify-between items-end text-grayFive text-sm leading-[17px] font-normal border-b border-primaryColor/30 px-4 pb-3'>
                    {/* {description} */}
                    <DescriptionsWithSeeMore description={description} />
                    {ResubmissionDate && (
                        <div className='text-xs leading-[15px] font-medium text-graySix'>
                            Resubmission Date: {ResubmissionDate}
                        </div>
                    )}
                </div>
            </div>

            <div className='px-4 py-4'>
                {allowUpload && (
                    <>
                        <div className='grid md:grid-cols-[auto_auto] grid-cols-1 gap-5'>
                            <div className='flex flex-wrap text-sm gap-2.5 items-center md:mt-0 mt-4'>
                                {files.length > 0 ? (
                                    <>
                                        <span className='text-sm leading-[17px] font-medium text-grayFive'>
                                            Documents:
                                        </span>
                                        {files.map((file, index) => (
                                            <div
                                                key={index}
                                                className='flex gap-2'
                                            >
                                                <div
                                                    className={` py-1 px-2 underline rounded-full flex gap-1 items-center cursor-pointer ${
                                                        status === 'Rejected'
                                                            ? 'text-[#FF3B30] bg-[#FF52520D]'
                                                            : 'text-secondaryColor bg-primaryOne'
                                                    }`}
                                                    onClick={() =>
                                                        handleFileClick(file)
                                                    }
                                                >
                                                    {typeof file === 'string'
                                                        ? file
                                                        : file.name}
                                                    <X size={16} />
                                                </div>
                                                <div className='text-grayFive py-1 px-2 bg-grayOne rounded-full flex gap-1 items-center'>
                                                    {typeof file === 'string'
                                                        ? file
                                                        : file.name}
                                                    <LoaderCircle
                                                        size={14}
                                                        className='animate-spin'
                                                    />
                                                </div>
                                            </div>
                                        ))}
                                    </>
                                ) : (
                                    <p className='text-grayFive'>
                                        No attached documents.
                                    </p>
                                )}
                            </div>
                            <div className='w-fit md:place-self-end flex gap-2.5 items-center'>
                                {files.length > 0 ? (
                                    <label className=' flex items-center gap-1 border border-primaryColor/30 text-primaryColor px-2.5 py-[7px] rounded-[6px] cursor-pointer hover:bg-secondaryColor text-xs leading-[18px] font-medium'>
                                        <Download className='w-4 h-4' />
                                    </label>
                                ) : null}
                                <label className=' flex items-center gap-1 border border-primaryColor bg-primaryColor text-white px-2.5 py-[7px] rounded-[6px] cursor-pointer hover:bg-secondaryColor text-xs leading-[18px] font-medium'>
                                    <UploadIcon className='w-4 h-4' />
                                    <input
                                        type='file'
                                        multiple
                                        className='hidden'
                                        onChange={handleUpload}
                                    />
                                </label>
                            </div>
                        </div>

                        {selectedFile && (
                            <div className='rounded-[8px] mt-4 p-4 bg-[#1E62E012]'>
                                <embed
                                    src={`${file}#view=FitH`}
                                    type='application/pdf'
                                    width='100%'
                                    title='Embedded PDF Viewer'
                                    className='md:h-[800px] h-[500px]'
                                    id='PDFSection'
                                />
                            </div>
                        )}
                    </>
                )}

                {allowAnswer && (
                    <>
                        <div
                            className='grid grid-cols-[auto_auto] gap-5 '
                            onClick={toggleCollapse}
                        >
                            <div className='flex gap-2.5 items-center text-sm leading-[17px] font-medium text-grayFive md:mt-0 mt-4'>
                                Questions Answered: 0
                            </div>
                            <Button className='md:mt-0 mt-4 w-fit place-self-end bg-primaryOne px-[13px] py-[14px] rounded-[6px] cursor-pointer hover:bg-primaryTwo shadow-none'>
                                <Image
                                    src={ArrowUp}
                                    alt='collapse'
                                    width={12}
                                    height={12}
                                    className={`transition-transform duration-200 ${
                                        isCollapsed ? 'rotate-180' : 'rotate-0'
                                    }`}
                                />
                            </Button>
                        </div>

                        {!isCollapsed && (
                            <div>
                                <div className='p-4 bg-primaryOne mt-4 rounded-[8px] flex flex-col gap-4'>
                                    <span className='font-normal text-sm leading-[17px] text-grayFive'>
                                        Please provide a copy of the
                                        applicant&apos;s English test scores:
                                    </span>
                                    <Label className='font-medium text-sm text-grayFive'>
                                        Would you like the University to
                                        purchase Overseas Student Health Cover
                                        (OSHC) on your behalf?*
                                    </Label>
                                    <Select>
                                        <SelectTrigger className='w-full bg-white boder border-primaryColor/20'>
                                            <SelectValue
                                                placeholder='Select'
                                                className='text-grayThree'
                                            />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value='light'>
                                                Light
                                            </SelectItem>
                                            <SelectItem value='dark'>
                                                Dark
                                            </SelectItem>
                                            <SelectItem value='system'>
                                                System
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <Label className='font-medium text-sm text-grayFive'>
                                        Do you currently hold an Australian
                                        Visa*
                                    </Label>
                                    <Select>
                                        <SelectTrigger className='w-full bg-white boder border-primaryColor/20'>
                                            <SelectValue
                                                placeholder='Select'
                                                className='text-grayThree'
                                            />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value='light'>
                                                Light
                                            </SelectItem>
                                            <SelectItem value='dark'>
                                                Dark
                                            </SelectItem>
                                            <SelectItem value='system'>
                                                System
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <Label className='font-medium text-sm text-grayFive'>
                                        Have you ever been refused a visa, had
                                        your visa cancelled or overstayed your
                                        visa in any country?*
                                    </Label>
                                    <Select>
                                        <SelectTrigger className='w-full bg-white boder border-primaryColor/20'>
                                            <SelectValue
                                                placeholder='Select'
                                                className='text-grayThree'
                                            />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value='light'>
                                                Light
                                            </SelectItem>
                                            <SelectItem value='dark'>
                                                Dark
                                            </SelectItem>
                                            <SelectItem value='system'>
                                                System
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <Label className='font-medium text-sm text-grayFive'>
                                        Have you ever been excluded from an
                                        Australian education institution
                                        before?*
                                    </Label>
                                    <Select>
                                        <SelectTrigger className='w-full bg-white boder border-primaryColor/20'>
                                            <SelectValue
                                                placeholder='Select'
                                                className='text-grayThree'
                                            />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value='light'>
                                                Light
                                            </SelectItem>
                                            <SelectItem value='dark'>
                                                Dark
                                            </SelectItem>
                                            <SelectItem value='system'>
                                                System
                                            </SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <div className='flex justify-end mt-2'>
                                        <Button className='py-[9px] px-2.5 text-xs leading-[15px] text-white bg-primaryColor hover:bg-secondaryColor'>
                                            <SubmitQuestion /> Submit
                                        </Button>
                                    </div>
                                </div>

                                <div className='p-4 bg-primaryOne mt-4 rounded-[8px] flex flex-col gap-4'>
                                    <span className='font-normal text-sm leading-[17px] text-grayFive'>
                                        Please provide a copy of the
                                        applicant&apos;s English test scores:
                                    </span>
                                    <Label className='font-medium text-sm text-grayFive flex justify-between'>
                                        Would you like the University to
                                        purchase Overseas Student Health Cover
                                        (OSHC) on your behalf?*
                                        <span className='text-xs text-primaryColor'>
                                            {charCount}/{maxLength}
                                        </span>
                                    </Label>
                                    <textarea
                                        maxLength={maxLength}
                                        onChange={handleInputChange}
                                        className='rounded-[8px] border border-primaryColor/20 h-32 py-1.5 pl-3.5 pr-1'
                                    />
                                    <div className='flex justify-end mt-2'>
                                        <Button className='py-[9px] px-2.5 text-xs leading-[15px] text-white bg-primaryColor hover:bg-secondaryColor'>
                                            <SubmitQuestion />
                                            Submit
                                        </Button>
                                    </div>
                                </div>

                                <div className='p-4 bg-primaryOne mt-4 rounded-[8px] flex flex-col gap-4'>
                                    <span className='font-normal text-sm leading-[17px] text-grayFive'>
                                        Please provide a copy of the
                                        applicant&apos;s English test scores:
                                    </span>
                                    <Label className='font-medium text-sm text-grayFive'>
                                        Would you like the University to
                                        purchase Overseas Student Health Cover
                                        (OSHC) on your behalf?*
                                    </Label>
                                    <div className='flex gap-3 text-sm leading-[24px] font-normal text-grayFive'>
                                        <input type='checkbox' id='option' />
                                        <label htmlFor='option'>
                                            Yes, I acknowledge.
                                        </label>
                                    </div>
                                    <div className='flex justify-end mt-2'>
                                        <Button className='py-[9px] px-2.5 text-xs leading-[15px] text-white bg-primaryColor hover:bg-secondaryColor'>
                                            <SubmitQuestion />
                                            Submit
                                        </Button>
                                    </div>
                                </div>

                                <div className='p-4 bg-primaryOne mt-4 rounded-[8px] flex flex-col gap-4'>
                                    <span className='font-normal text-sm leading-[17px] text-grayFive'>
                                        Please provide a copy of the
                                        applicant&apos;s English test scores:
                                    </span>
                                    <Label className='font-medium text-sm text-grayFive'>
                                        Would you like the University to
                                        purchase Overseas Student Health Cover
                                        (OSHC) on your behalf?*
                                    </Label>
                                    <div className='flex gap-3 text-sm leading-[24px] font-normal text-grayFive'>
                                        <input
                                            name='radiobutton'
                                            type='radio'
                                            id='yes'
                                        />
                                        <label htmlFor='yes'>Yes</label>
                                        <input
                                            name='radiobutton'
                                            type='radio'
                                            id='no'
                                        />
                                        <label htmlFor='no'>No</label>
                                    </div>
                                    <div className='flex justify-end mt-2'>
                                        <Button className='py-[9px] px-2.5 text-xs leading-[15px] text-white bg-primaryColor hover:bg-secondaryColor'>
                                            <SubmitQuestion />
                                            Submit
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        )}
                    </>
                )}
            </div>

            {RejectionNote && (
                <div className='p-4 flex gap-2.5 border-t text-grayFive border-primaryColor/30'>
                    <span className='font-normal text-sm leading-[17px]'>
                        Rejection Note:
                    </span>
                    <p className='text-sm leading-[17px]'>{RejectionNote}</p>
                </div>
            )}
        </div>
    );
};

export default RequirementCard;
