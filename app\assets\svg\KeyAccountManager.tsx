import React from 'react'

const KeyAccountManager = () => {
    return (
        <svg width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_2339_19197" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="18" height="19">
                <rect y="0.5" width="18" height="18" fill="#1E62E0" />
            </mask>
            <g mask="url(#mask0_2339_19197)">
                <path d="M12.75 12.5C12.125 12.5 11.5938 12.2812 11.1562 11.8438C10.7188 11.4062 10.5 10.875 10.5 10.25C10.5 9.625 10.7188 9.09375 11.1562 8.65625C11.5938 8.21875 12.125 8 12.75 8C13.375 8 13.9062 8.21875 14.3438 8.65625C14.7812 9.09375 15 9.625 15 10.25C15 10.875 14.7812 11.4062 14.3438 11.8438C13.9062 12.2812 13.375 12.5 12.75 12.5ZM12.75 11C12.9625 11 13.1406 10.9281 13.2844 10.7844C13.4281 10.6406 13.5 10.4625 13.5 10.25C13.5 10.0375 13.4281 9.85938 13.2844 9.71563C13.1406 9.57188 12.9625 9.5 12.75 9.5C12.5375 9.5 12.3594 9.57188 12.2156 9.71563C12.0719 9.85938 12 10.0375 12 10.25C12 10.4625 12.0719 10.6406 12.2156 10.7844C12.3594 10.9281 12.5375 11 12.75 11ZM8.25 17.75V15.575C8.25 15.3125 8.3125 15.0656 8.4375 14.8344C8.5625 14.6031 8.7375 14.4187 8.9625 14.2812C9.3625 14.0437 9.78437 13.8469 10.2281 13.6906C10.6719 13.5344 11.125 13.4187 11.5875 13.3438L12.75 14.75L13.9125 13.3438C14.375 13.4187 14.825 13.5344 15.2625 13.6906C15.7 13.8469 16.1187 14.0437 16.5187 14.2812C16.7437 14.4187 16.9219 14.6031 17.0531 14.8344C17.1844 15.0656 17.25 15.3125 17.25 15.575V17.75H8.25ZM9.73125 16.25H12.0375L11.025 15.0125C10.8 15.075 10.5813 15.1563 10.3687 15.2563C10.1562 15.3563 9.94375 15.4625 9.73125 15.575V16.25ZM13.4625 16.25H15.75V15.575C15.55 15.45 15.3438 15.3406 15.1313 15.2469C14.9188 15.1531 14.7 15.075 14.475 15.0125L13.4625 16.25ZM3.75 16.25C3.3375 16.25 2.98438 16.1031 2.69063 15.8094C2.39688 15.5156 2.25 15.1625 2.25 14.75V4.25C2.25 3.8375 2.39688 3.48438 2.69063 3.19063C2.98438 2.89688 3.3375 2.75 3.75 2.75H14.25C14.6625 2.75 15.0156 2.89688 15.3094 3.19063C15.6031 3.48438 15.75 3.8375 15.75 4.25V8C15.55 7.75 15.3313 7.5125 15.0938 7.2875C14.8562 7.0625 14.575 6.9125 14.25 6.8375V4.25H3.75V14.75H6.8625C6.825 14.8875 6.79688 15.025 6.77812 15.1625C6.75938 15.3 6.75 15.4375 6.75 15.575V16.25H3.75ZM5.25 7.25H10.5C10.825 7 11.1813 6.8125 11.5688 6.6875C11.9563 6.5625 12.35 6.5 12.75 6.5V5.75H5.25V7.25ZM5.25 10.25H9C9 9.9875 9.02813 9.73125 9.08438 9.48125C9.14062 9.23125 9.21875 8.9875 9.31875 8.75H5.25V10.25ZM5.25 13.25H7.8375C7.975 13.1375 8.12188 13.0375 8.27813 12.95C8.43438 12.8625 8.59375 12.7812 8.75625 12.7063V11.75H5.25V13.25ZM3.75 14.75V4.25V6.81875V6.5V14.75Z" fill="#1E62E0" />
            </g>
        </svg>

    )
}

export default KeyAccountManager