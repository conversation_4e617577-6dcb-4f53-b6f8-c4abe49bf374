import React from 'react'

const Settings = () => {
    return (
        <div>
            <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                <mask id="mask0_3840_9243" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="25">
                <rect y="0.5" width="24" height="24" fill="#D9D9D9"/>
                </mask>
                <g mask="url(#mask0_3840_9243)">
                <path d="M10.8255 22.5C10.3755 22.5 9.98797 22.35 9.66297 22.05C9.33797 21.75 9.14214 21.3833 9.07547 20.95L8.85047 19.3C8.6338 19.2167 8.42964 19.1167 8.23797 19C8.0463 18.8833 7.8588 18.7583 7.67547 18.625L6.12547 19.275C5.7088 19.4583 5.29214 19.475 4.87547 19.325C4.4588 19.175 4.1338 18.9083 3.90047 18.525L2.72547 16.475C2.49214 16.0917 2.42547 15.6833 2.52547 15.25C2.62547 14.8167 2.85047 14.4583 3.20047 14.175L4.52547 13.175C4.5088 13.0583 4.50047 12.9458 4.50047 12.8375V12.1625C4.50047 12.0542 4.5088 11.9417 4.52547 11.825L3.20047 10.825C2.85047 10.5417 2.62547 10.1833 2.52547 9.75C2.42547 9.31667 2.49214 8.90833 2.72547 8.525L3.90047 6.475C4.1338 6.09167 4.4588 5.825 4.87547 5.675C5.29214 5.525 5.7088 5.54167 6.12547 5.725L7.67547 6.375C7.8588 6.24167 8.05047 6.11667 8.25047 6C8.45047 5.88333 8.65047 5.78333 8.85047 5.7L9.07547 4.05C9.14214 3.61667 9.33797 3.25 9.66297 2.95C9.98797 2.65 10.3755 2.5 10.8255 2.5H13.1755C13.6255 2.5 14.013 2.65 14.338 2.95C14.663 3.25 14.8588 3.61667 14.9255 4.05L15.1505 5.7C15.3671 5.78333 15.5713 5.88333 15.763 6C15.9546 6.11667 16.1421 6.24167 16.3255 6.375L17.8755 5.725C18.2921 5.54167 18.7088 5.525 19.1255 5.675C19.5421 5.825 19.8671 6.09167 20.1005 6.475L21.2755 8.525C21.5088 8.90833 21.5755 9.31667 21.4755 9.75C21.3755 10.1833 21.1505 10.5417 20.8005 10.825L19.4755 11.825C19.4921 11.9417 19.5005 12.0542 19.5005 12.1625V12.8375C19.5005 12.9458 19.4838 13.0583 19.4505 13.175L20.7755 14.175C21.1255 14.4583 21.3505 14.8167 21.4505 15.25C21.5505 15.6833 21.4838 16.0917 21.2505 16.475L20.0505 18.525C19.8171 18.9083 19.4921 19.175 19.0755 19.325C18.6588 19.475 18.2421 19.4583 17.8255 19.275L16.3255 18.625C16.1421 18.7583 15.9505 18.8833 15.7505 19C15.5505 19.1167 15.3505 19.2167 15.1505 19.3L14.9255 20.95C14.8588 21.3833 14.663 21.75 14.338 22.05C14.013 22.35 13.6255 22.5 13.1755 22.5H10.8255ZM11.0005 20.5H12.9755L13.3255 17.85C13.8421 17.7167 14.3213 17.5208 14.763 17.2625C15.2046 17.0042 15.6088 16.6917 15.9755 16.325L18.4505 17.35L19.4255 15.65L17.2755 14.025C17.3588 13.7917 17.4171 13.5458 17.4505 13.2875C17.4838 13.0292 17.5005 12.7667 17.5005 12.5C17.5005 12.2333 17.4838 11.9708 17.4505 11.7125C17.4171 11.4542 17.3588 11.2083 17.2755 10.975L19.4255 9.35L18.4505 7.65L15.9755 8.7C15.6088 8.31667 15.2046 7.99583 14.763 7.7375C14.3213 7.47917 13.8421 7.28333 13.3255 7.15L13.0005 4.5H11.0255L10.6755 7.15C10.1588 7.28333 9.67964 7.47917 9.23797 7.7375C8.7963 7.99583 8.39214 8.30833 8.02547 8.675L5.55047 7.65L4.57547 9.35L6.72547 10.95C6.64214 11.2 6.5838 11.45 6.55047 11.7C6.51714 11.95 6.50047 12.2167 6.50047 12.5C6.50047 12.7667 6.51714 13.025 6.55047 13.275C6.5838 13.525 6.64214 13.775 6.72547 14.025L4.57547 15.65L5.55047 17.35L8.02547 16.3C8.39214 16.6833 8.7963 17.0042 9.23797 17.2625C9.67964 17.5208 10.1588 17.7167 10.6755 17.85L11.0005 20.5ZM12.0505 16C13.0171 16 13.8421 15.6583 14.5255 14.975C15.2088 14.2917 15.5505 13.4667 15.5505 12.5C15.5505 11.5333 15.2088 10.7083 14.5255 10.025C13.8421 9.34167 13.0171 9 12.0505 9C11.0671 9 10.238 9.34167 9.56297 10.025C8.88797 10.7083 8.55047 11.5333 8.55047 12.5C8.55047 13.4667 8.88797 14.2917 9.56297 14.975C10.238 15.6583 11.0671 16 12.0505 16Z" fill="currentColor"/>
                </g>
            </svg>
        </div>
    )
}

export default Settings