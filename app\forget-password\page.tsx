import React from 'react';
import CheckEmail from '../components/CheckEmail';
import AuthLayout from '../components/layout/AuthLayout';
import CreateNewPassword from '../components/CreateNewPassword';
import ForgetPasswordForm from '../components/ForgetPasswordForm';
import ForgetPasswordCover from '../assets/svg/ForgetPasswordCover';


const page = () => {
    return (
        <>
        <AuthLayout
            imageSrc={<ForgetPasswordCover />}
            description='Every reset is a fresh start—unlock your potential with a new password!'
            title='Forgot Password'
            heading='Enter the email associated with your account and we’ll send an email with instructions to reset your password.'
        >
            <div className='pt-9 max-w-[420px] mx-auto'>
                <ForgetPasswordForm />
                {/* <CheckEmail /> */}
                {/* <CreateNewPassword /> */}
            </div>
        </AuthLayout>
        </>
    )
}

export default page;
