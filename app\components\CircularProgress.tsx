'use client';

import { CircularProgressProps } from '@/types';
import React, { useEffect, useState } from 'react';


const CircularProgress: React.FC<CircularProgressProps> = ({
    value,
    size = 100,
    strokeWidth = 10,
    trackColor = '#F4F7FE',
    centerText = 'Complete',
    animationDuration = 1000,
    progressColor = '#1E62E0',
}) => {
    const [animatedValue, setAnimatedValue] = useState(0);
    const radius = (size - strokeWidth) / 2;
    const circumference = 2 * Math.PI * radius;
    const offset = circumference - (animatedValue  / 100) * circumference;

    useEffect(() => {
        const timeout = setTimeout(() => setAnimatedValue(value), 100);
        return () => clearTimeout(timeout);
    }, [value]);

    return (
        <div style={{ position: 'relative', width: size, height: size }}>
        <svg width={size} height={size} style={{ transform: 'rotate(-90deg)' }}>
            <circle
                cx={size / 2}
                cy={size / 2}
                r={radius}
                fill='none'
                stroke={trackColor}
                strokeWidth={strokeWidth}
            />
            <circle
                cx={size / 2}
                cy={size / 2}
                r={radius}
                fill='none'
                stroke={progressColor}
                strokeWidth={strokeWidth}
                strokeDasharray={circumference}
                strokeDashoffset={offset}
                strokeLinecap='round'
                style={{ transition: `stroke-dashoffset ${animationDuration}ms ease-in-out` }}
            />
        </svg>
        <div
            style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            textAlign: 'center',
            }}
        >
            <div style={{ fontSize: '20px', fontWeight: 600, lineHeight: '36px' }}>{animatedValue}%</div>
            <div style={{ fontSize: '12px', color: '#6b7280' }}>{centerText}</div>
        </div>
        </div>
    );
};

export { CircularProgress };
