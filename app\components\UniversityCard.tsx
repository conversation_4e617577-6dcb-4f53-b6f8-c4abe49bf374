import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Rank from '@/app/assets/svg/rank';
import { UniversityCardProps } from '@/types';
import Distance from '@/app/assets/svg/distance';
import { Checkbox } from '@/components/ui/checkbox';
import AttachMoney from '@/app/assets/svg/attach_money';

const UniversityCard: React.FC<UniversityCardProps> = ({
    onSelect, 
    isChecked, 
    isSelected, 
    university, 
}) => {
    const wrapperClasses = `space-y-2 rounded-xl p-4 transition-all cursor-pointer ${
        isSelected
          ? ' bg-primaryThree'
          : ' bg-white'
    }`;

    const content = (
        <>
        <div className='flex gap-2.5 items-center justify-between mb-4'>
            <div className='flex gap-2.5'>
                <div className='max-w-[20px] w-auto max-h-[20px] h-auto'>
                    <Image src={university.universityLogo} alt='University logo' />
                </div>
                <div>
                    <h3 className='font-medium text-sm text-graySix'>
                        {university.universityName}
                    </h3>
                </div>
            </div>
            {isChecked === true && (
                <Checkbox
                    id={`course-${university.id}`}
                    className='w-5 h-5 rounded-[50px] border border-[#D0D5DD] data-[state=checked]:bg-[#144296]'
                    checked={isSelected}
                    onCheckedChange={() => onSelect(university.id)}
                />
            )}
        </div>
        <p className='flex font-normal text-xs text-grayFour'>
            <span className='flex gap-1.5 items-center'>
                <Rank />
                Rank:
            </span>
            <span className='ml-2 font-semibold text-xs text-grayFive'>
                {' '}{university.rank}
            </span>
        </p>
        <ul className='ml-4 list-disc font-normal text-xs text-grayFour'>
            <li>{university.type}</li>
        </ul>
        <p className='flex font-normal text-xs text-grayFour'>
            <AttachMoney />
            Tuition Fees:{' '}
            <span className='ml-2 font-semibold text-xs text-grayFive'>
                ${university.tuitionFees}
            </span>
        </p>
        <div className='flex justify-between'>
            <p className='flex gap-1.5 font-normal text-xs text-grayFour'>
                <Distance />
                {university.location}
            </p>
            <div className='max-w-[22px] w-auto max-h-[22px] h-auto'>
                {<university.country.logo />}
            </div>
        </div>
        </>
    );
    
    return !isChecked ? (
        <Link href={`/student-individual/universities/${university.slug}`} className={wrapperClasses}>
            {content}
        </Link>
        ) : (
        <label htmlFor={`course-${university.id}`} className={wrapperClasses}>
            {content}
        </label>
    );
}
export default UniversityCard;