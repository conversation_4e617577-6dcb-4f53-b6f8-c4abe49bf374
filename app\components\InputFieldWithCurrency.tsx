import { ChevronDown, ChevronUp } from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';

interface CurrencyOption {
    code: string;
    symbol: string;
    label: string;
}

interface CurrencyInputProps {
    label?: string;
    value: number | string;
    onChange: (value: number | string) => void;
    placeholder?: string;
    disabled?: boolean;
    error?: string;
    required?: boolean;
    currencyOptions?: CurrencyOption[];
    defaultCurrency?: string;
    selectedCurrency?: CurrencyOption;
    onCurrencyChange?: (currency: CurrencyOption) => void;
    className?: string;
}

const defaultCurrencyOptions: CurrencyOption[] = [
    { code: 'USD', symbol: '$', label: 'USD ($)' },
    { code: 'EUR', symbol: '€', label: 'EUR (€)' },
    { code: 'GBP', symbol: '£', label: 'GBP (£)' },
    { code: 'JPY', symbol: '¥', label: 'JPY (¥)' },
    { code: 'CAD', symbol: 'C$', label: 'CAD (C$)' },
];

const InputFieldWithCurrency: React.FC<CurrencyInputProps> = ({
    label = 'Standard Application Fee',
    value,
    onChange,
    placeholder = '0.00',
    disabled = false,
    error,
    required = false,
    currencyOptions = defaultCurrencyOptions,
    defaultCurrency = 'USD',
    selectedCurrency: propSelectedCurrency, 
    onCurrencyChange,
    className = '',
}) => {
    const [dropdownOpen, setDropdownOpen] = useState(false);

    const initialCurrency =
        propSelectedCurrency ||
        currencyOptions.find((option) => option.code === defaultCurrency) ||
        currencyOptions[0];

    const [internalSelectedCurrency, setInternalSelectedCurrency] =
        useState<CurrencyOption>(initialCurrency);

    useEffect(() => {
        if (propSelectedCurrency) {
            setInternalSelectedCurrency(propSelectedCurrency);
        }
    }, [propSelectedCurrency]);

    const dropdownRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (
                dropdownRef.current &&
                !dropdownRef.current.contains(event.target as Node)
            ) {
                setDropdownOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const inputValue = e.target.value;

        if (!inputValue) {
            onChange('');
            return;
        }

        const numericValue = inputValue.replace(/[^0-9.]/g, '');

        const parts = numericValue.split('.');
        const formattedValue =
            parts[0] + (parts.length > 1 ? '.' + parts[1] : '');

        onChange(formattedValue);
    };

    const handleCurrencySelection = (currency: CurrencyOption) => {
        setInternalSelectedCurrency(currency);

        if (onCurrencyChange) {
            onCurrencyChange(currency);
        }

        setDropdownOpen(false);
    };

    const displayCurrency = propSelectedCurrency || internalSelectedCurrency;

    return (
        <div className={`w-full ${className}`}>
            {label && (
                <label className="block text-graySix text-sm font-medium mb-1.5">
                    {label}
                </label>
            )}

            <div className="relative">
                <div
                    className={`flex items-center rounded-md border border-primaryColor/20`}
                >
                    <div className="relative" ref={dropdownRef}>
                        <button
                            type="button"
                            className="flex items-center justify-between px-3 py-2 text-gray-700 text-sm rounded-l-md focus:outline-none"
                            onClick={() => setDropdownOpen(!dropdownOpen)}
                            disabled={disabled}
                        >
                            <span>{displayCurrency.label}</span>
                            <ChevronUp
                                className={`ml-2 h-4 w-4 transition-all ${
                                    dropdownOpen ? 'rotate-180' : ''
                                }`}
                            />
                        </button>

                        {dropdownOpen && (
                            <div className="absolute z-10 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                                {currencyOptions.map((currency) => (
                                    <div
                                        key={currency.code}
                                        className="cursor-pointer select-none py-2 px-3 text-gray-900 hover:bg-blue-100"
                                        onClick={() =>
                                            handleCurrencySelection(currency)
                                        }
                                    >
                                        {currency.label}
                                    </div>
                                ))}
                            </div>
                        )}
                    </div>

                    <input
                        type="text"
                        // value={value}
                        // onChange={handleInputChange}
                        placeholder={placeholder}
                        disabled={disabled}
                        className={`flex-1 px-3.5 py-2.5 outline-none rounded-r-md ${
                            disabled ? 'bg-gray-100 text-gray-500' : 'bg-white'
                        }`}
                        aria-invalid={!!error}
                    />
                </div>

                {error && <p className="mt-1 text-sm text-red-600">{error}</p>}
            </div>
        </div>
    );
};

export default InputFieldWithCurrency;
