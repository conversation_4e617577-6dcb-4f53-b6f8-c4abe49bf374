'use client';

import { useState } from 'react';
import "react-phone-number-input/style.css";
import PhoneInput, {
    formatPhoneNumber,
    formatPhoneNumberIntl,
    isPossiblePhoneNumber,
    isValidPhoneNumber,
} from 'react-phone-number-input';

const DemoPhone = () => {
    const [value, setValue] = useState<string>('');
    
    return (
        <div className=''>
            <label htmlFor="phone-input" className="mb-2 text-sm font-medium text-gray-700">
                Phone<span className="text-red-500">*</span>
            </label>
            <PhoneInput
                defaultCountry='BD'
                value={value}
                onChange={(value) => setValue(value || '')}
                placeholder="Enter phone number"
                className='border rounded-full px-4 py-2.5 focus:!border-none'
                numberInputProps={{ className: " pl-2 rounded-r-full focus:!border-none" }}
                international
                smartCaret
            />
            <div className="mt-4 space-y-2">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                    Is possible:{' '}
                    <span className="font-medium">
                        {value && isPossiblePhoneNumber(value) ? 'true' : 'false'}
                    </span>
                </span>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                    Is valid:{' '}
                    <span className="font-medium">
                        {value && isValidPhoneNumber(value) ? 'true' : 'false'}
                    </span>
                </span>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                    National: <span className="font-medium">{value && formatPhoneNumber(value)}</span>
                </span>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                    International: <span className="font-medium">{value && formatPhoneNumberIntl(value)}</span>
                </span>
                <span>
                    value: {value}
                </span>
            </div>
        {/* <div className='flex'>
            <PhoneInput
                defaultCountry={"CA"}
                placeholder={"Phone"}
                value={value}
                onChange={(value) => setValue(value || '')}
                className='flex '

                style={{
                    display: 'flex',
                    alignItems: 'center',
                    border: '1px solid #d4d4d8',
                    borderRadius: '4px',
                    backgroundColor: '#ffffff',
                    height: '44px',
                    paddingLeft: '12px',
                }}
                inputComponent={({ ref, ...props }) => (
                    <input
                        {...props}
                        ref={ref}
                        style={{
                            flex: 1,
                            minWidth: 0,
                            border: 'none',
                            outline: 'none',
                        }}
                    />
                )}
                flagComponent={({ country }) => (
                    <div
                        style={{
                            width: `10px`,
                            height: '10px',
                        }}
                    >
                        <img
                            src={`https://flagcdn.com/w40/${country.toLowerCase()}.png`}
                            alt={country}
                            width={5}
                            height={5}
                            style={{
                                display: 'block',
                                width: '100%',
                                height: '100%',
                            }}
                        />
                    </div>
                )}
                
            />
        </div> */}
        </div>
    );
};

export default DemoPhone;
