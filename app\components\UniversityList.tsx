'use client';

import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';

interface University {
    name: string;
    country: string;
    alpha_two_code: string;
    domains: string[];
    web_pages: string[];
    state_province: string | null;
}

const fetchUniversities = async (): Promise<University[]> => {
    const response = await fetch('http://universities.hipolabs.com/search?country=india');
    if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
    }
    return response.json();
  }

const UniversityList: React.FC = () => {
    const [showData, setShowData] = useState(false);

    const {data: universities = [], isLoading, isFetching, error, refetch, isFetched} = useQuery<University[]>({
        queryKey: ['universities', 'india'],
        queryFn: fetchUniversities,
        enabled: false, // don't fetch on mount
        staleTime: 1000 * 60 * 60 * 24, // 24h
    });

    const handleToggle = async () => {
        setShowData(prev => !prev);
        if (!isFetched) {
            await refetch(); // fetch only first time, React Query handles the rest
        }
    };

    return (
        <div className='p-4'>
            <button
                className='border border-grayFour text-graySix px-4 py-3 rounded-md'
                onClick={handleToggle}
            >
                Toggle data
            </button>

            {showData && (
                <>
                    {isLoading ? (
                        <div className='flex justify-center items-center h-screen'>
                            <div className='animate-spin rounded-full h-32 w-32 border-t-2 border-b-2 border-blue-500'></div>
                        </div>
                    ) : error ? (
                        <div className='flex justify-center items-center h-screen text-red-500'>
                            Error: {error instanceof Error ? error.message : 'An unknown error occurred'}
                        </div>
                    ) : (
                    <>
                        <div className='flex items-center mb-4 mt-4'>
                            <h1 className='text-2xl font-bold mr-4'>Universities in India</h1>
                            {isFetching && <div className='animate-pulse w-4 h-4 bg-blue-500 rounded-full'></div>}
                        </div>
                        <p className='mb-4'>Found {universities.length} universities</p>

                        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>
                            {universities.map((uni, index) => (
                                <div
                                    key={index}
                                    className='border rounded-lg p-4 shadow-md transition-transform hover:scale-105'
                                >
                                    <h2 className='text-xl font-semibold'>{uni.name}</h2>
                                    <p className='text-gray-600'>{uni.country}</p>
                                    <div className='mt-4'>
                                        {uni.web_pages?.[0] && (
                                          <a
                                              href={uni.web_pages[0]}
                                              target='_blank'
                                              rel='noopener noreferrer'
                                              className='text-blue-500 hover:underline'
                                          >
                                              Visit Website
                                          </a>
                                        )}
                                    </div>
                                </div>
                            ))}
                        </div>
                    </>
                )}
              </>
            )}
        </div>
    )};

export default UniversityList;
