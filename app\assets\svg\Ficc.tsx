import React from 'react'

const Ficc = () => {
    return (
        <svg width="140" height="70" viewBox="0 0 140 70" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_6753_41072)">
                <path d="M124.753 47.3831L119.741 42.3711C119.575 42.2049 119.379 42.1239 119.153 42.1239C119.049 42.1239 118.94 42.1452 118.827 42.19C118.488 42.337 118.318 42.5948 118.318 42.9592V45.4652H116.229C115.307 45.4652 114.452 45.5121 113.666 45.608C112.88 45.7039 112.185 45.836 111.584 46.0065C110.983 46.177 110.442 46.3879 109.958 46.6394C109.474 46.8908 109.065 47.1594 108.724 47.4428C108.385 47.7262 108.087 48.048 107.829 48.4081C107.571 48.7682 107.369 49.1241 107.215 49.4714C107.064 49.8188 106.941 50.2024 106.851 50.62C106.759 51.0377 106.698 51.4298 106.668 51.7942C106.638 52.1586 106.623 52.5549 106.623 52.9811C106.623 53.4691 106.7 53.9997 106.851 54.5729C106.913 54.8052 106.979 55.0141 107.045 55.2186C107.143 55.5234 107.245 55.811 107.354 56.0604C107.537 56.478 107.752 56.9106 108 57.3581C108.247 57.8056 108.419 58.1039 108.515 58.251C108.611 58.398 108.703 58.5344 108.79 58.6559C108.878 58.7688 108.991 58.8263 109.129 58.8263C109.163 58.8263 109.216 58.8178 109.287 58.8008C109.374 58.7581 109.43 58.7027 109.474 58.6409C109.485 58.6239 109.496 58.609 109.506 58.5919C109.54 58.5237 109.56 58.447 109.547 58.3554C109.357 56.9383 109.338 55.7279 109.485 54.7179C109.64 53.6439 109.979 52.7957 110.514 52.182C111.516 51.042 113.421 50.473 116.232 50.473H118.32V52.979C118.32 53.3455 118.49 53.6012 118.829 53.7483C118.942 53.7909 119.051 53.8143 119.155 53.8143C119.39 53.8143 119.586 53.7312 119.743 53.5671L124.755 48.5551C124.922 48.3889 125.003 48.195 125.003 47.967C125.003 47.7411 124.919 47.5451 124.755 47.381L124.753 47.3831Z" fill="#2A4EA2" />
                <path d="M109.296 58.7987C109.296 58.7987 109.285 58.8029 109.279 58.805C109.213 58.82 109.164 58.8285 109.132 58.8285C108.993 58.8285 108.88 58.7731 108.793 58.658C108.706 58.5365 108.614 58.4002 108.518 58.2531C108.422 58.1061 108.249 57.8078 108.002 57.3603C107.755 56.9128 107.54 56.4802 107.357 56.0625C107.261 55.8451 107.171 55.6001 107.086 55.338C107.069 55.289 107.005 55.2762 106.973 55.3188C106.928 55.3785 106.884 55.4403 106.845 55.5127C105.914 57.258 104.603 58.6367 102.916 59.6446C101.228 60.6547 99.3932 61.1576 97.4136 61.1576C94.3471 61.1576 91.7665 60.073 89.6718 57.8973C88.6617 56.8488 87.8967 55.647 87.3725 54.2874C86.8483 52.93 86.6245 51.5129 86.7034 50.0383C86.7418 48.602 87.0422 47.2638 87.6048 46.0214C88.1674 44.7812 88.9345 43.703 89.9041 42.7909C90.8737 41.8789 91.9903 41.1522 93.2518 40.6067C94.5133 40.0633 95.8622 39.7735 97.2985 39.733C98.1637 39.733 99.0033 39.8289 99.8173 40.0228C101.053 40.3169 102.234 39.3622 102.234 38.0921C102.234 37.2163 101.626 36.4556 100.772 36.2617C99.6916 36.0145 98.5728 35.8909 97.4136 35.8909C95.4339 35.8909 93.5608 36.268 91.7964 37.0267C90.0298 37.7832 88.487 38.8124 87.1679 40.1123C85.8489 41.4143 84.8004 42.9358 84.0248 44.6832C83.247 46.4306 82.84 48.293 82.8016 50.2727C82.7633 52.2907 83.1213 54.2022 83.8777 56.0071C84.6342 57.812 85.672 59.3847 86.9932 60.7229C88.3123 62.0611 89.8743 63.1202 91.6792 63.8959C93.4841 64.6737 95.3956 65.0615 97.4136 65.0615C100.092 65.0615 102.585 64.3732 104.895 62.9945C106.76 61.8821 108.284 60.4288 109.477 58.6452C109.434 58.7049 109.381 58.7582 109.298 58.8008L109.296 58.7987Z" fill="#2A4EA2" />
                <path d="M85.4899 54.4664C84.7974 54.4941 84.2646 54.8436 83.8938 55.5127C82.9626 57.258 81.6521 58.6367 79.9644 59.6446C78.2766 60.6547 76.4419 61.1576 74.4622 61.1576C71.3958 61.1576 68.8152 60.073 66.7205 57.8973C65.7104 56.8488 64.9454 55.647 64.4212 54.2874C63.8969 52.93 63.6732 51.5129 63.752 50.0383C63.7904 48.602 64.0909 47.2638 64.6534 46.0214C65.216 44.7812 65.9832 43.703 66.9527 42.7909C67.9223 41.8789 69.039 41.1522 70.3005 40.6067C71.562 40.0633 72.9109 39.7735 74.3472 39.733C75.2294 39.733 76.086 39.8331 76.915 40.0356C78.1232 40.3297 79.2846 39.4176 79.2846 38.1731V38.041C79.2846 37.2121 78.7263 36.4747 77.9186 36.2851C76.8084 36.023 75.6556 35.8909 74.4644 35.8909C72.4847 35.8909 70.6116 36.268 68.8472 37.0267C67.0806 37.7832 65.5378 38.8124 64.2187 40.1123C62.8997 41.4143 61.8512 42.9358 61.0756 44.6832C60.2978 46.4306 59.8908 48.293 59.8524 50.2727C59.814 52.2907 60.172 54.2022 60.9285 56.0071C61.685 57.812 62.7228 59.3847 64.044 60.7229C65.363 62.0611 66.925 63.1202 68.73 63.8959C70.5349 64.6737 72.4463 65.0615 74.4644 65.0615C77.143 65.0615 79.6362 64.3732 81.9462 62.9945C84.254 61.6179 86.0504 59.7256 87.3311 57.3176C87.6571 56.7018 87.6614 56.103 87.3439 55.5191C86.9837 54.8564 86.2443 54.4366 85.4921 54.4664H85.4899Z" fill="#2A4EA2" />
                <path d="M55.2044 65.0594C54.6994 65.0594 54.2625 64.8846 53.8939 64.5351C53.5252 64.1857 53.3398 63.7403 53.3398 63.1969V41.947C53.3398 41.4803 53.5231 41.084 53.8939 40.7537C54.2625 40.4234 54.6994 40.2593 55.2044 40.2593C55.7478 40.2593 56.2038 40.4255 56.5725 40.7537C56.9412 41.084 57.1266 41.4803 57.1266 41.947V63.1969C57.1266 63.7403 56.9412 64.1857 56.5725 64.5351C56.2038 64.8846 55.7478 65.0594 55.2044 65.0594Z" fill="#2A4EA2" />
                <path d="M55.2044 38.5694C54.6994 38.5694 54.2625 38.3947 53.8939 38.0452C53.5252 37.6957 53.3398 37.2503 53.3398 36.7069C53.3398 36.1635 53.5231 35.7182 53.8939 35.3687C54.2625 35.0192 54.6994 34.8445 55.2044 34.8445C55.7478 34.8445 56.2038 35.0192 56.5725 35.3687C56.9412 35.7182 57.1266 36.1657 57.1266 36.7069C57.1266 37.2482 56.9412 37.6957 56.5725 38.0452C56.2038 38.3947 55.7478 38.5694 55.2044 38.5694Z" fill="#2A4EA2" />
                <path d="M26.1291 47.0634V62.3424C26.1291 62.7835 25.9351 63.1607 25.5473 63.4739C25.1595 63.7893 24.6928 63.9449 24.1515 63.9449C23.6103 63.9449 23.1415 63.7872 22.7536 63.4739C22.3658 63.1607 22.1719 62.7835 22.1719 62.3424V38.7165C22.1719 37.114 22.5405 35.6031 23.2778 34.1882C24.0152 32.7732 25.0231 31.5394 26.3038 30.4867C27.5845 29.434 29.089 28.5923 30.815 27.9637C32.5411 27.335 34.395 27.0197 36.3747 27.0197C38.1604 27.0197 39.8482 27.2647 41.44 27.7506C43.0318 28.2386 44.4468 28.9226 45.6891 29.8027C46.9315 30.6828 47.9778 31.712 48.8323 32.8904C49.6847 34.0689 50.2494 35.3517 50.52 36.7347C50.5967 37.2376 50.4411 37.6702 50.0533 38.0324C49.6655 38.3947 49.1604 38.5758 48.5403 38.5758C48.0353 38.5758 47.5985 38.4501 47.2298 38.1987C46.8611 37.9472 46.6374 37.6339 46.5607 37.2546C46.3668 36.2488 45.9683 35.3219 45.3673 34.4716C44.7643 33.6235 44.0078 32.884 43.0979 32.2554C42.1858 31.6268 41.148 31.1388 39.9845 30.7936C38.8189 30.4484 37.5979 30.2758 36.3172 30.2758C34.9193 30.2758 33.6002 30.5038 32.3578 30.9598C31.1155 31.4158 30.0287 32.021 29.0975 32.7754C28.1663 33.5297 27.4375 34.4183 26.9154 35.4391C26.3912 36.4619 26.1291 37.553 26.1291 38.7165V43.8563H39.2856C39.829 43.8563 40.2935 44.014 40.6835 44.3273C41.0713 44.6427 41.2653 45.0198 41.2653 45.4588C41.2653 45.8978 41.0713 46.2771 40.6835 46.5904C40.2957 46.9036 39.829 47.0613 39.2856 47.0613H26.1291V47.0634Z" fill="#2A4EA2" />
                <path d="M24.4043 20.1302V26.9067C24.4043 26.9067 30.9037 24.2004 36.6765 24.2004C42.4492 24.2004 48.9401 26.9067 48.9401 26.9067V20.0322C48.9401 20.0322 42.739 16.7484 36.5785 16.7484C30.4179 16.7484 24.4043 20.1302 24.4043 20.1302Z" fill="#2A4EA2" />
                <path d="M55.827 18.5129L58.2712 17.2002L55.827 15.8534V15.212C55.827 15.212 56.5153 11.7258 52.6114 13.5477C52.4132 13.6521 52.3067 13.7651 52.2619 13.8716L36.1604 4.9408L15 17.0532L22.9911 20.68V19.5038C22.9911 19.5038 29.6929 15.7405 36.5695 15.7405C43.4461 15.7405 50.3526 19.3993 50.3526 19.3993V21.4536L54.7658 19.0882V28.6775H53.2976V32.0871L55.2538 30.7403L57.4188 32.0871V28.6775H55.8334V18.5129H55.827ZM54.7658 14.6175V15.2589L53.4766 14.545C53.9454 14.2339 54.7658 13.8034 54.7658 14.6175ZM54.3289 18.3807C53.76 18.3807 53.2976 17.9183 53.2976 17.3515C53.2976 16.7847 53.76 16.3201 54.3289 16.3201C54.8979 16.3201 55.3582 16.7825 55.3582 17.3515C55.3582 17.9205 54.9043 18.3807 54.3289 18.3807Z" fill="#2A4EA2" />
            </g>
            <defs>
                <clipPath id="clip0_6753_41072">
                    <rect width="110" height="60.1186" fill="white" transform="translate(15 4.9408)" />
                </clipPath>
            </defs>
        </svg>
    )
}

export default Ficc