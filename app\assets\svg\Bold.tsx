import React from 'react';
import { xIconProps } from '@/types';

const Bold: React.FC<xIconProps> = ({ className }) => {
    return (
        <svg
            className={className}
            width="18"
            height="18"
            viewBox="0 0 18 18"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="mask0_2560_27549"
                style={{maskType:'alpha'}}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="18"
                height="18"
            >
                <rect width="18" height="18" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_2560_27549)">
                <path
                    d="M6.60156 14.25C6.18906 14.25 5.83594 14.1031 5.54219 13.8094C5.24844 13.5156 5.10156 13.1625 5.10156 12.75V5.25C5.10156 4.8375 5.24844 4.48438 5.54219 4.19063C5.83594 3.89688 6.18906 3.75 6.60156 3.75H9.24531C10.0578 3.75 10.8078 4 11.4953 4.5C12.1828 5 12.5266 5.69375 12.5266 6.58125C12.5266 7.21875 12.3828 7.70938 12.0953 8.05313C11.8078 8.39688 11.5391 8.64375 11.2891 8.79375C11.6016 8.93125 11.9484 9.1875 12.3297 9.5625C12.7109 9.9375 12.9016 10.5 12.9016 11.25C12.9016 12.3625 12.4953 13.1406 11.6828 13.5844C10.8703 14.0281 10.1078 14.25 9.39531 14.25H6.60156ZM7.37031 12.15H9.32031C9.92031 12.15 10.2859 11.9969 10.4172 11.6906C10.5484 11.3844 10.6141 11.1625 10.6141 11.025C10.6141 10.8875 10.5484 10.6656 10.4172 10.3594C10.2859 10.0531 9.90156 9.9 9.26406 9.9H7.37031V12.15ZM7.37031 7.875H9.11406C9.52656 7.875 9.82656 7.76875 10.0141 7.55625C10.2016 7.34375 10.2953 7.10625 10.2953 6.84375C10.2953 6.54375 10.1891 6.3 9.97656 6.1125C9.76406 5.925 9.48906 5.83125 9.15156 5.83125H7.37031V7.875Z"
                    fill="currentColor"
                />
            </g>
        </svg>
    );
};

export default Bold;
