import React from 'react';

const Applications = () => {
    return (
        <div>
            <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_2875_4387" maskUnits="userSpaceOnUse" x="0" y="0" width="24" height="25">
            <rect y="0.5" width="24" height="24" fill="#D9D9D9"/>
            </mask>
            <g mask="url(#mask0_2875_4387)">
            <path d="M14 21.5V19.85C14 19.7167 14.025 19.5875 14.075 19.4625C14.125 19.3375 14.2 19.225 14.3 19.125L19.525 13.925C19.675 13.775 19.8417 13.6667 20.025 13.6C20.2083 13.5333 20.3917 13.5 20.575 13.5C20.775 13.5 20.9667 13.5375 21.15 13.6125C21.3333 13.6875 21.5 13.8 21.65 13.95L22.575 14.875C22.7083 15.025 22.8125 15.1917 22.8875 15.375C22.9625 15.5583 23 15.7417 23 15.925C23 16.1083 22.9667 16.2958 22.9 16.4875C22.8333 16.6792 22.725 16.85 22.575 17L17.375 22.2C17.275 22.3 17.1625 22.375 17.0375 22.425C16.9125 22.475 16.7833 22.5 16.65 22.5H15C14.7167 22.5 14.4792 22.4042 14.2875 22.2125C14.0958 22.0208 14 21.7833 14 21.5ZM15.5 21H16.45L19.475 17.95L18.55 17.025L15.5 20.05V21ZM6 22.5C5.45 22.5 4.97917 22.3042 4.5875 21.9125C4.19583 21.5208 4 21.05 4 20.5V4.5C4 3.95 4.19583 3.47917 4.5875 3.0875C4.97917 2.69583 5.45 2.5 6 2.5H13.175C13.4417 2.5 13.6958 2.55 13.9375 2.65C14.1792 2.75 14.3917 2.89167 14.575 3.075L19.425 7.925C19.6083 8.10833 19.75 8.32083 19.85 8.5625C19.95 8.80417 20 9.05833 20 9.325V10.75C20 11.0333 19.9042 11.2708 19.7125 11.4625C19.5208 11.6542 19.2833 11.75 19 11.75C18.7167 11.75 18.4792 11.6542 18.2875 11.4625C18.0958 11.2708 18 11.0333 18 10.75V9.5H14C13.7167 9.5 13.4792 9.40417 13.2875 9.2125C13.0958 9.02083 13 8.78333 13 8.5V4.5H6V20.5H11C11.2833 20.5 11.5208 20.5958 11.7125 20.7875C11.9042 20.9792 12 21.2167 12 21.5C12 21.7833 11.9042 22.0208 11.7125 22.2125C11.5208 22.4042 11.2833 22.5 11 22.5H6ZM19.025 17.475L18.55 17.025L19.475 17.95L19.025 17.475Z" fill="currentColor"/>
            </g>
            </svg>
        </div>
    );
};

export default Applications;