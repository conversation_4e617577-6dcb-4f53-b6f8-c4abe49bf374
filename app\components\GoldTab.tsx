import React from 'react';
import GoldStar from '../assets/svg/GoldStar';
import ServiceGetInPackage from './ServiceGetInPackage';
import PersonPinCircle from '../assets/svg/PersonPinCircle';

const GoldTab = () => {
    return (
        <div className='grid grid-cols-2 gap-6 mt-10'>
            <div className='rounded-[20px] border border-[#1E62E033] p-[30px] drop-shadow-[0_1px_4px_rgba(0,0,0,0.05)]'>
                <div className='flex gap-6 justify-between items-center'>
                    <div>
                        <div className='flex gap-2.5 items-center mb-4'>
                            <PersonPinCircle />
                            <span className='font-medium text-lg leading-5 text-primaryColor'>Your current level is Gold!</span>
                        </div>

                        <h3 className='font-medium text-lg leading-8 text-graySix'>25-74 Unique Submitted Applicants in the last 12 Months</h3>
                        <p className='mt-4 font-normal text-base leading-6 text-grayFour'>Incredible performance! You rank among our top performing partners, and have nearly full feature access. Take the final leap to Platinum to earn full application processing prioritization.</p>
                    </div>
                    <div>
                        <GoldStar />
                    </div>
                </div>
                <div>
                    <ServiceGetInPackage 
                        serviceHeading='Service and Support'
                        serviceAvailable={[
                            'Account Management Coverage',
                            'Account Support Coverage (By Invitation)',
                            'Application Processing Prioritization - Priority'
                        ]}
                    />
                    <ServiceGetInPackage 
                        serviceHeading='Access'
                        serviceAvailable={[
                            'Access to Prime Institutions - Exclusive',
                            'Institutions with Limited Seats',
                            'Ireland Institutes'
                        ]}
                        serviceNotAvailable={[
                            'Institutions with Limited Seats',
                            'Institutions with Limited Seats',
                            'Institutions with Limited Seats'
                        ]}
                    />
                </div>
            </div>
            <div className='rounded-[20px] border border-[#1E62E033] p-[30px] drop-shadow-[0_1px_4px_rgba(0,0,0,0.05)]'>
                <div>
                    <ServiceGetInPackage 
                        serviceHeading='Service and Support'
                        serviceAvailable={[
                            'Account Management Coverage',
                            'Account Support Coverage (By Invitation)',
                            'Application Processing Prioritization - Priority'
                        ]}
                    />
                    <ServiceGetInPackage 
                        serviceHeading='Access'
                        serviceAvailable={[
                            'Access to Prime Institutions - Exclusive',
                            'Institutions with Limited Seats',
                            'Ireland Institutes'
                        ]}
                        serviceNotAvailable={[
                            'Institutions with Limited Seats',
                            'Institutions with Limited Seats',
                            'Institutions with Limited Seats'
                        ]}
                    />
                </div>
            </div>
        </div>
    )
}

export default GoldTab