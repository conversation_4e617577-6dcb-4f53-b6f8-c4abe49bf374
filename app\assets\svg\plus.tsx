import React from 'react';

interface PlusProps {
    className?: string
}


const Plus:React.FC<PlusProps> = ({className}) => {
    return (
        <>
            <svg
                className={className}
                width="19"
                height="18"
                viewBox="0 0 19 18"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
            >
                <mask
                    id="mask0_2560_26328"
                    style={{ maskType: 'alpha' }}
                    maskUnits="userSpaceOnUse"
                    x="0"
                    y="0"
                    width="19"
                    height="18"
                >
                    <rect x="0.25" width="18" height="18" fill="#D9D9D9" />
                </mask>
                <g mask="url(#mask0_2560_26328)">
                    <path
                        d="M8.49951 9.75H4.74951C4.53701 9.75 4.35889 9.67812 4.21514 9.53437C4.07139 9.39062 3.99951 9.2125 3.99951 9C3.99951 8.7875 4.07139 8.60937 4.21514 8.46562C4.35889 8.32187 4.53701 8.25 4.74951 8.25H8.49951V4.5C8.49951 4.2875 8.57139 4.10937 8.71514 3.96562C8.85889 3.82187 9.03701 3.75 9.24951 3.75C9.46201 3.75 9.64014 3.82187 9.78389 3.96562C9.92764 4.10937 9.99951 4.2875 9.99951 4.5V8.25H13.7495C13.962 8.25 14.1401 8.32187 14.2839 8.46562C14.4276 8.60937 14.4995 8.7875 14.4995 9C14.4995 9.2125 14.4276 9.39062 14.2839 9.53437C14.1401 9.67812 13.962 9.75 13.7495 9.75H9.99951V13.5C9.99951 13.7125 9.92764 13.8906 9.78389 14.0344C9.64014 14.1781 9.46201 14.25 9.24951 14.25C9.03701 14.25 8.85889 14.1781 8.71514 14.0344C8.57139 13.8906 8.49951 13.7125 8.49951 13.5V9.75Z"
                        fill="currentColor"
                    />
                </g>
            </svg>
        </>
    );
};

export default Plus;
