import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON> } from '../components/PieCharts';
import UpcomingEvent from '../components/UpcomingEvent';
import { Radial<PERSON><PERSON> } from '../components/RadialChart';
import StatCardLayout from '../components/StatCardLayout';
import Horizontal<PERSON>hart from '../components/HorizontalChart';
import { SingleBarChart } from '../components/SingleBarChart';
import MultipleBarChart from '../components/MultipleBarChart';
import UniversitiesOnMap from '../components/UniversitiesOnMap';
import StatInfoCardSmall from '../components/StatInfoCardSmall';
import VerticalTabLayout from '../components/VerticalTabLayout';
import HorizontalBarChart from '../components/HorizontalBarChart';
import DashboardLayout from '../components/layout/DashboardLayout';
import CounselorActivities from '../components/CounselorActivities';
import DatePickerWithRange from '../components/DatepickerWithRange';
import { AreaChartGradient } from '../components/AreaChartGradient';
import StatCardVisitorIcon from '@/app/assets/svg/stat-card-visitor-icon.svg';
import HarvardUniLogoSmall from '../assets/svg/HarvardUniLogoSmall.svg';
import StanfordUnilogoSmall from '@/app/assets/svg/StanfornUnilogoSmall.svg'
import Chartlogodemo from '@/app/assets/img/Chartlogodemo.png'
import { acceptanceRateChartData, acceptanceRejectionRateData, ageWiseApplicationChartData, applicationDestinationData, applicationOverviewChartData, ApplicationProcessingTimeData, applicationStatusData, ApplicationVisaSuccessData, DemographicsChartData, feedbackRatingsData, paidUnpaidApplicationData, ProgramPopularityData, regionalAnalysisData, studentApplicationVolumnData, superAdminGeneralMeetingsData, SuperAdminGeneralMeetingsHead, UniversityOnboardingData } from '@/common';
import { 
    SuperAdminStatCardData, 
    CounselorBarChartData, 
    TopFiveSalesChartData, 
    visaApplicationVolumnData, 
    ApplicationStatusPieChartData, 
    AgencyStatusPieChartData, 
    ApprovalRateData, 
    RevenueGenerationChartData, 
    taskCompletionData 
} from '@/common';
import application from '../assets/svg/application';
import { DonutChart } from '../components/DonutChart';
import HorizontalIconChart from '../components/HorizontalIconChart';
import Link from 'next/link';
import ArrowForward from '../assets/svg/arrow_forward';


const chartConfig = {
    applications: { label: 'Applications', color: '#184EB3' },
    success: { label: 'Success', color: '#1E62E0' },
};
const visaApplicationVolumnChartConfig = {
    applications: { label: 'Approved', color: '#1E62E0' },
    pending: { label: 'Pending', color: '#E7EFFF' },
    rejected: { label: 'Rejected', color: '#1952BB' },
};
const applicationDestinationChartConfig = {
    africa: { label: 'Africa', color: '#184EB3' },
    asia: { label: 'Asia', color: '#1B58CA' },
    europe: { label: 'Europe', color: '#1E62E0' },
    northAmerica: { label: 'North America', color: '#4B81E6' },
    southAmerica: { label: 'South America', color: '#78A1EC' },
    oceania: { label: 'Oceania', color: '#9DC7FF' }
};
const regionalAnalysisChartConfig = {
    applicationsSubmitted: { label: 'Applications Submitted', color: '#1E62E0' },
    acceptanceRate: { label: 'Acceptance Rate', color: '#9DC7FF' },
};
const paidUnpaidApplicationChartConfig = {
    paidApplication: { label: 'Paid Applications', color: '#1E62E0' },
    unpaidApplication: { label: 'Unpaid Applications', color: '#E7EFFF' },
};
const feedbackRatingsChartConfig = {
    communication: { label: 'Communication', color: '#1E62E0' },
    responsiveness: { label: 'Responsiveness', color: '#1952BB' },
    admissionSupport: { label: 'Admission Support', color: '#E7EFFF' },
};

const TopDestinationChartConfig = {
    agency: { label: 'Accepted', color: '#1E62E0', name: 'Agency' },
}
const RevenueGenerationChartConfig = {
    revenue: { label: 'Revenue', color: '#1E62E0', name: 'Agency' },
}
const applicationOverviewChartConfig = {
    application: { label: 'Applications', color: '#1E62E0', name: 'Applications' },
}
const acceptanceRateChartConfig = {
    percentage: { label: 'Rate', color: '#1E62E0', name: 'Percentage' },
}
const TopFiveSalesChartConfig = {
    sales: { label: 'Sales', color: '#4B81E6', name: 'Sales' },
}
const FavoriteProgramChartConfig = {
    percentage: { label: 'Percentage', color: '#1E62E0', name: 'Percentage' },
}

const applicationStatusChartConfig = {
    application: {
        label: 'Applications',
        color: '#184EB3',
    },
    accepted: {
        label: 'Accepted',
        color: '#1E62E0',
    },
    rejected: {
        label: 'Rejected',
        color: '#E7EFFF',
    },
};

const chartData = [
    { label: "FICC", value: 536 },
    { label: "BSB", value: 786 },
    { label: "Applyboard", value: 852 },
    { label: "BSB", value: 333 },
    { label: "Applyboard", value: 424},
    { label: "BSB", value: 124},
];

const lines = [
    { 
        dataKey: "This", 
        label: "This Year", 
        color: "#1E62E0", 
        strokeWidth: 5 
    },
    { 
        dataKey: "Previous", 
        label: "Previous Year", 
        color: "#E7EFFF", 
        strokeWidth: 5 
    }
];
const ApprovalRateChartConfig = [
    { 
        dataKey: "approval", 
        label: "Approval", 
        color: "#1E62E0", 
        strokeWidth: 5 
    },
    { 
        dataKey: "decline", 
        label: "Decline", 
        color: "#E7EFFF", 
        strokeWidth: 5 
    }
];
const ApplicationProcessingTimeChartConfig = [
    { 
        dataKey: "days", 
        label: "Days", 
        color: "#1E62E0", 
        strokeWidth: 5

    },
];
const acceptanceRejectionRateChartConfig = [
    { 
        dataKey: "accepted", 
        label: "Accepted", 
        color: "#1E62E0", 
        strokeWidth: 5
    },
    { 
        dataKey: "rejected", 
        label: "Rejected", 
        color: "#E7EFFF", 
        strokeWidth: 5
    },
];

const taskCompletionConfig = {
    completed: {
      label: 'Completed',
      color: '#1E62E0',
    },
    remaining: {
      label: 'Remaining',
      color: '#1952BB',
    },
  };
const studentApplicationVolumnConfig = {
    applications: {
      label: 'Applications',
      color: '#1E62E0',
    }
  };


  const customTeamLabels = {
    'team-a': {
      imageUrl: Chartlogodemo,
    },
    'team-b': {
      imageUrl: StanfordUnilogoSmall,
    },
    'team-c': {
      imageUrl: HarvardUniLogoSmall,
    }
  };

    const teamPerformanceData = [
        { category: 'team-a', application: 82, imageUrl: StanfordUnilogoSmall },
        { category: 'team-b', application: 67, imageUrl: Chartlogodemo },
        { category: 'team-c', application: 93, imageUrl: HarvardUniLogoSmall },
        { category: 'team-a', application: 82, imageUrl: StanfordUnilogoSmall },
        { category: 'team-b', application: 67, imageUrl: StanfordUnilogoSmall },
        { category: 'team-c', application: 93, imageUrl: HarvardUniLogoSmall },
    ];
    
    const teamPerformanceConfig = {
        application: {
            label: 'Preferred Universities',
            color: '#1E62E0',
        },
    };

const page = () => {
    const general = () => {
        return (
            <>
            <div className='flex flex-col gap-10 py-10'>
                <div className='grid md:grid-flow-col md:auto-cols-fr gap-6'>
                    {SuperAdminStatCardData.map((item, index) => (
                        <StatCardLayout key={index}>
                            <StatInfoCardSmall
                                imageSrc={item.icon}
                                description={item.label}
                                number={item.value}
                                decrease={item.decrease}
                                increase={item.increase}
                            />
                        </StatCardLayout>
                    ))}
                </div>
                <div className='grid md:grid-cols-2 grid-cols-1 gap-6'>
                    <StatCardLayout className='h-full'>
                        <SingleBarChart
                            title='Agency Onboarding'
                            chartConfig={TopDestinationChartConfig}
                            data={CounselorBarChartData}
                            textAnchor='middle'
                        />
                    </StatCardLayout>
                    <StatCardLayout className='h-full'>
                        <AreaChartGradient
                            title="University Onboarding"
                            data={UniversityOnboardingData}
                            lines={lines}
                            xAxisKey="month"
                            showLegends = {false}
                            className='-ml-5'
                            chartContainerClassName='max-h-[400px]'
                            xAxisHeight={60}
                        />
                    </StatCardLayout>
                </div>
                <div className='grid md:grid-cols-2 grid-cols-1 gap-6'>
                    <StatCardLayout className='h-full'>
                        {/* <HorizontalSingleBarChart /> */}
                        <HorizontalChart data={chartData} />
                    </StatCardLayout>
                    <StatCardLayout className='h-full'>
                        <MultipleBarChart 
                            title='Applications with VISA Success' 
                            data={ApplicationVisaSuccessData} 
                            chartConfig={chartConfig} 
                            deltaValue={0}
                        />
                    </StatCardLayout>
                </div>
                <div className='grid md:grid-cols-2 grid-cols-1 gap-6'>
                    <StatCardLayout className='h-full'>
                        <SingleBarChart
                            title='Top 5 Sales by Countries'
                            chartConfig={TopFiveSalesChartConfig}
                            data={TopFiveSalesChartData}
                            xAxisKey="country" 
                            textAnchor='middle'
                        />
                    </StatCardLayout>
                    <StatCardLayout className='h-full'>
                        <AreaChartGradient
                            title="Earnings Chart ($)"
                            data={UniversityOnboardingData}
                            lines={lines}
                            xAxisKey="month"
                            showLegends = {false}
                            className='-ml-5'
                            chartContainerClassName='max-h-[400px]'
                            xAxisHeight={60}
                        />
                    </StatCardLayout>
                </div>
                <div className='grid md:grid-cols-2 grid-cols-1 gap-6'>
                    <StatCardLayout className='h-full'>
                        <HorizontalBarChart
                            title="Task Completion Progress (%)"
                            data={taskCompletionData}
                            config={taskCompletionConfig}
                            height={400}
                            barRadius={200}
                            showLegend={true}
                            yAxisWeidth={90}
                        />
                    </StatCardLayout>
                </div>
                <div className='grid md:grid-cols-10 grid-cols-1 gap-6 h-full'>
                    {/* <StatCardLayout className='h-full md:col-span-6'>
                        <CounselorActivities />
                    </StatCardLayout> */}
                    <StatCardLayout className='h-full col-span-6 p-5'>
                        <div className='flex justify-between items-center'>
                            <span className='font-bold text-lg text-grayFive'>Meetings</span>
                            <Link           
                                href={'view-all'} 
                                className='flex gap-1 items-center font-medium text-[10px] text-secondaryColor rounded-[16px] py-2.5 px-4 bg-primaryOne'
                            >
                                View All
                                <ArrowForward className='w-3 h-3 text-secondaryColor' />
                            </Link>
                        </div>
                        <div className='overflow-x-auto border-[0.5px] border-grayOne rounded-[20px] mt-[22px]'>
                            <table className='bg-white min-w-full text-sm text-left'>
                                <thead>
                                    <tr>
                                        {SuperAdminGeneralMeetingsHead.map((thead, index) => (
                                            <th 
                                                key={index}
                                                className={`p-2.5 font-bold text-xs tracking-[0.4px] leading-5 text-grayFive ${
                                                    index === 4 ? 'text-center' : ''
                                                }`}
                                            >
                                                {thead}
                                            </th>
                                        ))}
                                    </tr>
                                </thead>
                                <tbody className='border-t border-grayOne divide-y divide-grayOne'>
                                    {superAdminGeneralMeetingsData.map((meeting, index) => (
                                        <tr key={index}>
                                            <td className='text-xs leading-5 text-graySix p-2.5 font-normal'>
                                                <div className='flex items-center gap-3.5'>
                                                    {<meeting.agency.logo />}
                                                    {meeting.agency.label}
                                                </div>
                                            </td>
                                            <td className='text-xs leading-5 text-graySix p-2.5 font-normal flex gap-2.5'>{meeting.keyAccountManager}</td>
                                            <td className='text-xs leading-5 text-graySix p-2.5 font-normal'>{meeting.meetingAgenda}</td>
                                            <td className='text-xs leading-5 text-graySix p-2.5 font-normal'>{meeting.dateAndTime}</td>
                                            <td className='text-xs leading-5 text-graySix p-2.5 font-normal'>
                                                <div className='flex justify-around'>
                                                    <Link
                                                        href={meeting.joinLink}
                                                        className='py-1 px-2.5 text-white bg-primaryColor rounded-full'
                                                    >
                                                        Join
                                                    </Link>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    </StatCardLayout>
                    <StatCardLayout className='h-full md:col-span-4'>
                        <UpcomingEvent />
                    </StatCardLayout>
                </div>
            </div>
            </>
        )
    };

    const leads = () => {
        return (
            <div className="py-5">
                leads
            </div>
        )
    };

    const agencies = () => {
        return (
            <div className="flex flex-col gap-10 py-10">
                <div className='grid md:grid-flow-col md:auto-cols-fr gap-6'>
                    {SuperAdminStatCardData.map((item, index) => (
                        <StatCardLayout key={index}>
                            <StatInfoCardSmall
                                imageSrc={item.icon}
                                description={item.label}
                                number={item.value}
                                decrease={item.decrease}
                                increase={item.increase}
                            />
                        </StatCardLayout>
                    ))}
                </div>
                <div className='grid md:grid-cols-7 grid-cols-1 gap-6'>
                    <StatCardLayout className='h-full col-span-4'>
                        <MultipleBarChart 
                            title='VISA Application Volume'
                            chartConfig={visaApplicationVolumnChartConfig}
                            data={visaApplicationVolumnData}
                            deltaValue={0}
                            chartHeightClass='max-h-[400px]'
                        />
                    </StatCardLayout>
                    <StatCardLayout className='h-full col-span-3'>
                        <PieCharts 
                            title="Agency Status" 
                            data={AgencyStatusPieChartData}
                            // customLabels={customLabels}
                            showLabelsOnPie={true}
                            showLegendLayout='bottom'
                        />
                    </StatCardLayout>
                </div>
                <div className='grid md:grid-cols-2 grid-cols-1 gap-6'>
                    <StatCardLayout className='h-full'>
                        <AreaChartGradient
                            title="Approval Rate"
                            data={ApprovalRateData}
                            lines={ApprovalRateChartConfig}
                            xAxisKey='month'
                            showLegends = {true}
                            className='-ml-4'
                            chartContainerClassName='max-h-[400px]'
                        />
                    </StatCardLayout>
                    <StatCardLayout className='h-full'>
                        <SingleBarChart
                            title='Revenue Generation'
                            chartConfig={RevenueGenerationChartConfig}
                            data={RevenueGenerationChartData}
                            showLegend={false}
                            xAxisKey='agency'
                            textAnchor='middle'
                        />
                    </StatCardLayout>
                </div>
                <div className='grid md:grid-cols-2 grid-cols-1 gap-6'>
                    <StatCardLayout className='h-full'>
                        Underperforming Aspects
                    </StatCardLayout>
                    
                </div>
            </div>
        )
    };

    const universities = () => {
        return (
            <div className="flex flex-col gap-10 py-10">
                <div className='grid md:grid-flow-col md:auto-cols-fr gap-6'>
                    {SuperAdminStatCardData.map((item, index) => (
                        <StatCardLayout key={index}>
                            <StatInfoCardSmall
                                imageSrc={item.icon}
                                description={item.label}
                                number={item.value}
                                decrease={item.decrease}
                                increase={item.increase}
                            />
                        </StatCardLayout>
                    ))}
                </div>
                <div className='grid md:grid-cols-2 grid-cols-1 gap-6'>
                    <StatCardLayout className='h-full'>
                        <HorizontalIconChart
                            title='Most Preferred Universities'
                            viewAllLink='/view-all'
                            items={[
                                { img: HarvardUniLogoSmall, value: 54, label: 'University of Hervard' },
                                { img: HarvardUniLogoSmall, value: 43, label: 'University of California' },
                                { img: HarvardUniLogoSmall, value: 67, label: 'University of Michigan' },
                                { img: HarvardUniLogoSmall, value: 78, label: 'Massachusetts Institute of Technology (MIT)' },
                                { img: HarvardUniLogoSmall, value: 36, label: 'Cornell University' },
                                { img: HarvardUniLogoSmall, value: 36, label: 'University of Michigan'},
                            ]}
                        />
                    </StatCardLayout>
                    <StatCardLayout className='h-full'>
                        <AreaChartGradient
                            title="Application Processing Time (days)"
                            data={ApplicationProcessingTimeData}
                            lines={ApplicationProcessingTimeChartConfig}
                            xAxisKey="university"
                            showLegends = {false}
                            className='-ml-4'
                            chartContainerClassName='max-h-[400px]'
                            xAxisHeight={150}
                        />
                    </StatCardLayout>
                </div>
                <div className='grid md:grid-cols-7 grid-cols-1 gap-6'>
                    <StatCardLayout className='h-full col-span-4'>
                        <MultipleBarChart 
                            title='Feedback Ratings'
                            chartConfig={feedbackRatingsChartConfig}
                            data={feedbackRatingsData}
                            deltaValue={0}
                            chartHeightClass='max-h-[400px]'
                            xAxisAngle={-40}
                            xAxisHeight={150}
                            textAnchor='end'
                        />
                    </StatCardLayout>
                    <StatCardLayout className='h-full col-span-3'>
                        <SingleBarChart
                            title='Acceptance Rate'
                            chartConfig={acceptanceRateChartConfig}
                            data={acceptanceRateChartData}
                            showLegend={false}
                            xAxisKey='university'
                            xAxisLabelAngle={-30}
                            xAxisHeight={110}
                        />
                    </StatCardLayout>
                </div>
                <div>
                    <UniversitiesOnMap />
                </div>
            </div>
        )
    };

    const students = () => {
        return (
            <div className="flex flex-col gap-10 py-10">
                <div className='grid md:grid-flow-col md:auto-cols-fr gap-6'>
                    {SuperAdminStatCardData.map((item, index) => (
                        <StatCardLayout key={index}>
                            <StatInfoCardSmall
                                imageSrc={item.icon}
                                description={item.label}
                                number={item.value}
                                decrease={item.decrease}
                                increase={item.increase}
                            />
                        </StatCardLayout>
                    ))}
                </div>
                <div className='grid md:grid-cols-7 grid-cols-1 gap-6'>
                    <StatCardLayout className='h-full col-span-4'>
                        <SingleBarChart
                            title='Favorite Programs'
                            chartConfig={FavoriteProgramChartConfig}
                            data={ProgramPopularityData}
                            chartHeightClass='max-h-[370px]'
                            xAxisLabelAngle={-90}
                            xAxisHeight={190}
                            xAxisTickMargin={10}
                        />
                    </StatCardLayout>
                    <StatCardLayout className='h-full col-span-3'>
                        <PieCharts 
                            title="Demographics" 
                            data={DemographicsChartData}
                            labelKey="country"
                            showLabelsOnPie={true}
                            showLegendLayout='right'
                        />
                    </StatCardLayout>
                </div>
                <div className='grid md:grid-cols-2 grid-cols-1 gap-6'>
                    <StatCardLayout className='h-full'>
                        <AreaChartGradient
                            title="Acceptance Rejection Rate"
                            data={acceptanceRejectionRateData}
                            lines={acceptanceRejectionRateChartConfig}
                            xAxisKey="university"
                            showLegends = {false}
                            className='-ml-4'
                            chartContainerClassName='max-h-[400px]'
                            xAxisHeight={150}
                        />
                    </StatCardLayout>
                    <StatCardLayout className='h-full'>
                        <HorizontalBarChart
                            title="Students Application Volume"
                            data={studentApplicationVolumnData}
                            config={studentApplicationVolumnConfig}
                            height={400}
                            barRadius={200}
                            showLegend={false}
                            showBarLabels
                            yAxisWeidth={220}
                        />
                    </StatCardLayout>
                </div>
                <div>
                    <StatCardLayout className='h-full'>
                        <MultipleBarChart 
                            title='Applications by Destination and Present Region'
                            chartConfig={applicationDestinationChartConfig}
                            data={applicationDestinationData}
                            deltaValue={0}
                            chartHeightClass='max-h-[575px]'
                            barSize={20}
                        />
                    </StatCardLayout>
                    
                </div>
                <div className='grid md:grid-cols-7 grid-cols-1 gap-6'>
                    <StatCardLayout className='h-full col-span-3' >
                        <PieCharts 
                            title="Age-wise Application Distribution" 
                            data={ageWiseApplicationChartData}
                            labelKey="ageGroup"
                            showLabelsOnPie={true}
                            showLegendLayout='bottom'
                        />
                    </StatCardLayout>
                    
                </div>
            </div>
        )
    };

    const applications = () => {
        return (
            <div className="flex flex-col gap-10 py-10">
                <div className='grid md:grid-flow-col md:auto-cols-fr gap-6'>
                    {SuperAdminStatCardData.map((item, index) => (
                        <StatCardLayout key={index}>
                            <StatInfoCardSmall
                                imageSrc={item.icon}
                                description={item.label}
                                number={item.value}
                                decrease={item.decrease}
                                increase={item.increase}
                            />
                        </StatCardLayout>
                    ))}
                </div>
                <div className='grid md:grid-cols-7 grid-cols-1 gap-6'>
                    <StatCardLayout className='h-full col-span-4'>
                        <SingleBarChart
                            title='Applications Overview'
                            chartConfig={applicationOverviewChartConfig}
                            data={applicationOverviewChartData}
                            showLegend={false}
                            xAxisKey='month'
                            textAnchor='middle'
                            xAxisLabelAngle={-30}
                        />
                    </StatCardLayout>
                    <StatCardLayout className='h-full col-span-3'>
                        <DonutChart 
                            chartConfig={applicationStatusChartConfig}
                            data={applicationStatusData} // Pass the array directly, not the object
                            title="Application Status"
                            legendPosition='bottom'
                            chartClassName='max-h-[350px]'
                            innerCircleRedius={80}
                        />
                    </StatCardLayout>
                </div>
                <div className='grid md:grid-cols-2 grid-cols-1 gap-6'>
                    <StatCardLayout className='h-full'>
                        <RadialChart

                        />
                    </StatCardLayout>
                    <StatCardLayout className='h-full'>
                        <MultipleBarChart 
                            title='Regional Analysis'
                            chartConfig={regionalAnalysisChartConfig}
                            data={regionalAnalysisData}
                            deltaValue={0}
                            chartHeightClass='max-h-[360px]'
                            barSize={25}
                        />
                    </StatCardLayout>
                </div>
                <div className='grid md:grid-cols-7 grid-cols-1 gap-6'>
                    <StatCardLayout className='h-full col-span-4'>
                        <MultipleBarChart 
                            title='Paid and Unpaid Applications'
                            chartConfig={paidUnpaidApplicationChartConfig}
                            data={paidUnpaidApplicationData}
                            deltaValue={0}
                            chartHeightClass='max-h-[500px]'
                            barSize={15}
                            xAxisAngle= {-90}
                            xAxisHeight={180}
                            textAnchor='end'
                        />
                    </StatCardLayout>
                </div>
            </div>
        )
    };

    const accounts = () => {
        return (
             <div className="py-5">
                accounts
            </div>
        )
    };

    const reports = () => {
        return (
             <div className="py-5">
                reports
            </div>
        )
    };

    const tabContentsSuperAdmin = [
        {
            value: 'general',
            label: 'General',
            content: general
        },
        {
            value: 'leads',
            label: 'Leads',
            content: leads
        },
        {
            value: 'agencies',
            label: 'Agencies',
            content: agencies
        },
        {
            value: 'universities',
            label: 'Universities',
            content: universities
        },
        {
            value: 'students',
            label: 'Students',
            content: students
        },
        {
            value: 'applications',
            label: 'Applications',
            content: applications
        },
        {
            value: 'accounts',
            label: 'Accounts',
            content: accounts
        },
        {
            value: 'reports',
            label: 'Reports',
            content: reports
        }
    ];

    const tabContents = tabContentsSuperAdmin.map((tab) => ({
        value: tab.value,
        children: <tab.content />,
    }));

    return (
        <DashboardLayout>
            <div className="flex flex-col gap-10 py-5">
                <div className="sticky top-0 z-20 flex justify-between items-center bg-primaryOne py-4">
                    <h1 className="text-[28px] leading-[34px] font-bold text-graySix">
                        Welcome Michael!
                    </h1>
                    <DatePickerWithRange />
                </div>
                <div>
                    <VerticalTabLayout 
                        tabMenu={tabContentsSuperAdmin}
                        tabContents={tabContents}
                        defaultValue='general'
                        tabListClassName='!p-0 max-w-[1193px] md:!justify-start sticky top-[70px] z-10'
                        tabContentClassName='pb-[96px]'
                        tabTriggerClassName=''
                    />
                </div>
            </div>
        </DashboardLayout>
    );
};

export default page;
