import React from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';
import { NavItemProps } from '@/types';
import { usePathname } from 'next/navigation';

const NavItem:React.FC<NavItemProps> = ({
    icon,
    href,
    label,
    isExpanded,
    toggleSidebar
}) => {
    const pathname = usePathname();
    const isActive = pathname === href || (href !== '/' && pathname.startsWith(href));
    
    return (
        <Link
            href={href}
            className={cn(
            'flex items-center py-2.5 rounded-lg px-3',
            isActive ? 'bg-primaryColor text-white' : 'hover:bg-primaryOne text-grayFive'
            )}
        >
            <div className='flex'>
                {typeof icon === 'function' ? 
                    (<span className='flex-shrink-0'>
                        {React.createElement(icon)}
                    </span>)
                        :
                    (<span>{icon}</span>)
                }
                {(isExpanded || toggleSidebar) && (
                    <span className= 'ml-4 font-medium text-base whitespace-nowrap'>
                        {label}
                    </span>
                )}
            </div>
        </Link>
    )
}

export default NavItem

  