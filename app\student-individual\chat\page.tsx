'use client'

import { chatDept } from '@/common';
import React, { useState } from 'react';
import Search from '@/app/assets/svg/search';
import ChatSection from '@/app/components/ChatSection';
import { Separator } from '@/components/ui/separator';
import InputFieldWithIcon from '@/app/components/InputFieldWithIcon';
import DashboardLayout from '@/app/components/layout/DashboardLayout';

const Page = () => {
    const [chat, setChat] = useState(false);
    const [selectedDept, setSelectedDept] = useState<string>(chatDept[0]?.dept || '');

    const showChat = (dept: string) => {
        setChat(true)
        setSelectedDept(dept); 
    };

    return (
        <DashboardLayout>
             <div className='flex flex-col md:flex-row gap-4 mt-12'>
                <aside className={`${chat ? '': ''} w-full md:w-1/4 rounded-[20px] bg-white`}>
                    <div className='flex items-center gap-2.5 p-6'>
                        <h2 className='font-semibold text-lg text-graySeven'>Messages</h2>
                        <span className='text-xs font-semibold rounded-[22px] bg-primaryFour px-2 py-0.5 text-primaryColor'>
                            12
                        </span>
                    </div>
                    <Separator />
                    <div className='mt-4 mx-4'>
                        <InputFieldWithIcon 
                            placeholder='Search'
                            icon={<Search />}
                            className='w-full border-none rounded-xl py-2.5 px-4 bg-[#EFF0F0] bg-opacity-30'
                            inputClass={'placeholder:bg-[#EFF0F0] h-full placeholder:bg-opacity-30 focus:bg-[#EFF0F0] focus:bg-opacity-30'}
                        />
                    </div>
                    <div className=''>
                        <h3 className='py-3 text-sm font-medium text-primaryColor border-b-2 border-primaryColor inline-block ml-6'>Dept</h3>
                        <Separator />
                        <ul className='space-y-3 px-4 mt-3'>
                            {chatDept.map((chat, index) => (
                                <li 
                                    key={index} 
                                    className={`group cursor-pointer py-2 px-2 hover:bg-primaryOne hover:rounded-xl flex justify-between ${
                                    selectedDept === chat.dept ? 'bg-primaryOne rounded-xl' : ''
                                    }`}
                                    onClick={() => showChat(chat.dept)}
                                >
                                    <div className='flex gap-3'>
                                        <div 
                                            className={`flex items-center justify-center w-11 h-11 rounded-[22px] 
                                            ${selectedDept === chat.dept ? 'bg-primaryTwo' : 'bg-primaryOne'} 
                                            group-hover:bg-primaryTwo`}
                                        >
                                            {<chat.icon />}
                                        </div>
                                        <div className='flex flex-col gap-0.5'>
                                            <span className={`${chat.unreadMsg ? 'font-bold' : 'font-semibold'} text-sm text-graySix`}>
                                                {chat.dept}
                                            </span>
                                            <span className={`${chat.unreadMsg ? 'font-semibold' : 'font-normal'} text-xs leading-5 text-black text-opacity-40`}>
                                                {chat.message}
                                            </span>
                                        </div>
                                    </div>
                                    <div className='flex flex-col gap-0.5'>
                                        <span className='font-normal text-[13px] leading-5 text-grayTwo'>12m</span>
                                        {chat.unreadMsgCount && (
                                            <div className='flex items-center justify-center rounded-[10px] bg-primaryColor w-4 h-4'>
                                                <span className='font-normal text-[10px] leading-3 text-white'>{chat.unreadMsgCount}</span>
                                            </div>
                                        )}
                                    </div>
                                </li>
                            ))}
                        </ul>
                    </div>
                </aside>
                <div className={`w-full md:w-3/4`}>
                    <ChatSection 
                        setChat={setChat} 
                        selectedDept={selectedDept}
                    />
                </div>
            </div>
        </DashboardLayout>
    )
}

export default Page