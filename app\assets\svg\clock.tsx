import React from 'react'

const clock = () => {
    return (
        <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_609_1808" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="14" height="15">
            <rect y="0.5" width="14" height="14" fill="#D9D9D9"/>
            </mask>
            <g mask="url(#mask0_609_1808)">
            <path d="M7.58307 7.85002V5.75002C7.58307 5.58475 7.52717 5.44621 7.41536 5.3344C7.30356 5.22259 7.16502 5.16669 6.99974 5.16669C6.83446 5.16669 6.69592 5.22259 6.58411 5.3344C6.47231 5.44621 6.41641 5.58475 6.41641 5.75002V8.06877C6.41641 8.14655 6.43099 8.2219 6.46016 8.29482C6.48932 8.36773 6.53307 8.43336 6.59141 8.49169L8.22474 10.125C8.33168 10.232 8.4678 10.2854 8.63307 10.2854C8.79835 10.2854 8.93446 10.232 9.04141 10.125C9.14835 10.0181 9.20182 9.88197 9.20182 9.71669C9.20182 9.55141 9.14835 9.4153 9.04141 9.30836L7.58307 7.85002ZM6.99974 13.3334C6.27057 13.3334 5.58759 13.1948 4.95078 12.9177C4.31398 12.6406 3.75981 12.2663 3.28828 11.7948C2.81675 11.3233 2.44245 10.7691 2.16536 10.1323C1.88828 9.49551 1.74974 8.81252 1.74974 8.08336C1.74974 7.35419 1.88828 6.67121 2.16536 6.0344C2.44245 5.39759 2.81675 4.84343 3.28828 4.3719C3.75981 3.90037 4.31398 3.52607 4.95078 3.24898C5.58759 2.9719 6.27057 2.83336 6.99974 2.83336C7.72891 2.83336 8.41189 2.9719 9.0487 3.24898C9.6855 3.52607 10.2397 3.90037 10.7112 4.3719C11.1827 4.84343 11.557 5.39759 11.8341 6.0344C12.1112 6.67121 12.2497 7.35419 12.2497 8.08336C12.2497 8.81252 12.1112 9.49551 11.8341 10.1323C11.557 10.7691 11.1827 11.3233 10.7112 11.7948C10.2397 12.2663 9.6855 12.6406 9.0487 12.9177C8.41189 13.1948 7.72891 13.3334 6.99974 13.3334ZM1.19557 4.75836C1.08863 4.65141 1.03516 4.5153 1.03516 4.35002C1.03516 4.18475 1.08863 4.04864 1.19557 3.94169L2.85807 2.27919C2.96502 2.17225 3.10113 2.11877 3.26641 2.11877C3.43168 2.11877 3.5678 2.17225 3.67474 2.27919C3.78168 2.38614 3.83516 2.52225 3.83516 2.68752C3.83516 2.8528 3.78168 2.98891 3.67474 3.09586L2.01224 4.75836C1.9053 4.8653 1.76918 4.91877 1.60391 4.91877C1.43863 4.91877 1.30252 4.8653 1.19557 4.75836ZM12.8039 4.75836C12.697 4.8653 12.5609 4.91877 12.3956 4.91877C12.2303 4.91877 12.0942 4.8653 11.9872 4.75836L10.3247 3.09586C10.2178 2.98891 10.1643 2.8528 10.1643 2.68752C10.1643 2.52225 10.2178 2.38614 10.3247 2.27919C10.4317 2.17225 10.5678 2.11877 10.7331 2.11877C10.8984 2.11877 11.0345 2.17225 11.1414 2.27919L12.8039 3.94169C12.9109 4.04864 12.9643 4.18475 12.9643 4.35002C12.9643 4.5153 12.9109 4.65141 12.8039 4.75836ZM6.99974 12.1667C8.13724 12.1667 9.10217 11.7705 9.89453 10.9781C10.6869 10.1858 11.0831 9.22086 11.0831 8.08336C11.0831 6.94586 10.6869 5.98093 9.89453 5.18857C9.10217 4.3962 8.13724 4.00002 6.99974 4.00002C5.86224 4.00002 4.89731 4.3962 4.10495 5.18857C3.31259 5.98093 2.91641 6.94586 2.91641 8.08336C2.91641 9.22086 3.31259 10.1858 4.10495 10.9781C4.89731 11.7705 5.86224 12.1667 6.99974 12.1667Z" fill="#664D03"/>
            </g>
        </svg>
    )
}

export default clock