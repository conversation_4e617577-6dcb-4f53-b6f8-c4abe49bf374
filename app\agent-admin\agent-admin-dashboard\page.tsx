'use client'

import React from 'react';
import Faq from '@/app/components/Faq';
import Vodafone from '@/app/assets/svg/Vodafone';
import GoldStar from '@/app/assets/svg/GoldStar';
import { BadgeStatusData, FaqData } from '@/common';
import ProgressBar from '@/app/components/ProgressBar';
import BadgeStatus from '@/app/components/BadgeStatus';
import PartnerBenefitsTab from '@/app/components/PartnerBenefitsTab';
import DashboardLayout from '@/app/components/layout/DashboardLayout';

const page = () => {
    return (
        <DashboardLayout>
            <div className='flex gap-6 mt-10'>
                <div className='flex justify-between w-[70%] py-[84px] px-[50px] drop-shadow-[0_1px_4px_rgba(0,0,0,0.05)] rounded-[20px] bg-gradient-to-r from-[#DDE9FF] to-[#FAFBFF]'>
                    <div>
                        <span className='mb-1 font-normal text-[22px] leading-6 tracking-[4%] text-primaryColor'>Preferred Partners</span>
                        <p className='font-normal italic text-xs leading-4 text-primaryColor'>Brought to you by ApplyGoal</p>
                        <div className='mt-10'>
                            <h1 className='mb-1 font-bold text-[28px] leading-8 text-graySix'>Hi, FICC</h1>
                            <p className='font-normal text-sm leading-4 text-graySix'>Incredible performance! You rank among our top performing partners.</p>
                        </div>
                    </div>
                    <div className='w-[30%] flex justify-end'>
                        <div className='flex justify-center items-center w-[150px] h-[150px] bg-white rounded-full drop-shadow-[0_4px_10px_rgba(0,0,0,0.08)]'>
                            <Vodafone />
                        </div>
                    </div>
                </div>
                <div className='bg-white w-[30%] rounded-[20px] py-5 px-6 drop-shadow-[0_1px_4px_rgba(0,0,0,0.05)]'>
                    <div className='flex gap-5'>
                        <div className='w-[80%]'>
                            <h3 className='font-semibold text-lg leading-5 text-graySix'>Your Progress So Far</h3>
                            <p className='mt-5 font-normal text-base leading-6 text-grayFour'>You're just 15 unique submitted applicants away from keeping the benefits of Gold status, come January 1st 2025. If you do not meet the target by 2025 you will be downgraded to Silver status.</p>
                        </div>
                        <div className='w-[20%]'>
                            <GoldStar />
                        </div>
                    </div>
                    <div className='mt-5'>
                        <div className='flex justify-between mb-3'>
                            <h3 className='font-medium text-base leading-5 text-graySix'>Unique Submitted Applicants</h3>
                            <span className='font-medium text-base leading-5 text-grayFive'>10</span>
                        </div>
                        <ProgressBar progressValue={20} />
                    </div>
                </div>
            </div>
            <div className='bg-white mt-10 px-10 rounded-[20px] drop-shadow-[0_2px_2px_rgba(0,0,0,0.05)]'>
                <PartnerBenefitsTab />
            </div>
            <div className='mt-10 bg-white rounded-[20px] drop-shadow-[0_1px_4px_rgba(0,0,0,0.05)] p-10'>
                <div className='flex justify-center pt-[60px] pb-10'>
                    <h2 className='font-semibold text-[28px] leading-8 text-graySix'>Here's Your Roadmap To Platinum Status</h2>
                </div>
                <div>
                    <BadgeStatus StatusProps={BadgeStatusData} />
                </div>
            </div>
            <div className='mt-10 mb-20 bg-white rounded-[20px] drop-shadow-[0_1px_4px_rgba(0,0,0,0.05)] p-10'>
                <div className='flex justify-center pt-[60px] pb-10'>
                    <h2 className='font-semibold text-[28px] leading-8 text-graySix'>FAQ</h2>
                </div>
                <Faq 
                    faqData={FaqData}
                />
            </div>
        </DashboardLayout>
    )
}

export default page