import { object, string } from 'zod'
import { isValidPhoneNumber } from 'react-phone-number-input';

export const signUpFormSchema = object({
        name: string().min(5, {
            message: 'name must be at least 5 characters.'})
            .regex(/^[A-Za-z\s]+$/, {
            message: "Only letters are allowed (no numbers or symbols).",
            }),
        nationality: string({required_error: 'Nationality is required'})
            .min(2, {
            message: 'Nationality must be at least 2 characters.'})
            .refine((val) => val && val.trim() !== '', {
                message: 'Nationality is required',
            }),
        email: string({ required_error: 'Email is required' })
            .min(1, 'Email is required')
            .email('Invalid email')
            .refine((val) => !/^[0-9]/.test(val), {
                message: 'Email cannot start with a number.',
            }),
        phone: string({required_error: 'Phone number required'})
            .min(1, 'Phone number is required')
            .refine((val) => {
                if (!val) return true;
                return isValidPhoneNumber(val);
            }, {
                message: 'Please enter a valid phone number',
            }),
        password: string({ required_error: "Password is required" })
            .min(1, "Password is required")
            .min(8, "Password must be more than 8 characters")
            .max(32, "Password must be less than 32 characters"),
            confirmPassword: string(),
    })
    .refine((data) => data.password === data.confirmPassword, {
        message: 'Passwords must match.',
        path: ['confirmPassword'],
    });

export const signUpRecruitmentPartnerFormSchema = object({
        name: string().min(5, {
            message: 'name must be at least 5 characters.'})
            .regex(/^[A-Za-z\s]+$/, {
            message: "Only letters are allowed (no numbers or symbols).",
            }),
        email: string({ required_error: 'Email is required' })
            .min(1, 'Email is required')
            .email('Invalid email')
            .refine((val) => !/^[0-9]/.test(val), {
                message: 'Email cannot start with a number.',
            }),
        organizationName: string().min(5, {
            message: 'name must be at least 5 characters.'})
            .regex(/^[A-Za-z\s]+$/, {
            message: "Only letters are allowed (no numbers or symbols).",
            }),
        password: string({ required_error: "Password is required" })
            .min(1, "Password is required")
            .min(8, "Password must be more than 8 characters")
            .max(32, "Password must be less than 32 characters"),
            confirmPassword: string(),
    })
    .refine((data) => data.password === data.confirmPassword, {
        message: 'Passwords must match.',
        path: ['confirmPassword'],
    });

export const signInSchema = object({
    email: string({ required_error: 'Email is required'})
        .min(1, 'Email is required')
        .email('Invalid email'),
    password: string({ required_error: 'Password is required' })
        .min(1, 'Password is required')
        .min(8, 'Password must be more than 8 characters')
        .max(32, 'Password must be less than 32 characters'),
})