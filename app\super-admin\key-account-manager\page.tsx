'use client'

import React from 'react';
import Trash from '@/app/assets/svg/trash';
import Canada from '@/app/assets/svg/canada';
import Heading from '@/app/components/Heading';
import { UniTypeSelectButton } from '@/common';
import EditAction from '@/app/assets/svg/EditAction';
import InputField from '@/app/components/InputField';
import SelectField from '@/app/components/SelectField';
import DropDownButton from '@/app/components/DropDownButton';
import DashboardLayout from '@/app/components/layout/DashboardLayout';

const page = () => {
    const tableHeadData = [
        'KID',
        'Name',
        'Email',
        'Mobile',
        'Assigned Countries',
        'Assigned Agencies',
        'Total Applications',
        'Students Enrolled',
        'Transfer Requests',
        'Performance Score',
        'Joining On',
        'Last Activity',
        'Status',
        'Action'
    ];

    const statusColor = (value:string) => {
        if(value === 'On Leave') {
            return 'bg-[#F7E1C140] text-[#F2A735]'
        }else if(value === 'Inactive') {
            return 'bg-[#FF3B300F] text-[#FF3B30]'
        }else {
            return 'bg-[#ECFDF3] text-[#027A48]'
        }
    }

    const keyManagerLists= [
        {
            kid: 262,
            name: 'Nolan Westervelt',
            email: '<EMAIL>',
            mobile: '+8801962-446543',
            assignedCountries: [Canada, Canada, Canada],
            assignedAgencies: '12',
            totalApplications: '315',
            studentsEnrolled: '180',
            transferRequests: '6',
            performanceScore: '92%',
            joiningOn: '22 Oct 2023',
            lastActivity: '22 Oct 2023',
            status: 'On Leave',
            actions: ''
        }
    ];
    return (
        <DashboardLayout>
            <Heading level='h1'>
                Key Account Manager
            </Heading>
            <div className='grid grid-cols-4 gap-3 py-5'>
                <InputField 
                    id='first_name'
                    placeholder='345689' 
                    type='text' 
                    // register={register('first_name', { required: 'First name is required' })}
                    label='UID' 
                    className='border-grayOne border-opacity-none'
                />
                <SelectField
                    label='Type'
                    options={UniTypeSelectButton}
                />
                 <SelectField
                    label='Country'
                    options={UniTypeSelectButton}
                />
                 <SelectField
                    label='Sort By'
                    options={UniTypeSelectButton}
                />
            </div>
            <div className='flex justify-between'>
                <Heading level='h2'>
                    All(32)
                </Heading>
                <DropDownButton />
            </div>
            <div className='overflow-x-auto'>
                <table className='bg-white min-w-full rounded-[20px] text-sm text-left'>
                    <thead>
                        <tr>
                            <th className='p-4'>
                                <input 
                                    type='checkbox' 
                                    className='h-3 w-3 border-grayFour rounded-[4px] border-[0.67px]' 
                                />
                            </th>
                            {tableHeadData.map((thData, index) => (
                                <th 
                                    key={index} 
                                    className='p-4 font-bold text-xs tracking-[0.4px] text-grayFive'
                                >
                                    {thData}
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody className='border-t border-grayOne divide-y divide-grayOne'>
                        {keyManagerLists.map((list, index) => (
                            <tr key={index}>
                                <td className='text-xs leading-5 text-graySix p-4 font-normal'>
                                    <input 
                                        type='checkbox' 
                                        className='h-3 w-3 border-grayFour rounded-[4px] border-[0.67px]' 
                                    />
                                </td>
                                <td className='w-[80px] text-xs leading-5 text-graySix p-4 font-normal'>{list.kid}</td>
                                <td className='w-[180px] text-xs leading-5 text-graySix px-6 font-normal'>{list.name}</td>
                                <td className='w-[180px] text-xs leading-5 text-graySix p-4 font-normal'>{list.email}</td>
                                <td className='w-[170px] text-xs leading-5 text-graySix p-4 font-normal'>{list.mobile}</td>
                                <td className='flex w-[112px] text-xs leading-5 text-graySix p-4 font-normal'>
                                    {list.assignedCountries.map((country, index) => (
                                        <div  key={`country-${index}`}>
                                            <span className='-ml-2' >{country()}</span>
                                        </div>
                                    ))}
                                </td>
                                <td className='w-[110px] text-xs leading-5 text-graySix p-4'>{list.assignedAgencies}</td>
                                <td className='w-[130px] text-xs leading-5 text-graySix p-4'>{list.totalApplications}</td>
                                <td className='w-[110px] text-xs leading-5 text-graySix p-4'>{list.studentsEnrolled}</td>
                                <td className='w-[110px] text-xs leading-5 text-graySix p-4'>{list.transferRequests}</td>
                                <td className='w-[114px] text-xs leading-5 text-graySix p-4'>{list.performanceScore}</td>
                                <td className='w-[110px] text-xs leading-5 text-graySix p-4'>{list.joiningOn}</td>
                                <td className='w-[110px] text-xs leading-5 text-graySix p-4'>{list.lastActivity}</td>
                                <td className={`w-[110px]`}>
                                    <span className={`rounded-[16px] font-medium text-xs ${statusColor(list.status)} leading-4 py-[2px] px-2`}>{list.status}</span>
                                </td>
                                <td className='w-[90px] text-xs leading-5 text-graySix p-4 flex gap-2.5 items-center'>
                                    <EditAction />
                                    <Trash />
                                </td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </DashboardLayout>
    )
}

export default page