// 'use client';

// import React, { useState } from 'react';
// import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query';

// interface Post  {
//     title: string;
//     body: string;
//     id: number;
//     web_pages: string[];
// }

// const fetchPosts = async (): Promise<Post []> => {
//     const res = await fetch('https://jsonplaceholder.typicode.com/posts');

//     if (!res.ok) throw new Error('Failed to fetch');
//     return res.json();
// };

// const deletePost = async (id: number) => {
//     const res = await fetch(`https://jsonplaceholder.typicode.com/posts/${id}`, {
//         method: 'DELETE',
//     });
//     if (!res.ok) throw new Error('Failed to delete post');
//     return true;
// };

// const UniversityListAndPagination: React.FC = () => {
//     const [page, setPage] = useState(1);
//     const itemsPerPage = 10;
//     const queryClient = useQueryClient();

//     const { data: posts = [], isLoading, isError} = useQuery({
//         queryKey: ['posts'],
//         queryFn: fetchPosts,
//         staleTime: 1000 * 60,
//     });

//     const { mutate: deletePostById } = useMutation({
//         mutationFn: deletePost,
//         onSuccess: (_, id) => {
//           queryClient.setQueryData(['posts'], (oldData: Post[] = []) =>
//             oldData.filter((post) => post.id !== id)
//           );
//         },
//     });
//     const start = (page - 1) * itemsPerPage;
//     const paginatedData = posts.slice(start, start + itemsPerPage);
//     const totalPages = Math.ceil(posts.length / itemsPerPage);

//     return (
//         <div className='p-4'>
//             <h1 className='text-xl font-bold mb-4'>posts in India (Page {page})</h1>

//             {isLoading ? (
//                 <p>Loading...</p>
//             ) : isError ? (
//                 <p className='text-red-500'>Error fetching data</p>
//             ) : (
//                 <>
//                 <div className='space-y-2'>
//                     {paginatedData.map((post, idx) => (
//                         <div key={idx} className='border p-2 rounded'>
//                             <h2 className='font-semibold'>{post.title}</h2>
//                             <p>{post.body}</p>
//                             <p>{post.id}</p>
//                             <button
//                                 onClick={() => deletePostById(post.id)}
//                                 className='mt-2 bg-red-500 text-white px-3 py-1 rounded'
//                                 >
//                                 Delete
//                                 </button>
//                             {post.web_pages?.[0] && (
//                                 <a
//                                     href={post.web_pages[0]}
//                                     className='text-blue-500 underline'
//                                     target='_blank'
//                                     rel='noopener noreferrer'
//                                 >
//                                     Visit Website
//                                 </a>
//                             )}
//                         </div>
//                     ))}
//                 </div>

//                 <div className='flex gap-4 mt-6'>
//                     <button
//                         disabled={page === 1}
//                         onClick={() => setPage((p) => Math.max(p - 1, 1))}
//                         className='px-3 py-1 bg-gray-300 rounded disabled:opacity-50'
//                     >
//                         Previous
//                     </button>
//                     <span className='px-2'>Page {page} of {totalPages}</span>
//                     <button
//                         disabled={page === totalPages}
//                         onClick={() => setPage((p) => Math.min(p + 1, totalPages))}
//                         className='px-3 py-1 bg-blue-500 text-white rounded disabled:opacity-50'
//                     >
//                         Next
//                     </button>
//                 </div>
//                 </>
//             )}
//         </div>
//     );
// };

// export default UniversityListAndPagination;

'use client';

import React, { useState } from 'react';
import {
    useQuery,
    useMutation,
    useQueryClient,
} from '@tanstack/react-query';

interface Post {
    title: string;
    body: string;
    id: number;
    web_pages?: string[];
}

// Fetch all posts
const fetchPosts = async (): Promise<Post[]> => {
    const res = await fetch('https://jsonplaceholder.typicode.com/posts');
    if (!res.ok) throw new Error('Failed to fetch');
    return res.json();
};

// Delete post
const deletePost = async (id: number) => {
    const res = await fetch(`https://jsonplaceholder.typicode.com/posts/${id}`, {
        method: 'DELETE',
    });
    if (!res.ok) throw new Error('Failed to delete post');
    return true;
};

// Edit post
const updatePost = async (updatedPost: Post) => {
    const res = await fetch(`https://jsonplaceholder.typicode.com/posts/${updatedPost.id}`, {
        method: 'PATCH',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            title: updatedPost.title,
            body: updatedPost.body,
        }),
    });

    if (!res.ok) throw new Error('Failed to update post');
    return res.json();
};

const UniversityListAndPagination: React.FC = () => {
    const [page, setPage] = useState(1);
    const itemsPerPage = 10;
    const queryClient = useQueryClient();

    const { data: posts = [], isLoading, isError } = useQuery({
        queryKey: ['posts'],
        queryFn: fetchPosts,
        staleTime: 1000 * 60,
    });

    const { mutate: deletePostById } = useMutation({
        mutationFn: deletePost,
        onSuccess: (_, id) => {
            queryClient.setQueryData(['posts'], (oldData: Post[] = []) =>
                oldData.filter((post) => post.id !== id)
            );
        },
    });

    const { mutate: editPostById } = useMutation({
        mutationFn: updatePost,
        onSuccess: (updatedPost) => {
            queryClient.setQueryData(['posts'], (oldData: Post[] = []) =>
                oldData.map((post) => (post.id === updatedPost.id ? updatedPost : post))
            );
        },
    });

    const [editingPostId, setEditingPostId] = useState<number | null>(null);
    const [editedTitle, setEditedTitle] = useState('');
    const [editedBody, setEditedBody] = useState('');

    const start = (page - 1) * itemsPerPage;
    const paginatedData = posts.slice(start, start + itemsPerPage);
    const totalPages = Math.ceil(posts.length / itemsPerPage);

    const handleEdit = (post: Post) => {
        setEditingPostId(post.id);
        setEditedTitle(post.title);
        setEditedBody(post.body);
    };

    const handleUpdate = () => {
        if (editingPostId === null) return;
        editPostById({ id: editingPostId, title: editedTitle, body: editedBody });
        setEditingPostId(null);
    };

    return (
        <div className='p-4'>
            <h1 className='text-xl font-bold mb-4'>Posts (Page {page})</h1>

            {isLoading ? (
                <p>Loading...</p>
            ) : isError ? (
                <p className='text-red-500'>Error fetching data</p>
            ) : (
                <>
                <div className='space-y-2'>
                    {paginatedData.map((post) => (
                        <div key={post.id} className='border p-2 rounded'>
                            {editingPostId === post.id ? (
                                <>
                                    <input
                                        value={editedTitle}
                                        onChange={(e) => setEditedTitle(e.target.value)}
                                        className='w-full p-1 border mb-2 rounded'
                                    />
                                    <textarea
                                        value={editedBody}
                                        onChange={(e) => setEditedBody(e.target.value)}
                                        className='w-full p-1 border mb-2 rounded'
                                    />
                                    <div className='flex gap-2'>
                                        <button
                                            onClick={handleUpdate}
                                            className='bg-green-500 text-white px-3 py-1 rounded'
                                        >
                                            Save
                                        </button>
                                        <button
                                            onClick={() => setEditingPostId(null)}
                                            className='bg-gray-400 text-white px-3 py-1 rounded'
                                        >
                                            Cancel
                                        </button>
                                    </div>
                                </>
                            ) : (
                            <>
                                <h2 className='font-semibold'>{post.title}</h2>
                                <p>{post.body}</p>
                                <p>ID: {post.id}</p>
                                <div className='flex gap-2 mt-2'>
                                    <button
                                        onClick={() => handleEdit(post)}
                                        className='bg-yellow-500 text-white px-3 py-1 rounded'
                                    >
                                        Edit
                                    </button>
                                    <button
                                        onClick={() => deletePostById(post.id)}
                                        className='bg-red-500 text-white px-3 py-1 rounded'
                                    >
                                        Delete
                                    </button>
                                </div>
                            </>
                            )}
                        </div>
                    ))}
                </div>

                <div className='flex gap-4 mt-6'>
                    <button
                        disabled={page === 1}
                        onClick={() => setPage((p) => Math.max(p - 1, 1))}
                        className='px-3 py-1 bg-gray-300 rounded disabled:opacity-50'
                    >
                        Previous
                    </button>
                    <span className='px-2'>
                        Page {page} of {totalPages}
                    </span>
                    <button
                        disabled={page === totalPages}
                        onClick={() => setPage((p) => Math.min(p + 1, totalPages))}
                        className='px-3 py-1 bg-blue-500 text-white rounded disabled:opacity-50'
                    >
                        Next
                    </button>
                </div>
                </>
            )}
        </div>
    );
};

export default UniversityListAndPagination;

