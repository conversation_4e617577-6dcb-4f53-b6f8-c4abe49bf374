import 'next-auth';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      name?: string | null;
      email: string;
      image?: string | null;
      roles?: string[];
      actions?: Array<{
        name: string;
        feature: string;
      }>;
      userData?: any;
    };
    accessToken?: string;
    refreshToken?: string;
    idToken?: string;
  }

  interface User {
    id: string;
    name?: string | null;
    email: string;
    image?: string | null;
    roles?: string[];
    actions?: Array<{
      name: string;
      feature: string;
    }>;
    accessToken?: string;
    refreshToken?: string;
    userData?: any;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    id: string;
    roles?: string[];
    actions?: Array<{
      name: string;
      feature: string;
    }>;
    accessToken?: string;
    refreshToken?: string;
    idToken?: string;
    userData?: any;
  }
}
