import React from 'react';
import { IconProps } from '@/types';

const Bookmark: React.FC<IconProps> = ({
    className
}) => {
    return (
        <svg
            className={className}
            width="16"
            height="17"
            viewBox="0 0 16 17"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
        >
            <mask
                id="mask0_4174_28949"
                style={{maskType:'alpha'}}
                maskUnits="userSpaceOnUse"
                x="0"
                y="0"
                width="16"
                height="17"
            >
                <rect y="0.5" width="16" height="16" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_4174_28949)">
                <path
                    d="M8.00065 12.5L5.20065 13.7C4.75621 13.8889 4.33398 13.8528 3.93398 13.5917C3.53398 13.3306 3.33398 12.9611 3.33398 12.4833V3.83333C3.33398 3.46667 3.46454 3.15278 3.72565 2.89167C3.98676 2.63056 4.30065 2.5 4.66732 2.5H11.334C11.7007 2.5 12.0145 2.63056 12.2757 2.89167C12.5368 3.15278 12.6673 3.46667 12.6673 3.83333V12.4833C12.6673 12.9611 12.4673 13.3306 12.0673 13.5917C11.6673 13.8528 11.2451 13.8889 10.8007 13.7L8.00065 12.5ZM8.00065 11.0333L11.334 12.4667V3.83333H4.66732V12.4667L8.00065 11.0333Z"
                    fill="currentColor"
                />
            </g>
        </svg>
    );
};

export default Bookmark;
