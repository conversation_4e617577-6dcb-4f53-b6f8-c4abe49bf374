'use client';

import Aside from '../Aside';
import Image from 'next/image';
import { useState } from 'react';
import UserDropdown from '../UserDropdown';
import InputWithIcon from '../InputWithIcon';
import Search from '@/app/assets/svg/search';
import { DashboardLayoutProps } from '@/types';
// import CustomBreadcrumb from '../CustomBreadcrumb';
import WhatCircle from '@/app/assets/svg/WhatCircle';
import Hamburger from '@/app/assets/svg/hamburger.svg';
import NotificationsIcon from '@/app/assets/svg/NotificationsIcon';


const DashboardLayout: React.FC<DashboardLayoutProps> = ({ 
    children 
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const toggleSidebar = () => setIsOpen(!isOpen);
    const [isExpanded, setIsExpanded] = useState(false);
    const expandSidbar = () => setIsExpanded(!isExpanded);

    return (
        <div className='flex relative h-screen'>
            <Aside  
                isExpanded={isExpanded} 
                expandSidbar={expandSidbar} 
            />
            <main className='flex-1 sm:ml-24 px-5 md:px-10 z-0 overflow-y-auto overflow-x-hidden'>
                <div className='flex md:justify-between justify-between items-center py-5'>
                    <div className='hidden md:block'>
                        <InputWithIcon 
                            icon={Search} 
                            placeholder='Search' 
                            className='rounded-[50px] py-2.5 border-[0.5px] bg-white border-grayTwo' 
                        />
                    </div>
                    <div className='md:hidden block'>
                        <button  onClick={toggleSidebar}>
                            <Image 
                                src={Hamburger}
                                alt='Hamburger icon'
                            />
                        </button>
                    </div>
                    <div className='flex gap-8 items-center'>
                        <div className='max-w-6 max-h-6 w-auto h-auto'>
                            <button>
                                <NotificationsIcon />
                            </button>   
                        </div>
                        <div className='max-w-6 max-h-6 w-auto h-auto'>
                            <WhatCircle />
                        </div>
                        <div>
                            <UserDropdown />
                        </div>
                    </div>
                    
                    <div
                        className={`fixed top-0 left-0 h-screen w-64 bg-white transform 
                        ${isOpen ? 'translate-x-0' : '-translate-x-full'} 
                        transition-transform duration-300 z-40`}
                    >
                        <Aside  
                            isExpanded={isExpanded} 
                            expandSidbar={expandSidbar} 
                            toggleSidebar={toggleSidebar} 
                        />
                    </div>

                    {isOpen && (
                        <div
                            onClick={toggleSidebar}
                            className='fixed inset-0 bg-[#131313] bg-opacity-50 z-30'
                        />
                    )}
                </div>

                {/* <CustomBreadcrumb /> */}
                {children}
            </main>
        </div>
    )};

export default DashboardLayout

