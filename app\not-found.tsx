import Link from 'next/link';
import ArrowBack from './assets/svg/ArrowBack';
import NotFoundBg from './assets/svg/NotFoundBg';
import DashboardLayout from './components/layout/DashboardLayout';

export const metadata = {
    title: ' Page Not Found | ApplyGoal',
    description: `Sorry, the page you’re looking for does not exist. Return to ApplyGoal's homepage or browse our latest content.`
};

export default function NotFound() {
    return (
        <DashboardLayout>
            <div className='relative'>
                <div className='pt-[700px] md:pt-36 w-screen z-0 h-screen'>
                    <NotFoundBg />
                </div>
                <div className='absolute top-0 flex flex-col items-center justify-center h-screen w-full z-40'>
                    <span className='text-[136px] md:text-[320px] md:leading-[387px] font-black text-[#D9E4FC]'>404</span>
                    <div className='text-center text-secondaryColor text-2xl leading-[36px] font-normal'>
                        <h1>This link is unavailable.</h1>
                        <p>But we’re always here to help - try searching again!</p>
                    </div>

                    <div className='mt-14'>
                        <Link 
                            href={'/'}
                            className='flex items-center gap-1 rounded-full bg-primaryColor py-3.5 px-4 font-semibold text-sm leading-none text-white'
                        >
                            <ArrowBack />
                            Back to Home
                        </Link>
                    </div>
                </div>
            </div>
        </DashboardLayout>
    )
}
