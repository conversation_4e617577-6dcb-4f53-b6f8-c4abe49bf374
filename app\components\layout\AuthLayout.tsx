import React from 'react';
import Link from 'next/link';
import Heading from '../Heading';
import { AuthLayoutProps } from '@/types';
import ApplyGoalLogo from '@/app/assets/svg/ApplyGoalLogo';

const AuthLayout: React.FC<AuthLayoutProps> = ({ 
    title, 
    heading,
    imageSrc, 
    children, 
    description,
    successIcon
}) =>  {
    return (
        <>
        <div className='flex flex-col md:flex-row h-screen'>
            <div className='w-full md:w-1/2 bg-[#F4F7FE] h-screen md:flex flex-col items-center justify-between py-10 md:px-[100px] hidden'>
                <div className='flex w-full'>
                    <Link href={'https://www.applygoal.com'}>
                        <ApplyGoalLogo />
                    </Link>
                </div>
                <div className="flex justify-center">
                    {imageSrc}
                </div>
                
                <p className='font-normal italic text-3xl leading-[42px] text-center text-tertiary'>{description}</p>
            </div>
            <div className='bg-white w-full md:w-1/2 md:px-0 px-8 overflow-y-auto grid grid-cols-1 items-center'>
                <div className='md:py-20 py-[27px]'>
                    <div className='flex flex-col items-center justify-center gap-6'>
                        <div className='flex flex-col items-center gap-3'>
                            {successIcon}
                            <Heading level='h1'>
                                {title}
                            </Heading>
                            <p className='text-base text-center leading-6 font-normal text-grayFour max-w-[420px]'>
                                {heading}
                            </p>
                        </div>
                    </div>
                    <div>
                        {children}
                    </div>
                </div>
            </div>
        </div>
        </>
    )
}
export default AuthLayout;