'use client';

import Image from 'next/image';
import Check from '../assets/svg/check';
import XIcon from '../assets/svg/x_icon';
import Search from '../assets/svg/search';
import PenIcon from '../assets/svg/PenIcon';
import Heading from '../components/Heading';
import Unselect from '../assets/svg/Unselect';
import ThreeDot from '../assets/svg/ThreeDot';
import Clipboard from '../assets/svg/Clipboard';
import { Button } from '@/components/ui/button';
import AddNewRole from '../assets/svg/AddNewRole';
import { Checkbox } from '@/components/ui/checkbox';
import ShowAndHide from '../assets/svg/ShowAndHide';
import singleUser from '../assets/img/singleUser.png';
import InputWithIcon from '../components/InputWithIcon';
import DeleteForever from '../assets/svg/DeleteForever';
import RoleDropdown from '../components/ui/RoleDropdown';
import React, { useState, useRef, useMemo } from 'react';
import AddCirclePrimary from '../assets/svg/AddCirclePrimary';
import DashboardLayout from '../components/layout/DashboardLayout';
import AddSuperadminRole from '../components/ui/AddSuperadminRole';
import VisibilityPermission from '../assets/svg/VisibilityPermission';
import AddDepartmentWiseRole from '../components/ui/AddDepartmentWiseRole';
import { 
    useGetRolesQuery, 
    useCreateAndUpdateRoleMutation, 
    useDeleteRoleMutation,
    useUpdateRoleNameMutation,
} from '@/lib/redux/api/rolesApi';
import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from '@/components/ui/accordion'
import Cookies from 'js-cookie';

const Page = () => {
    // Fetch roles data from API
    const { data: rolesData, error, isLoading, refetch } = useGetRolesQuery();
    console.log(rolesData)
    const [createRole] = useCreateAndUpdateRoleMutation();
    const [deleteRoleMutation] = useDeleteRoleMutation();
    const [updateRoleNameMutation] = useUpdateRoleNameMutation();

    // Transform API data to match the existing UI structure
    const { roles, sections, initialPermissions, roleIdMap } = useMemo(() => {
        if (!rolesData?.roles) {
            return {
                roles: [],
                sections: [],
                initialPermissions: {},
                roleIdMap: {}
            };
        }
        console.log(rolesData.roles)
        // Extract roles and create role ID mapping
        const roles = rolesData.roles.map(role => role.role);
        const roleIdMap: Record<string, number> = {};
        rolesData.roles.forEach(role => {
            roleIdMap[role.role] = role.id;
        });

        // Transform modules to sections
        const sections = rolesData.roles[0]?.modules.map(module => ({
            name: module.module,
            permissions: module.features.map(feature => feature.feature)
        })) || [];

        // Create initial permissions object
        const initialPermissions: Record<string, Record<string, boolean>> = {};
        roles.forEach((role) => {
            initialPermissions[role] = {};
            sections.forEach((section) => {
                section.permissions.forEach((perm) => {
                    initialPermissions[role][`${section.name}__${perm}`] = false;
                });
            });
        });

        // Populate with actual permissions from API
        rolesData.roles.forEach(apiRole => {
            apiRole.modules.forEach(module => {
                module.features.forEach(feature => {
                    const key = `${module.module}__${feature.feature}`;
                    if (initialPermissions[apiRole.role]) {
                        initialPermissions[apiRole.role][key] = feature.permissions;
                    }
                });
            });
        });

        return { roles, sections, initialPermissions, roleIdMap };
    }, [rolesData]);

    const [permissions, setPermissions] = useState(initialPermissions);
    const [rolesList, setRolesList] = useState(roles);
    const [editingRole, setEditingRole] = useState<string | null>(null);
    const [editingRoleName, setEditingRoleName] = useState('');
    const [isCreatingNewRole, setIsCreatingNewRole] = useState(false);
    const [newRoleName, setNewRoleName] = useState('');
    const [showCloneSelector, setShowCloneSelector] = useState(false);
    const [cloneSearchTerm, setCloneSearchTerm] = useState('');
    const [selectedRolesToClone, setSelectedRolesToClone] = useState<string[]>([]);
    const [isShowHideMode, setIsShowHideMode] = useState(false);
    const [hiddenRoles, setHiddenRoles] = useState<string[]>([]);
    const [hiddenRolesFromCookies, setHiddenRolesFromCookies] = useState<string[]>([]);
    const [permissionChanges, setPermissionChanges] = useState<Array<{
        id: number;
        roleName: string;
        module: string;
        feature: string;
        permission: boolean;
    }>>([]);
    console.log(permissionChanges)
    const [isSavingPermissions, setIsSavingPermissions] = useState(false);
    const sectionRefs = useRef<Record<string, HTMLDivElement | null>>({});

    React.useEffect(() => {
        setPermissions(initialPermissions);
        setRolesList(roles);
    }, [initialPermissions, roles]);

    // Load selected roles from cookies on component mount
    React.useEffect(() => {
        const loadHiddenRolesFromCookies = () => {
            const hiddenRoles = Cookies.get('hiddenRoles');
            if (hiddenRoles) {
                try {
                    const hiddenRolesArray = JSON.parse(hiddenRoles);
                    setHiddenRolesFromCookies(hiddenRolesArray);
                    setHiddenRoles(hiddenRolesArray);
                } catch (error) {
                    console.error('Error parsing hidden roles from cookies:', error);
                }
            }
        };

        loadHiddenRolesFromCookies();
    }, []);


    const scrollToSection = (sectionName: string) => {
        const el = sectionRefs.current[sectionName];
        if (el) {
            el.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    };

    const togglePermission = (role: string, key: string) => {
        const [module, feature] = key.split('__');
        const roleId = roleIdMap[role];
        const currentValue = permissions[role]?.[key] || false;
        const newValue = !currentValue;
        
        // Update local state
        setPermissions((prev) => ({
            ...prev,
            [role]: {
                ...prev[role],
                [key]: newValue,
            }
        }));
        
        // Only track the change if it's actually different from the initial state
        const initialValue = initialPermissions[role]?.[key] || false;
        if (newValue !== initialValue) {
            // Track the change
            setPermissionChanges(prev => {
                const existingChangeIndex = prev.findIndex(change => 
                    change.id === roleId && 
                    change.module === module && 
                    change.feature === feature
                );
                
                if (existingChangeIndex >= 0) {
                    // Update existing change
                    const updatedChanges = [...prev];
                    updatedChanges[existingChangeIndex] = {
                        ...updatedChanges[existingChangeIndex],
                        permission: newValue
                    };
                    return updatedChanges;
                } else {
                    // Add new change
                    return [...prev, {
                        id: roleId,
                        roleName: role,
                        module: module,
                        feature: feature,
                        permission: newValue
                    }];
                }
            });
        } else {
            // If the value is back to initial state, remove it from changes
            setPermissionChanges(prev => 
                prev.filter(change => 
                    !(change.id === roleId && 
                      change.module === module && 
                      change.feature === feature)
                )
            );
        }
    };

    const selectAllPermissions = (role: string) => {
        const roleId = roleIdMap[role];
        const newChanges: Array<{
            id: number;
            roleName: string;
            module: string;
            feature: string;
            permission: boolean;
        }> = [];
        
        setPermissions((prev) => {
            const newPermissions = { ...prev };
            newPermissions[role] = {};
            
            sections.forEach((section) => {
                section.permissions.forEach((perm) => {
                    const key = `${section.name}__${perm}`;
                    const initialValue = initialPermissions[role]?.[key] || false;
                    newPermissions[role][key] = true;
                    
                    // Track changes only for permissions that were initially false
                    if (!initialValue) {
                        newChanges.push({
                            id: roleId,
                            roleName: role,
                            module: section.name,
                            feature: perm,
                            permission: true
                        });
                    }
                });
            });
            
            return newPermissions;
        });
        
        // Add new changes to the tracking state
        setPermissionChanges(prev => {
            const existingChanges = prev.filter(change => change.roleName !== role);
            return [...existingChanges, ...newChanges];
        });
    };

    const unselectAllPermissions = (role: string) => {
        const roleId = roleIdMap[role];
        const newChanges: Array<{
            id: number;
            roleName: string;
            module: string;
            feature: string;
            permission: boolean;
        }> = [];
        
        setPermissions((prev) => {
            const newPermissions = { ...prev };
            newPermissions[role] = {};
            
            sections.forEach((section) => {
                section.permissions.forEach((perm) => {
                    const key = `${section.name}__${perm}`;
                    const initialValue = initialPermissions[role]?.[key] || false;
                    newPermissions[role][key] = false;
                    
                    // Track changes only for permissions that were initially true
                    if (initialValue) {
                        newChanges.push({
                            id: roleId,
                            roleName: role,
                            module: section.name,
                            feature: perm,
                            permission: false
                        });
                    }
                });
            });
            
            return newPermissions;
        });
        
        // Add new changes to the tracking state
        setPermissionChanges(prev => {
            const existingChanges = prev.filter(change => change.roleName !== role);
            return [...existingChanges, ...newChanges];
        });
    };

    const startRenameRole = (role: string) => {
        setEditingRole(role);
        setEditingRoleName(role);
    };

    const saveRoleName = async () => {
        if (editingRole && editingRoleName.trim()) {
            const newRoleName = editingRoleName.trim();
            
            // Don't update if the name hasn't changed
            if (editingRole === newRoleName) {
                setEditingRole(null);
                setEditingRoleName('');
                return;
            }

            try {
                // Get the role ID from the mapping
                const roleId = roleIdMap[editingRole];
                
                if (!roleId) {
                    console.error('Role ID not found for role:', editingRole);
                    return;
                }

                // Call the API to update the role name
                await updateRoleNameMutation({ id: roleId, newName: newRoleName }).unwrap();
                
                // Optimistically update the UI
                setRolesList(prev => prev.map(role => role === editingRole ? newRoleName : role));
                
                // Update permissions with new role name
                setPermissions(prev => {
                    const newPermissions = { ...prev };
                    newPermissions[newRoleName] = newPermissions[editingRole];
                    delete newPermissions[editingRole];
                    return newPermissions;
                });
                
                setEditingRole(null);
                setEditingRoleName('');
                
                console.log('Role renamed successfully:', editingRole, 'to', newRoleName);
            } catch (error) {
                console.error('Failed to rename role:', error);
                // You might want to show a toast or error message here
            }
        }
    };

    const cancelRename = () => {
        setEditingRole(null);
        setEditingRoleName('');
    };

    const deleteRole = async (roleToDelete: string) => {
        try {
            // Get the role ID from the mapping
            const roleId = roleIdMap[roleToDelete];
            
            if (!roleId) {
                console.error('Role ID not found for role:', roleToDelete);
                return;
            }

            // Call the API to delete the role
            await deleteRoleMutation(roleId).unwrap();
            
            // Optimistically update the UI
            setRolesList(prev => prev.filter(role => role !== roleToDelete));
            
            // Remove role from permissions
            setPermissions(prev => {
                const newPermissions = { ...prev };
                delete newPermissions[roleToDelete];
                return newPermissions;
            });
            
            console.log('Role deleted successfully:', roleToDelete);
        } catch (error) {
            console.error('Failed to delete role:', error);
            // You might want to show a toast or error message here
        }
    };

    const cloneRole = async (roleToClone: string) => {
        const clonedRoleName = `${roleToClone} (Copy)`;
        
        try {
            // Call the API to create the cloned role in the database
            const response = await createRole({
                id: 0,
                name: clonedRoleName,
            }).unwrap();
            console.log('Role cloned successfully:', response);
            
            // Optimistically update the UI
            setRolesList(prev => [...prev, clonedRoleName]);
            
            // Clone permissions for the new role
            setPermissions(prev => {
                const newPermissions = { ...prev };
                newPermissions[clonedRoleName] = { ...prev[roleToClone] };
                return newPermissions;
            });
            
            // Automatically start editing the cloned role
            setEditingRole(clonedRoleName);
            setEditingRoleName(clonedRoleName);
            
        } catch (error) {
            console.error('Failed to clone role:', error);
            // You might want to show a toast or error message here
        }
    };

    const addNewRole = () => {
        setIsCreatingNewRole(true);
        setNewRoleName('');
    };

    const saveNewRole = async () => {
        if (newRoleName.trim()) {
            const roleName = newRoleName.trim();
            console.log(roleName);
            try {
                // Call the mutation to create the new role in the database
                const response = await createRole({
                    id: 0,
                    name: roleName,
                }).unwrap();
                console.log(response);
                // Optimistically update the UI
                setRolesList(prev => [...prev, roleName]);
                setPermissions(prev => ({
                    ...prev,
                    [roleName]: {},
                }));
                sections.forEach((section) => {
                    section.permissions.forEach((perm) => {
                        setPermissions(prev => ({
                            ...prev,
                            [roleName]: {
                                ...prev[roleName],
                                [`${section.name}__${perm}`]: false,
                            }
                        }));
                    });
                });

                setIsCreatingNewRole(false);
                // setNewRoleName('');
            } catch (error) {
                // Handle error (e.g., show a toast or error message)
                console.error('Failed to create role:', error);
            }
        }
    };

    const cancelNewRole = () => {
        setIsCreatingNewRole(false);
        setNewRoleName('');
    };

    const openShowHideSelector = () => {
        setShowCloneSelector(true);
        setIsShowHideMode(true);
        setCloneSearchTerm('');
        setSelectedRolesToClone([...hiddenRoles]);
    };

    const openCloneSelector = () => {
        setShowCloneSelector(true);
        setIsShowHideMode(false);
        setCloneSearchTerm('');
        setSelectedRolesToClone([]);
    };

    const closeCloneSelector = () => {
        setShowCloneSelector(false);
        setIsShowHideMode(false);
        setCloneSearchTerm('');
        setSelectedRolesToClone([]);
    };

    const toggleRoleSelection = (roleName: string) => {
        setSelectedRolesToClone(prev => 
            prev.includes(roleName) 
                ? prev.filter(role => role !== roleName)
                : [...prev, roleName]
        );
    };

    const executeClone = () => {
        try {
            const clonedRoleNames: string[] = [];
            
            for (const roleToClone of selectedRolesToClone) {
                const clonedRoleName = `${roleToClone} (Copy)`;
                clonedRoleNames.push(clonedRoleName);
                
                // Call the API to create the cloned role in the database
                createRole({
                    id: 0,
                    name: clonedRoleName,
                }).unwrap();
                
                // Optimistically update the UI
                setRolesList(prev => [...prev, clonedRoleName]);
                
                // Clone permissions for the new role
                setPermissions(prev => {
                    const newPermissions = { ...prev };
                    newPermissions[clonedRoleName] = { ...prev[roleToClone] };
                    return newPermissions;
                });
            }
            
            console.log('Roles cloned successfully');
            closeCloneSelector();
            
            // Automatically start editing the first cloned role if only one was cloned
            if (clonedRoleNames.length === 1) {
                setEditingRole(clonedRoleNames[0]);
                setEditingRoleName(clonedRoleNames[0]);
            }
        } catch (error) {
            console.error('Failed to clone roles:', error);
            // You might want to show a toast or error message here
        }
    };

    const executeShowHide = () => {
        if (isShowHideMode) {
            // Check if we're showing or hiding roles
            const rolesToShow = selectedRolesToClone.filter(role => hiddenRoles.includes(role));
            const rolesToHide = selectedRolesToClone.filter(role => !hiddenRoles.includes(role));
            
            let newHiddenRoles = [...hiddenRoles];
            
            // Show selected hidden roles
            if (rolesToShow.length > 0) {
                newHiddenRoles = newHiddenRoles.filter(role => !rolesToShow.includes(role));
            }
            
            // Hide selected visible roles
            if (rolesToHide.length > 0) {
                newHiddenRoles = [...new Set([...newHiddenRoles, ...rolesToHide])];
            }
            
            setHiddenRoles(newHiddenRoles);
            setHiddenRolesFromCookies(newHiddenRoles);
            
            // Save to cookies
            Cookies.set('hiddenRoles', JSON.stringify(newHiddenRoles), { expires: 7 });
        }
        closeCloneSelector();
    };

    const hideRole = (roleName: string) => {
        const newHiddenRoles = [...hiddenRoles, roleName];
        setHiddenRoles(newHiddenRoles);
        setHiddenRolesFromCookies(newHiddenRoles);
        
        // Save to cookies
        Cookies.set('hiddenRoles', JSON.stringify(newHiddenRoles), { expires: 7 });
    };

    const visibleRoles = rolesList.filter(role => !hiddenRoles.includes(role));

    const filteredRoles = rolesList.filter(role => 
        role.toLowerCase().includes(cloneSearchTerm.toLowerCase())
    );

    const savePermissionChanges = async () => {
        if (permissionChanges.length === 0) {
            return; // No changes to save
        }
        
        if (!rolesData?.roles) {
            console.error('Roles data not available');
            return;
        }
        
        setIsSavingPermissions(true);
        
        try {
            console.log('Starting to save permission changes:', permissionChanges);
            console.log('Number of changes to save:', permissionChanges.length);
            
            // Group changes by role
            const changesByRole = permissionChanges.reduce((acc, change) => {
                if (!acc[change.id]) {
                    acc[change.id] = [];
                }
                acc[change.id].push(change);
                return acc;
            }, {} as Record<number, typeof permissionChanges>);
            
            // For each role, create the complete role data with updated permissions
            for (const [roleId, changes] of Object.entries(changesByRole)) {
                const roleName = changes[0].roleName;
                const roleData = rolesData?.roles.find(role => role.id === parseInt(roleId));
                
                if (!roleData) {
                    console.error('Role data not found for roleId:', roleId);
                    continue;
                }
                
                console.log(`Processing role: ${roleName} (ID: ${roleId})`);
                
                // Create updated modules with new permissions
                const updatedModules = roleData.modules.map(module => ({
                    ...module,
                    features: module.features.map(feature => {
                        const change = changes.find(c => 
                            c.module === module.module && c.feature === feature.feature
                        );
                        
                        return {
                            ...feature,
                            permissions: change ? change.permission : feature.permissions
                        };
                    })
                }));
                
                // Create the complete role data to send
                const completeRoleData = {
                    id: parseInt(roleId),
                    name: roleName,
                    department: roleData.department,
                    modules: updatedModules
                };
                
                console.log(`Sending complete role data for ${roleName}:`, completeRoleData);
                
                try {
                    // Use the createAndUpdateRole mutation to update the entire role
                    const result = await createRole(completeRoleData).unwrap();
                    console.log(`Successfully saved role ${roleName}:`, result);
                } catch (roleError) {
                    console.error(`Failed to save role ${roleName}:`, roleError);
                    throw roleError;
                }
            }
            
            // Clear the changes after successful save
            setPermissionChanges([]);
            console.log('All permission changes saved successfully. Changes array cleared.');
            console.log('Current permissionChanges state:', []);
            
            // Refetch the roles data to get the updated permissions
            // This will update the initialPermissions and local permissions state
            console.log('Refetching roles data...');
            await refetch();
            console.log('Roles data refetched successfully');
            
        } catch (error) {
            console.error('Failed to save permission changes:', error);
            // You might want to show a toast or error message here
        } finally {
            setIsSavingPermissions(false);
        }
    };

    // Loading state
    if (isLoading) {
        return (
            <DashboardLayout>
                <div className="flex justify-center items-center h-64">
                    <div className="text-lg text-grayFive">Loading roles and permissions...</div>
                </div>
            </DashboardLayout>
        );
    }

    // Error state
    if (error) {
        return (
            <DashboardLayout>
                <div className="flex justify-center items-center h-64">
                    <div className="text-lg text-red-500">
                        Error loading roles: {JSON.stringify(error)}
                    </div>
                </div>
            </DashboardLayout>
        );
    }

    // No data state
    if (!rolesData || roles.length === 0) {
        return (
            <DashboardLayout>
                <div className="flex justify-center items-center h-64">
                    <div className="text-lg text-grayFive">No roles data available</div>
                </div>
            </DashboardLayout>
        );
    }

    return (
        <DashboardLayout>
            <div className='flex justify-between items-center'>
                <Heading level='h1'>
                    Access Permissions
                </Heading>
                <div className='max-w-[400px] w-full'>
                    <InputWithIcon 
                        icon={Search} 
                        placeholder='Search in Access Permissions' 
                        className='rounded-[50px] py-2.5 border-[0.5px] bg-white border-grayTwo' 
                    />
                </div>
            </div>
            <div className='mt-10'>
                <div className='flex gap-6'>
                    <div className='w-[17%]'>
                        <div className='bg-white rounded-[20px] p-2.5'>
                            {
                                sections.map((item, index) => (
                                    <button 
                                        key={index}
                                        onClick={() => scrollToSection(item.name)}
                                        className='w-full text-left flex flex-col py-3 px-[18px] font-normal text-sm leading-none text-grayFive hover:bg-primaryThree hover:rounded-[10px] hover:text-grayFive hover:font-semibold' 
                                    >
                                        {item.name}
                                    </button>
                                ))
                            }
                        </div>
                        {/* <button className='mt-6 border-2 border-primaryColor rounded-full flex items-center gap-1 py-3.5     justify-center font-semibold text-base leading-6 text-primaryColor w-full'>
                            <Plus />
                            Add Module
                        </button> */}
                    </div>
                    
                    <div className='w-[83%] relative'>
                        <div className='flex flex-col bg-white rounded-[20px] h-[calc(100vh-300px)] mb-8'>
                            <div className='overflow-x-auto'>
                                <div className='min-w-max'>
                                    <table className='w-full'>
                                        <thead>
                                            <tr className='flex'>
                                                <th className='py-2.5 px-[18px] min-w-[300px] flex-shrink-0'>
                                                    <div className='flex justify-between items-center'>
                                                        <span className='font-bold text-sm leading-5 text-grayFive'>Roles</span>
                                                        <RoleDropdown
                                                            menuItems={[
                                                                {
                                                                    icon: <AddNewRole />,
                                                                    label: "Add new role",
                                                                    description: "Creates a new role",
                                                                    onClick: () => addNewRole()
                                                                },
                                                                {
                                                                    icon: <Clipboard />,
                                                                    label: "Clone role",
                                                                    description: "Creates a new role that is an exact copy of this role",
                                                                    onClick: () => openCloneSelector()
                                                                },
                                                                {
                                                                    icon: <ShowAndHide />,
                                                                    label: "Show/Hide roles",
                                                                    description: "Shows or hides roles in this table",
                                                                    onClick: () => openShowHideSelector(),
                                                                    separator: true
                                                                }
                                                            ]}
                                                            triggerIcon={<ThreeDot />}
                                                        />
                                                    </div>
                                                    <div className='flex justify-start'>
                                                        <div className='mt-2.5 inline-flex items-center justify-start font-normal text-[10px] leading-[15px] text-grayThree gap-1 rounded-[16px] py-0.5 px-2 bg-grayOne w-auto'>
                                                            <VisibilityPermission />
                                                            <span className='text-grayFive'>{visibleRoles.length}</span>
                                                            <span>out of</span>
                                                            <span>{rolesList.length}</span>
                                                        </div>
                                                    </div>
                                                </th>
                                                {visibleRoles.map((role:any, idx) => (
                                                    <th key={role + '-' + idx} className='py-2.5 flex justify-center min-w-[300px] flex-shrink-0'>
                                                        <div className='flex flex-col justify-center items-center gap-2.5'>
                                                            <div className='flex items-center'>
                                                                {editingRole === role ? (
                                                                    <div className='flex items-center gap-2'>
                                                                        <input
                                                                            type='text'
                                                                            value={editingRoleName}
                                                                            onChange={(e) => setEditingRoleName(e.target.value)}
                                                                            onKeyDown={(e) => {
                                                                                if (e.key === 'Enter') {
                                                                                    saveRoleName();
                                                                                } else if (e.key === 'Escape') {
                                                                                    cancelRename();
                                                                                }
                                                                            }}
                                                                            onBlur={saveRoleName}
                                                                            className='text-center px-[18px] font-bold text-sm leading-5 text-grayFive border-2 py-1 border-primaryColor rounded bg-white focus:outline-none focus:ring-2 focus:ring-primaryColor cursor-text'
                                                                            autoFocus
                                                                        />
                                                                    </div>
                                                                ) : (
                                                                    <span className='px-[18px] font-bold text-sm leading-5 text-grayFive'>{role}</span>
                                                                )}
                                                                <RoleDropdown
                                                                    menuItems={[
                                                                        {
                                                                            icon: <Check className='text-primaryColor w-[18px] h-[18px]' />,
                                                                            label: "Select all permissions",
                                                                            description: "Enables all permissions once",
                                                                            onClick: () => selectAllPermissions(role)
                                                                        },
                                                                        {
                                                                            icon: <Unselect />,
                                                                            label: "Unselect all permissions",
                                                                            description: "Disable all permissions at once",
                                                                            onClick: () => unselectAllPermissions(role)
                                                                        },
                                                                        {
                                                                            icon: <PenIcon />,
                                                                            label: "Rename",
                                                                            description: "Changes the roles name",
                                                                            onClick: () => startRenameRole(role)
                                                                        },
                                                                        {
                                                                            icon: <Clipboard />,
                                                                            label: "Clone",
                                                                            description: "Creates a new role that is an exact copy of this role",
                                                                            onClick: () => cloneRole(role)
                                                                        },
                                                                        {
                                                                            icon: <DeleteForever />,
                                                                            label: "Delete",
                                                                            description: "Deletes the role and all assigned access permissions",
                                                                            onClick: () => deleteRole(role)
                                                                        },
                                                                        {
                                                                            icon: <ShowAndHide />,
                                                                            label: "Hide",
                                                                            description: "Hides the role from the access permissions table",
                                                                            onClick: () => hideRole(role)
                                                                        },
                                                                    ]}
                                                                    triggerIcon={<ThreeDot />}
                                                                />
                                                            </div>
                                                            <div className='flex items-center gap-3'>
                                                                <div className='flex items-center'>
                                                                    <Image src={singleUser} alt='singleUser' />
                                                                    <Image className='-ml-1.5' src={singleUser} alt='singleUser' />
                                                                    <Image className='-ml-1.5' src={singleUser} alt='singleUser' />
                                                                    <Image className='-ml-1.5' src={singleUser} alt='singleUser' />
                                                                    <Image className='-ml-1.5' src={singleUser} alt='singleUser' />
                                                                </div>
                                                                {role === 'Super Admin' ?  (<AddSuperadminRole />) : (<AddDepartmentWiseRole roleName={role} />)}
                                                            </div>
                                                        </div>
                                                    </th>
                                                ))}
                                                {isCreatingNewRole && (
                                                    <th className='flex border-b border-grayOne'>
                                                        <div className='min-w-[300px] flex-shrink-0 py-2.5 px-[18px] flex justify-center items-center'>
                                                            <div className='flex flex-col justify-center items-center gap-2.5'>
                                                                <div className='flex items-center gap-2'>
                                                                    <input
                                                                        type='text'
                                                                        value={newRoleName}
                                                                        onChange={(e) => setNewRoleName(e.target.value)}
                                                                        onKeyDown={(e) => {
                                                                            if (e.key === 'Enter') {
                                                                                saveNewRole();
                                                                            } else if (e.key === 'Escape') {
                                                                                cancelNewRole();
                                                                            }
                                                                        }}
                                                                        onBlur={saveNewRole}
                                                                        placeholder='Enter role name'
                                                                        className='text-center px-[18px] font-bold text-sm leading-5 text-grayFive border-2 py-1 border-primaryColor rounded bg-white focus:outline-none focus:ring-2 focus:ring-primaryColor cursor-text'
                                                                        autoFocus
                                                                    />
                                                                </div>
                                                                <div className='flex items-center gap-3'>
                                                                    <div className='flex items-center'>
                                                                        <Image src={singleUser} alt='singleUser' />
                                                                        <Image className='-ml-1.5' src={singleUser} alt='singleUser' />
                                                                        <Image className='-ml-1.5' src={singleUser} alt='singleUser' />
                                                                        <Image className='-ml-1.5' src={singleUser} alt='singleUser' />
                                                                        <Image className='-ml-1.5' src={singleUser} alt='singleUser' />
                                                                    </div>
                                                                    <AddDepartmentWiseRole roleName={newRoleName || 'New Role'} />
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </th>
                                                )}
                                                <th className='py-2.5 px-[18px] flex justify-center items-center min-w-[300px] flex-shrink-0'>
                                                    <button onClick={addNewRole}>
                                                        <AddCirclePrimary />
                                                    </button>
                                                </th>
                                            </tr>
                                        </thead>
                                    </table>
                                   
                                    <Accordion
                                        type='multiple'
                                        defaultValue={sections.map((section) => section.name)}
                                    >
                                        {sections.map((section) => (
                                            <AccordionItem 
                                                key={section.name} 
                                                value={section.name} 
                                                className='  '
                                                onClick={() => scrollToSection(section.name)}
                                            >
                                                <div 
                                                    ref={(el) => {
                                                        sectionRefs.current[section.name] = el;
                                                    }}
                                                >
                                                    <AccordionTrigger 
                                                        className='justify-start px-[18px] py-3.5 bg-[#1E62E00D] text-sm leading-5 tracking-[0.4px] font-medium border-b border-grayOne text-grayFive hover:no-underline gap-1.5'
                                                    >
                                                        <div className="flex items-center gap-1.5">
                                                            {section.name}
                                                        </div>
                                                    </AccordionTrigger>
                                                </div>
                                                
                                                <AccordionContent className='p-0'>
                                                    <table className='w-full'>
                                                        <tbody>
                                                            {section.permissions.map((perm) => (
                                                                <tr className='flex border-b border-grayOne' key={perm}>
                                                                    <td className='font-normal leading-5 tracking-[0.25px] px-6 py-3.5 text-sm text-grayFive w-[300px] flex-shrink-0'>{perm}</td>
                                                                    {visibleRoles.map((role, idx) => {
                                                                        const key = `${section.name}__${perm}`
                                                                        return (
                                                                            <td key={`${key}__${role}__${idx}`} className='text-center px-6 py-3.5 min-w-[300px] flex-shrink-0'>
                                                                                <Checkbox
                                                                                    checked={permissions[role]?.[key] || false}
                                                                                    onCheckedChange={() => togglePermission(role, key)}
                                                                                    className='w-[18px] h-[18px] ronded-[5px] border border-grayFour'
                                                                                />
                                                                            </td>
                                                                        );
                                                                    })}
                                                                </tr>
                                                            ))}
                                                        </tbody>
                                                    </table>
                                                </AccordionContent>
                                            </AccordionItem>
                                        ))}
                                    </Accordion>
                                </div>
                            </div>
                        </div>
                        <div className='rounded-[20px] sticky bottom-8 left-0 right-0 bg-primaryOne border-2 border-[#1952BB33] py-[22px] pr-[22px] flex justify-end gap-[18px]'>
                            <Button className='rounded-full border border-grayTwo py-2.5 px-4 font-semibold text-base leading-6 text-grayFour' variant='outline'>Cancel</Button>
                            <Button 
                                onClick={savePermissionChanges}
                                disabled={permissionChanges.length === 0 || isSavingPermissions}
                                className='rounded-full py-2.5 px-4 bg-primaryColor text-white font-semibold text-base leading-6 disabled:opacity-50 disabled:cursor-not-allowed'
                            >
                                {isSavingPermissions ? 'Saving...' : `Save changes${permissionChanges.length > 0 ? ` (${permissionChanges.length})` : ''}`}
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
            
            {/* Clone Selector Modal */}
            {showCloneSelector && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                    <div className="bg-white rounded-[20px] p-6 w-[500px] max-h-[600px] overflow-y-auto">
                        <div className="flex justify-between items-center mb-4">
                            <h3 className="text-lg font-semibold text-grayFive">
                                {isShowHideMode ? 'Show/Hide Roles' : 'Clone Roles'}
                            </h3>
                            <button 
                                onClick={closeCloneSelector}
                                className="text-grayThree hover:text-grayFive"
                            >
                                <XIcon  className='w-[18px] h-[18px]'/>
                            </button>
                        </div>
                        
                        <div className="mb-4">
                            <input
                                type="text"
                                placeholder={isShowHideMode ? "Search roles to hide..." : "Search roles..."}
                                value={cloneSearchTerm}
                                onChange={(e) => setCloneSearchTerm(e.target.value)}
                                className="w-full px-4 py-2 border border-grayTwo rounded-lg focus:outline-none focus:ring-2 focus:ring-primaryColor"
                            />
                        </div>
                        
                        <div className="mb-4">
                            <h4 className="text-sm font-medium text-grayFive mb-2">
                                {isShowHideMode 
                                    ? (() => {
                                        const rolesToShow = selectedRolesToClone.filter(role => hiddenRoles.includes(role));
                                        const rolesToHide = selectedRolesToClone.filter(role => !hiddenRoles.includes(role));
                                        
                                        if (rolesToShow.length > 0 && rolesToHide.length > 0) {
                                            return `Show ${rolesToShow.length} and Hide ${rolesToHide.length} (${selectedRolesToClone.length} total)`;
                                        } else if (rolesToShow.length > 0) {
                                            return `Show ${rolesToShow.length} hidden roles`;
                                        } else if (rolesToHide.length > 0) {
                                            return `Hide ${rolesToHide.length} visible roles`;
                                        } else {
                                            return `Select roles to show/hide (${selectedRolesToClone.length} selected)`;
                                        }
                                    })()
                                    : `Select roles to clone (${selectedRolesToClone.length} selected)`
                                }
                            </h4>
                            <div className="max-h-[300px] overflow-y-auto border border-grayOne rounded-lg">
                                {filteredRoles.length > 0 ? (
                                    filteredRoles.map((role) => (
                                        <div
                                            key={role}
                                            onClick={() => toggleRoleSelection(role)}
                                            className={`flex items-center px-4 py-3 cursor-pointer hover:bg-grayOne ${
                                                selectedRolesToClone.includes(role) ? 'bg-primaryThree' : ''
                                            }`}
                                        >
                                            <input
                                                type="checkbox"
                                                checked={selectedRolesToClone.includes(role)}
                                                onChange={() => toggleRoleSelection(role)}
                                                className="mr-3 w-4 h-4 text-primaryColor border-grayFour rounded focus:ring-primaryColor"
                                            />
                                            <span className="text-sm text-grayFive">{role}</span>
                                            {isShowHideMode && hiddenRoles.includes(role) && (
                                                <span className="ml-auto text-xs text-grayThree bg-grayOne px-2 py-1 rounded">
                                                    Hidden
                                                </span>
                                            )}
                                        </div>
                                    ))
                                ) : (
                                    <div className="px-4 py-3 text-sm text-grayThree">
                                        No roles found
                                    </div>
                                )}
                            </div>
                        </div>
                        
                        <div className="flex justify-end gap-3">
                            <button
                                onClick={closeCloneSelector}
                                className="px-4 py-2 border border-grayTwo rounded-lg text-grayFour hover:bg-grayOne"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={isShowHideMode ? executeShowHide : executeClone}
                                disabled={selectedRolesToClone.length === 0}
                                className="px-4 py-2 bg-primaryColor text-white rounded-lg hover:bg-primaryColor/90 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {isShowHideMode 
                                    ? (() => {
                                        const rolesToShow = selectedRolesToClone.filter(role => hiddenRoles.includes(role));
                                        const rolesToHide = selectedRolesToClone.filter(role => !hiddenRoles.includes(role));
                                        
                                        if (rolesToShow.length > 0 && rolesToHide.length > 0) {
                                            return `Apply Changes`;
                                        } else if (rolesToShow.length > 0) {
                                            return `Show ${rolesToShow.length}`;
                                        } else if (rolesToHide.length > 0) {
                                            return `Hide ${rolesToHide.length}`;
                                        } else {
                                            return `Apply`;
                                        }
                                    })()
                                    : `Clone ${selectedRolesToClone.length > 0 ? `(${selectedRolesToClone.length})` : ''}`
                                }
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </DashboardLayout>
    )
}

export default Page