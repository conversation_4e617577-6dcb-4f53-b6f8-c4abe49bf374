import React from 'react';

const PersonPinCircle = () => {
    return (
        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_1684_4678" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="20" height="20">
                <rect width="20" height="20" fill="#D9D9D9" />
            </mask>
            <g mask="url(#mask0_1684_4678)">
                <path d="M9.99998 17.7709C9.80553 17.7709 9.61109 17.7361 9.41665 17.6667C9.2222 17.5972 9.04859 17.4931 8.89581 17.3542C7.99304 16.5209 7.19442 15.7084 6.49998 14.9167C5.80554 14.125 5.22567 13.3577 4.7604 12.6146C4.29512 11.8715 3.94095 11.1563 3.6979 10.4688C3.45484 9.78127 3.33331 9.12502 3.33331 8.50002C3.33331 6.41669 4.00345 4.75697 5.34373 3.52085C6.68401 2.28474 8.23609 1.66669 9.99998 1.66669C11.7639 1.66669 13.316 2.28474 14.6562 3.52085C15.9965 4.75697 16.6666 6.41669 16.6666 8.50002C16.6666 9.12502 16.5451 9.78127 16.3021 10.4688C16.059 11.1563 15.7048 11.8715 15.2396 12.6146C14.7743 13.3577 14.1944 14.125 13.5 14.9167C12.8055 15.7084 12.0069 16.5209 11.1041 17.3542C10.9514 17.4931 10.7778 17.5972 10.5833 17.6667C10.3889 17.7361 10.1944 17.7709 9.99998 17.7709ZM9.99998 8.33335C10.4583 8.33335 10.8507 8.17016 11.1771 7.84377C11.5035 7.51738 11.6666 7.12502 11.6666 6.66669C11.6666 6.20835 11.5035 5.81599 11.1771 5.4896C10.8507 5.16321 10.4583 5.00002 9.99998 5.00002C9.54165 5.00002 9.14929 5.16321 8.8229 5.4896C8.49651 5.81599 8.33331 6.20835 8.33331 6.66669C8.33331 7.12502 8.49651 7.51738 8.8229 7.84377C9.14929 8.17016 9.54165 8.33335 9.99998 8.33335ZM9.99998 12.5C10.5833 12.5 11.1354 12.382 11.6562 12.1459C12.1771 11.9097 12.6389 11.5764 13.0416 11.1459C13.1111 11.0764 13.1632 10.9965 13.1979 10.9063C13.2326 10.816 13.25 10.7222 13.25 10.625C13.25 10.4722 13.2118 10.3334 13.1354 10.2084C13.059 10.0834 12.9583 9.97919 12.8333 9.89585C12.3889 9.65974 11.9305 9.47919 11.4583 9.35419C10.9861 9.22919 10.5 9.16669 9.99998 9.16669C9.49998 9.16669 9.01387 9.22919 8.54165 9.35419C8.06942 9.47919 7.61109 9.65974 7.16665 9.89585C7.04165 9.9653 6.94442 10.0625 6.87498 10.1875C6.80554 10.3125 6.77081 10.4514 6.77081 10.6042C6.77081 10.7014 6.7847 10.7952 6.81248 10.8854C6.84026 10.9757 6.88887 11.0625 6.95831 11.1459C7.36109 11.5764 7.8229 11.9097 8.34373 12.1459C8.86456 12.382 9.41665 12.5 9.99998 12.5Z" fill="#1E62E0" />
            </g>
        </svg>
    )
}

export default PersonPinCircle