import React from 'react'

const SwapVertical = () => {
    return (
        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
            <mask id="mask0_1010_9423" style={{maskType: 'alpha'}} maskUnits="userSpaceOnUse" x="0" y="0" width="12" height="12">
            <rect width="12" height="12" fill="#D9D9D9"/>
            </mask>
            <g mask="url(#mask0_1010_9423)">
            <path d="M4.50039 6.50003C4.35872 6.50003 4.23997 6.45211 4.14414 6.35628C4.04831 6.26044 4.00039 6.14169 4.00039 6.00003V2.91253L3.06289 3.85003C2.97122 3.94169 2.85664 3.98753 2.71914 3.98753C2.58164 3.98753 2.46289 3.94169 2.36289 3.85003C2.26289 3.75003 2.21289 3.63128 2.21289 3.49378C2.21289 3.35628 2.26289 3.23753 2.36289 3.13753L4.15039 1.35002C4.20039 1.30002 4.25456 1.26461 4.31289 1.24377C4.37122 1.22294 4.43372 1.21252 4.50039 1.21252C4.56706 1.21252 4.62956 1.22294 4.68789 1.24377C4.74622 1.26461 4.80039 1.30002 4.85039 1.35002L6.65039 3.15003C6.75039 3.25003 6.79831 3.36669 6.79414 3.50003C6.78997 3.63336 6.73789 3.75003 6.63789 3.85003C6.53789 3.94169 6.42122 3.98961 6.28789 3.99378C6.15456 3.99794 6.03789 3.95003 5.93789 3.85003L5.00039 2.91253V6.00003C5.00039 6.14169 4.95247 6.26044 4.85664 6.35628C4.76081 6.45211 4.64206 6.50003 4.50039 6.50003ZM7.50039 10.7875C7.43372 10.7875 7.37122 10.7771 7.31289 10.7563C7.25456 10.7354 7.20039 10.7 7.15039 10.65L5.35039 8.85003C5.25039 8.75003 5.20247 8.63336 5.20664 8.50003C5.21081 8.36669 5.26289 8.25003 5.36289 8.15003C5.46289 8.05836 5.57956 8.01044 5.71289 8.00628C5.84622 8.00211 5.96289 8.05003 6.06289 8.15003L7.00039 9.08753V6.00003C7.00039 5.85836 7.04831 5.73961 7.14414 5.64378C7.23997 5.54794 7.35872 5.50003 7.50039 5.50003C7.64206 5.50003 7.76081 5.54794 7.85664 5.64378C7.95247 5.73961 8.00039 5.85836 8.00039 6.00003V9.08753L8.93789 8.15003C9.02956 8.05836 9.14414 8.01253 9.28164 8.01253C9.41914 8.01253 9.53789 8.05836 9.63789 8.15003C9.73789 8.25003 9.78789 8.36878 9.78789 8.50628C9.78789 8.64378 9.73789 8.76253 9.63789 8.86253L7.85039 10.65C7.80039 10.7 7.74622 10.7354 7.68789 10.7563C7.62956 10.7771 7.56706 10.7875 7.50039 10.7875Z" fill="#57585E"/>
            </g>
        </svg>

    )
}

export default SwapVertical