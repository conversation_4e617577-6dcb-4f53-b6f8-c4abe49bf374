import React from 'react'

const UsaLarge = () => {
    return (
        <svg width="46" height="46" viewBox="0 0 46 46" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_6762_41785)">
                <path d="M2.875 3H42.875V43H2.875" fill="#BD3D44" />
                <path d="M2.875 7.53125H42.875H2.875ZM2.875 13.7031H42.875H2.875ZM2.875 19.875H42.875H2.875ZM2.875 26.0469H42.875H2.875ZM2.875 32.2188H42.875H2.875ZM2.875 38.3906H42.875H2.875Z" fill="black" />
                <path d="M2.875 7.53125H42.875M2.875 13.7031H42.875M2.875 19.875H42.875M2.875 26.0469H42.875M2.875 32.2188H42.875M2.875 38.3906H42.875" stroke="white" strokeWidth="4.16667" />
                <path d="M2.875 3H33.3438V24.4844H2.875V3Z" fill="#192F5D" />
                <g clipPath="url(#clip1_6762_41785)">
                    <path d="M-3.79297 3H49.5404V43H-3.79297" fill="#BD3D44" />
                    <path d="M-3.79297 7.60828H49.5404H-3.79297ZM-3.79297 13.7499H49.5404H-3.79297ZM-3.79297 19.9166H49.5404H-3.79297ZM-3.79297 26.0833H49.5404H-3.79297ZM-3.79297 32.2499H49.5404H-3.79297ZM-3.79297 38.4166H49.5404H-3.79297Z" fill="black" />
                    <path d="M-3.79297 6.06641V9.14974H49.5404V6.06641H-3.79297ZM-3.79297 12.2081V15.2914H49.5404V12.2081H-3.79297ZM-3.79297 18.3747V21.4581H49.5404V18.3747H-3.79297ZM-3.79297 24.5414V27.6247H49.5404V24.5414H-3.79297ZM-3.79297 30.7081V33.7914H49.5404V30.7081H-3.79297ZM-3.79297 36.8747V39.9581H49.5404V36.8747H-3.79297Z" fill="white" />
                    <path d="M-3.79297 3H26.607V24.5417H-3.79297" fill="#192F5D" />
                    <path d="M24.04 21.1669L24.79 23.4169L22.8734 22.0002H25.2067L23.29 23.4169L24.04 21.1669ZM19.04 21.1669L19.79 23.4169L17.8734 22.0002H20.2067L18.29 23.4169L19.04 21.1669ZM13.9567 21.1669L14.7067 23.4169L12.79 22.0002H15.1234L13.2067 23.4169L13.9567 21.1669ZM8.87337 21.1669L9.62337 23.4169L7.70671 22.0002H10.04L8.12337 23.4169L8.87337 21.1669ZM3.79004 21.1669L4.54004 23.4169L2.62337 22.0002H4.95671L3.04004 23.4169L3.79004 21.1669ZM-1.29329 21.1669L-0.543294 23.4169L-2.45996 22.0002H-0.126628L-2.04329 23.4169L-1.29329 21.1669ZM21.54 19.0002L22.29 21.2502L20.3734 19.8335H22.7067L20.79 21.2502L21.54 19.0002ZM16.4567 19.0002L17.2067 21.2502L15.29 19.8335H17.6234L15.7067 21.2502L16.4567 19.0002ZM11.4567 19.0002L12.2067 21.2502L10.29 19.8335H12.6234L10.7067 21.2502L11.4567 19.0002ZM6.37337 19.0002L7.12337 21.2502L5.20671 19.8335H7.54004L5.62337 21.2502L6.37337 19.0002ZM1.29004 19.0002L2.04004 21.2502L0.123372 19.8335H2.45671L0.540039 21.2502L1.29004 19.0002ZM24.04 16.8335L24.79 19.0835L22.8734 17.6669H25.2067L23.29 19.0835L24.04 16.8335ZM19.04 16.8335L19.79 19.0835L17.8734 17.6669H20.2067L18.29 19.0835L19.04 16.8335ZM13.9567 16.8335L14.7067 19.0835L12.79 17.6669H15.1234L13.2067 19.0835L13.9567 16.8335ZM8.87337 16.8335L9.62337 19.0835L7.70671 17.6669H10.04L8.12337 19.0835L8.87337 16.8335ZM3.79004 16.8335L4.54004 19.0835L2.62337 17.6669H4.95671L3.04004 19.0835L3.79004 16.8335ZM-1.29329 16.8335L-0.543294 19.0835L-2.45996 17.6669H-0.126628L-2.04329 19.0835L-1.29329 16.8335ZM21.54 14.7502L22.29 17.0002L20.3734 15.5835H22.7067L20.79 17.0002L21.54 14.7502ZM16.4567 14.7502L17.2067 17.0002L15.29 15.5835H17.6234L15.7067 17.0002L16.4567 14.7502ZM11.4567 14.7502L12.2067 17.0002L10.29 15.5835H12.6234L10.7067 17.0002L11.4567 14.7502ZM6.37337 14.7502L7.12337 17.0002L5.20671 15.5835H7.54004L5.62337 17.0002L6.37337 14.7502ZM1.29004 14.7502L2.04004 17.0002L0.123372 15.5835H2.45671L0.540039 17.0002L1.29004 14.7502ZM24.04 12.5835L24.79 14.8335L22.8734 13.4169H25.2067L23.29 14.8335L24.04 12.5835ZM19.04 12.5835L19.79 14.8335L17.8734 13.4169H20.2067L18.29 14.8335L19.04 12.5835ZM13.9567 12.5835L14.7067 14.8335L12.79 13.4169H15.1234L13.2067 14.8335L13.9567 12.5835ZM8.87337 12.5835L9.62337 14.8335L7.70671 13.4169H10.04L8.12337 14.8335L8.87337 12.5835ZM3.79004 12.5835L4.54004 14.8335L2.62337 13.4169H4.95671L3.04004 14.8335L3.79004 12.5835ZM-1.29329 12.5835L-0.543294 14.8335L-2.45996 13.4169H-0.126628L-2.04329 14.8335L-1.29329 12.5835ZM21.54 10.4169L22.29 12.6669L20.3734 11.2502H22.7067L20.79 12.6669L21.54 10.4169ZM16.4567 10.4169L17.2067 12.6669L15.29 11.2502H17.6234L15.7067 12.6669L16.4567 10.4169ZM11.4567 10.4169L12.2067 12.6669L10.29 11.2502H12.6234L10.7067 12.6669L11.4567 10.4169ZM6.37337 10.4169L7.12337 12.6669L5.20671 11.2502H7.54004L5.62337 12.6669L6.37337 10.4169ZM1.29004 10.4169L2.04004 12.6669L0.123372 11.2502H2.45671L0.540039 12.6669L1.29004 10.4169ZM24.04 8.2502L24.79 10.5002L22.8734 9.08354H25.2067L23.29 10.5002L24.04 8.2502ZM19.04 8.2502L19.79 10.5002L17.8734 9.08354H20.2067L18.29 10.5002L19.04 8.2502ZM13.9567 8.2502L14.7067 10.5002L12.79 9.08354H15.1234L13.2067 10.5002L13.9567 8.2502ZM8.87337 8.2502L9.62337 10.5002L7.70671 9.08354H10.04L8.12337 10.5002L8.87337 8.2502ZM3.79004 8.2502L4.54004 10.5002L2.62337 9.08354H4.95671L3.04004 10.5002L3.79004 8.2502ZM-1.29329 8.2502L-0.543294 10.5002L-2.45996 9.08354H-0.126628L-2.04329 10.5002L-1.29329 8.2502ZM21.54 6.08354L22.29 8.33354L20.3734 6.91687H22.7067L20.79 8.33354L21.54 6.08354ZM16.4567 6.08354L17.2067 8.33354L15.29 6.91687H17.6234L15.7067 8.33354L16.4567 6.08354ZM11.4567 6.08354L12.2067 8.33354L10.29 6.91687H12.6234L10.7067 8.33354L11.4567 6.08354ZM6.37337 6.08354L7.12337 8.33354L5.20671 6.91687H7.54004L5.62337 8.33354L6.37337 6.08354ZM1.29004 6.08354L2.04004 8.33354L0.123372 6.91687H2.45671L0.540039 8.33354L1.29004 6.08354ZM24.04 3.91687L24.79 6.16687L22.8734 4.7502H25.2067L23.29 6.16687L24.04 3.91687ZM19.04 3.91687L19.79 6.16687L17.8734 4.7502H20.2067L18.29 6.16687L19.04 3.91687ZM13.9567 3.91687L14.7067 6.16687L12.79 4.7502H15.1234L13.2067 6.16687L13.9567 3.91687ZM8.87337 3.91687L9.62337 6.16687L7.70671 4.7502H10.04L8.12337 6.16687L8.87337 3.91687ZM3.79004 3.91687L4.54004 6.16687L2.62337 4.7502H4.95671L3.04004 6.16687L3.79004 3.91687ZM-1.29329 3.91687L-0.543294 6.16687L-2.45996 4.7502H-0.126628L-2.04329 6.16687L-1.29329 3.91687Z" fill="white" />
                </g>
            </g>
            <rect x="1.76389" y="1.88889" width="42.2222" height="42.2222" rx="21.1111" stroke="#C7C7CA" strokeWidth="2.22222" />
            <defs>
                <clipPath id="clip0_6762_41785">
                    <rect x="2.875" y="3" width="40" height="40" rx="20" fill="white" />
                </clipPath>
                <clipPath id="clip1_6762_41785">
                    <rect width="53.3333" height="40" fill="white" transform="translate(-3.79199 3)" />
                </clipPath>
            </defs>
        </svg>

    )
}

export default UsaLarge