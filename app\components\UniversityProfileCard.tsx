import React from 'react';
import { UniversityProfileCardProps } from '@/types';

const UniversityProfileCard:React.FC<UniversityProfileCardProps> = ({ 
    icon,
    heading,
    children,
    className 
}) => {
    return (
        <div className={`flex flex-col gap-3 border border-tertiary/20 rounded-xl py-4 px-5 ${className}`}>
            <div className="flex gap-2.5 items-center pb-2.5 border-b border-tertiary/15">
                <div className="h-4 w-4 text-primaryColor">
                    {icon}
                </div>
                <h3 className="font-medium text-[13px] line-clamp-1 leading-4">
                    {heading}
                </h3>
            </div>
            <div>
                {children}
            </div>
        </div>
    )
}

export default UniversityProfileCard;
