import React from 'react';
import { SelectedItemsProps } from '@/types';
import X_icon from '@/app/assets/svg/x_icon';

const SelectedItem: React.FC<SelectedItemsProps> = ({
    selectedOptions,
    subjects,
    removeOption
}) => {
    // console.log(selectedOptions)
    return (
        <>
        <p className='mt-4'>
            {selectedOptions.length > 0 && 
            <span className='flex flex-wrap gap-4'>
                {selectedOptions.map((option) => (
                    <span
                        key={option}
                        className='text-grayFive flex items-center bg-grayOne px-1.5 py-0.5 rounded-2xl font-medium text-xs leading-[18px]'
                    >
                        {subjects.find((o) => o.value === option)?.label}
                        <button
                            type='button'
                            className='cursor-pointer ml-1'
                            onClick={() => removeOption(option)}
                        >
                            <X_icon />
                        </button>
                    </span>
                ))}
            </span>
            }
        </p>
        </>
    )
}

export default SelectedItem