import React from 'react';
import { DateRangeSelectProps } from '@/types';
import { 
    Select, 
    SelectItem, 
    SelectValue, 
    SelectContent, 
    SelectTrigger, 
} from '@/components/ui/select';

const DateRangeSelect: React.FC<DateRangeSelectProps> = ({
    options,
    onChange,
    className,
    selectedValue,
    placeholder = 'Select Range'
}) => {
    return (
        <Select onValueChange={onChange} value={selectedValue}>
            <SelectTrigger className={`h-fit ${className} px-5 shadow-none border-none space-x-2 active:border-none focus:ring-0 bg-primaryOne rounded-[50px]`}>
                <SelectValue placeholder={placeholder} />
            </SelectTrigger>
            <SelectContent>
                {options.map((option) => (
                <SelectItem key={option} value={option}>
                    {option}
                </SelectItem>
                ))}
            </SelectContent>
        </Select>
    );
};

export default DateRangeSelect;
