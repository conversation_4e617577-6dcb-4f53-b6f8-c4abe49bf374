import React from 'react';
import Link from 'next/link';
// import Book from '@/app/assets/svg/book';
import Flag from '@/app/assets/svg/flag';
import { CounselorActivitiesData } from '@/common';
import Telephone from '@/app/assets/svg/telephone';
import CalendarSmall from '@/app/assets/svg/calendar-small';
import GraduationCap from '@/app/assets/svg/graduation-cap';
import ChevronForward from '@/app/assets/svg/chevron_forward';

const CounselorActivities = () => {
    return (
        <div className='py-6 px-5 flex flex-col gap-[22px]'>
            <div className='flex justify-between items-center'>
                <h2 className='font-bold text-[18px] leading-[22px] text-grayFive'>
                    Activities
                </h2>
                <Link
                    href={'/counselor-activities'}
                    className='flex gap-0.5 font-medium text-[10px] bg-primaryOne py-1.5 px-2 leading-3 rounded-2xl text-secondaryColor'
                >
                    View All
                    <ChevronForward />
                </Link>
            </div>
            <div>
                <div className=' w-full rounded-lg flex md:flex-row flex-col gap-3'>
                    {CounselorActivitiesData.sections
                        .slice(0, 2)
                        .map((activities, index) => (
                            <div key={index} className='divide-y'>
                                <div className='bg-tertiary/90 text-white px-[15px] py-2 rounded-t-[10px]'>
                                    <h2 className='text-[22px] leading-[27px] font-bold'>
                                        {activities.date}
                                    </h2>
                                </div>
                                {activities.entries.map((entry, entryIndex) => (
                                    <div
                                        key={entryIndex}
                                        className='flex flex-col gap-1.5 bg-primaryOne border-b-[0.5px] border-primaryColor/20 py-2 px-[15px]'
                                    >
                                        <div>
                                            <h3 className='font-medium text-[13px] leading-4 text-primaryColor line-clamp-1'>
                                                {entry.name}
                                            </h3>
                                        </div>
                                        <div className='flex items-center gap-3'>
                                            {(entry.buttonText ===
                                                'Student Enrollment' || entry.buttonText === 'Counseling') ? (
                                                <>
                                                    <span className='text-[10px] leading-[12px] font-medium text-grayFour whitespace-nowrap inline-flex items-center gap-2.5'>
                                                        <Telephone />
                                                        {entry.phone}
                                                    </span>

                                                    <span className='text-[10px] leading-[12px] font-medium text-grayFour inline-flex items-center gap-2.5 line-clamp-1'>
                                                        <Flag />
                                                        {entry.location}
                                                    </span>
                                                </>
                                            ) : (entry.buttonText ==='Application') ? (
                                                <>
                                                    <span className='text-[10px] leading-[12px] font-medium text-grayFour whitespace-nowrap inline-flex items-center gap-2.5'>
                                                        <CalendarSmall />
                                                        {entry.startDate}
                                                    </span>

                                                    <span className='text-[10px] leading-[12px] font-medium text-grayFour inline-flex items-center gap-2.5 line-clamp-1'>
                                                        <GraduationCap />
                                                        <div className='line-clamp-1'>
                                                            {entry.university}  
                                                        </div>
                                                    </span>
                                                </>
                                            ) : (
                                                <span>no activities yet</span>
                                            )}
                                        </div>
                                    </div>
                                ))}

                            </div>
                        ))}
                </div>
            </div>
        </div>
    );
};

export default CounselorActivities;
