import React from 'react'

const NoDepartment = () => {
    return (
        <svg width="252" height="123" viewBox="0 0 252 123" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M222.224 103.872C222.224 103.918 179.141 103.958 126.004 103.958C72.8673 103.958 29.7773 103.928 29.7773 103.872C29.7773 103.815 72.854 103.785 126.004 103.785C179.154 103.785 222.224 103.822 222.224 103.872Z" fill="#C7C7CA" stroke="#C7C7CA" />
            <path d="M40.43 81.9955C40.9935 82.0362 41.5431 82.19 42.046 82.4477C42.5488 82.7054 42.9945 83.0618 43.3566 83.4955C44.0766 84.3682 44.5864 85.3946 44.8466 86.4955C45.41 88.6022 45.0633 91.0922 44.4633 93.1888C42.1833 92.3722 40.7733 89.9389 40.13 88.6589C39.13 86.6389 38.5333 82.3722 40.42 81.9922" fill="#144296" />
            <path d="M47.747 95.7461C47.4047 95.2151 47.2089 94.603 47.1796 93.9718C47.1504 93.3407 47.2886 92.7131 47.5804 92.1527C47.8813 91.5977 48.2898 91.1081 48.782 90.7126C49.2741 90.3171 49.8402 90.0237 50.447 89.8494C51.0037 89.6761 51.6504 89.6061 52.1337 89.9294C52.3516 90.0942 52.5213 90.3144 52.6251 90.5671C52.7288 90.8198 52.7629 91.0957 52.7237 91.3661C52.6362 91.9076 52.4061 92.4162 52.057 92.8394C50.947 94.3427 49.6004 95.5927 47.7237 95.7461" fill="#144296" />
            <path d="M47.9995 102.79C47.9501 102.648 47.9154 102.502 47.8962 102.353C47.8362 102.04 47.7562 101.637 47.6595 101.153C47.3603 99.8299 47.277 98.4667 47.4129 97.1167C47.57 95.7601 48.0867 94.47 48.9095 93.38C49.1609 93.0541 49.4508 92.7597 49.7729 92.5033C49.8604 92.4339 49.9516 92.3694 50.0462 92.31C50.0763 92.285 50.11 92.2648 50.1462 92.25C49.7245 92.6102 49.3394 93.011 48.9962 93.4467C48.2168 94.5378 47.726 95.8083 47.5695 97.14C47.3562 98.6934 47.6195 100.117 47.7729 101.14C47.8529 101.65 47.9195 102.063 47.9562 102.35C47.9824 102.495 47.9969 102.642 47.9995 102.79Z" fill="#263238" />
            <path d="M41.1621 85.207C41.2002 85.2596 41.2315 85.3167 41.2554 85.377C41.3154 85.5104 41.3954 85.677 41.4888 85.887C41.6921 86.3304 41.9754 86.9737 42.3188 87.7704C43.0054 89.367 43.9254 91.587 44.8854 94.0604C45.8454 96.5337 46.6621 98.7904 47.2188 100.434C47.5055 101.254 47.7288 101.92 47.8855 102.384C47.9555 102.604 48.0088 102.78 48.0521 102.917C48.0759 102.978 48.0927 103.042 48.1021 103.107C48.0683 103.05 48.0415 102.99 48.0221 102.927C47.9721 102.794 47.9088 102.62 47.8254 102.404L47.1221 100.47C46.5254 98.8404 45.6921 96.587 44.7354 94.117C43.7788 91.647 42.8721 89.4204 42.2121 87.8137C41.8788 87.0237 41.6221 86.3804 41.4321 85.9104C41.3454 85.697 41.2754 85.527 41.2221 85.3904C41.1971 85.331 41.177 85.2697 41.1621 85.207Z" fill="#263238" />
            <path d="M45.2898 98.1308C44.6039 96.9889 43.6561 96.0266 42.5247 95.3235C41.3933 94.6205 40.1107 94.1968 38.7831 94.0875C38.1165 94.0408 37.2965 94.1542 36.9965 94.7542C36.6965 95.3542 37.0965 96.0875 37.5565 96.5742C38.5338 97.5966 39.792 98.3068 41.1724 98.6153C42.5528 98.9237 43.9936 98.8166 45.3132 98.3075" fill="#144296" />
            <path d="M39.8106 95.8936C39.9519 95.8865 40.0936 95.8921 40.2339 95.9102C40.3982 95.9191 40.5619 95.938 40.7239 95.9669C40.9139 96.0036 41.1372 96.0236 41.3706 96.0902C41.6314 96.1454 41.8885 96.2166 42.1406 96.3036C42.4325 96.3974 42.7186 96.5087 42.9972 96.6369C43.638 96.924 44.2441 97.2829 44.8039 97.7069C45.3571 98.1409 45.8583 98.6375 46.2972 99.1869C46.4868 99.426 46.6615 99.6765 46.8205 99.9369C46.9643 100.162 47.0956 100.394 47.2139 100.634C47.314 100.828 47.4031 101.029 47.4805 101.234C47.5474 101.385 47.6042 101.541 47.6505 101.7C47.6999 101.833 47.7367 101.97 47.7605 102.11C47.7239 102.11 47.5639 101.537 47.1139 100.684C46.9913 100.454 46.8567 100.232 46.7105 100.017C46.5481 99.7644 46.3723 99.5207 46.1839 99.2869C45.312 98.2095 44.2002 97.3511 42.9372 96.7802C42.6472 96.6536 42.3706 96.5336 42.1006 96.4469C41.8527 96.3566 41.6001 96.2798 41.3439 96.2169C40.4106 95.9602 39.8072 95.9302 39.8106 95.8936Z" fill="#263238" />
            <path d="M100.667 34.4427L88.8673 95.0428L87.334 102.976L166.001 102.596C169.534 102.579 172.667 99.4428 173.797 94.7828L187.751 37.5727C188.614 34.036 186.721 30.356 184.041 30.3594L104.401 30.4427C102.667 30.4394 101.114 32.0794 100.667 34.4427Z" fill="#1E62E0" fillOpacity="0.4" />
            <g opacity="0.5">
                <path d="M100.667 34.4427L88.8673 95.0428L87.334 102.976L166.001 102.596C169.534 102.579 172.667 99.4428 173.797 94.7828L187.751 37.5727C188.614 34.036 186.721 30.356 184.041 30.3594L104.401 30.4427C102.667 30.4394 101.114 32.0794 100.667 34.4427Z" fill="#144296" />
            </g>
            <path d="M158.464 90.9302L150.13 23.7934C149.797 21.0434 147.6 18.99 145.01 19L126.744 19.0567C126.065 19.0644 125.395 19.2102 124.774 19.4852C124.153 19.7603 123.595 20.1589 123.134 20.6567L114.467 29.8801L63.667 30.1534C60.5636 30.1701 58.177 33.1101 58.587 36.4101L66.3236 98.4603C66.657 101.207 68.8536 103.257 71.437 103.254L159.987 103.077C169.847 103.28 170.79 100.37 170.79 100.37C159.47 102.874 158.464 90.9469 158.464 90.9302Z" fill="#E9F0FF" />
            <path d="M118.623 76.0151C119.533 76.8342 119.595 78.2406 118.761 79.1368C117.946 80.0114 116.581 80.0717 115.692 79.2723L95.9499 61.5099C95.0394 60.6908 94.9773 59.2844 95.812 58.3882C96.6266 57.5136 97.9919 57.4533 98.8804 58.2527L118.623 76.0151Z" fill="#1E62E0" />
            <path d="M114.557 57.6985C115.382 56.8122 116.766 56.7512 117.666 57.5614C118.588 58.3913 118.651 59.8158 117.805 60.7238L100.021 79.8244C99.1955 80.711 97.8118 80.7724 96.911 79.9625C95.9877 79.1323 95.9244 77.7064 96.7706 76.7978L114.557 57.6985Z" fill="#1E62E0" />
        </svg>
    )
}

export default NoDepartment