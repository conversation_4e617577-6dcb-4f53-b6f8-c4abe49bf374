import React from 'react';
import Cross from '../assets/svg/Cross';
import { FilterOptionProps } from '@/types';

const FilterOption: React.FC<FilterOptionProps> = ({
    option,
    onRemove
}) => {
    return (
        <div className='flex items-center gap-1 rounded-[16px] py-0.5 px-1.5 bg-white border-[0.5px] border-grayTwo'>
            <span className='font-medium text-xs leading-4 text-grayFive'>{option}</span>
            {onRemove && (
                <button onClick={() => onRemove(option)}>
                    <Cross className='text-grayFive w-3 h-3 cursor-pointer' />
                </button>
            )}
        </div>
    )
}

export default FilterOption