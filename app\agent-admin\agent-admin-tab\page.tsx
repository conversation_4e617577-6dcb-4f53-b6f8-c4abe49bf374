import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Call from '@/app/assets/svg/Call';
import Trash from '../../assets/svg/trash';
import Heading from '@/app/components/Heading';
import linkedin from '../../assets/svg/linkedin';
import Bookmark from '../../assets/svg/bookmark';
import GoldStar from '../../assets/svg/GoldStar';
import EmailInbox from '../../assets/svg/emailInbox';
import ProgressBar from '../../components/ProgressBar';
import { ChartConfig } from '@/components/ui/chart';
import { PieCharts } from '../../components/PieCharts';
import Universities from '../../assets/svg/universities';
import { DonutChart } from '../../components/DonutChart';
import ArrowForward from '../../assets/svg/arrow_forward';
import Arrow_outward from '../../assets/svg/ArrowOutward';
import JaneRafayel from '@/app/assets/img/JaneRafayel.png';
import CalendarSmall from '../../assets/svg/calendar-small';
import StatCardLayout from '../../components/StatCardLayout';
import AssistKnowledge from '../../assets/svg/AssistKnowledge';
import SignedAgreement from '../../assets/svg/SignedAgreement';
import HorizontalChart from '../../components/HorizontalChart';
import TooltipCountry from '@/app/components/ui/TooltipCountry';
import { SingleBarChart } from '../../components/SingleBarChart';
import MultipleBarChart from '../../components/MultipleBarChart';
import StatInfoCardSmall from '../../components/StatInfoCardSmall';
import DashboardLayout from '../../components/layout/DashboardLayout';
import CircularProgressBar from '../../components/CircularProgressBar';
import { AreaChartGradient } from '../../components/AreaChartGradient';
import Chicago from '@/app/assets/img/the-university-of-chicago.png';
import ApplygoalCertificate from '../../assets/svg/ApplygoalCertificate';
import { mostRecentVisaSuccess, employeePerformanceList } from '@/common';
import StatCardVisitorIcon from '@/app/assets/svg/stat-card-visitor-icon.svg';
import { CounselorBarChartData, TeamPerformanceData, UniversityOnboardingData } from '@/common';
import { 
    Tabs, 
    TabsContent, 
    TabsList, 
    TabsTrigger 
} from '@/components/ui/tabs';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';

const lines = [
    { 
        dataKey: "This", 
        label: "This Year", 
        color: "#1E62E0", 
        strokeWidth: 5 
    },
    { 
        dataKey: "Previous", 
        label: "Previous Year", 
        color: "#E7EFFF", 
        strokeWidth: 5 
    }
];

const tabData = [
    { 
        label: 'General', 
        value: 'General',
        Data: [
            {
                CardLabel: 'Visitors/ Leads',
                CardValue: 32,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Students',
                CardValue: 120,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Applications',
                CardValue: 134,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'VISA Success',
                CardValue: 134,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Employee',
                CardValue: 40,
                Decrease: 2.2,
                Increase: undefined, 
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Employee',
                CardValue: 1200,
                Decrease: 2.2,
                Increase: undefined, 
                icon : StatCardVisitorIcon
            },
        ]

    },    
    { 
        label: "Students", 
        value: "Students",
        Data: [
            {
                CardLabel: 'Students',
                CardValue: 134,
                Decrease: undefined, 
                Increase: undefined, 
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Pending',
                CardValue: 120,
                Decrease: undefined,
                Increase: undefined,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'In Process',
                CardValue: 134,
                Decrease: undefined, 
                Increase: undefined,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Success',
                CardValue: 60,
                Increase: undefined,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Rejected',
                CardValue: 20,
                Decrease: undefined, 
                Increase: undefined,
                icon : StatCardVisitorIcon
            },
        ]
    },  
    { 
        label: "Applications", 
        value: "Applications",
        Data: [
            {
                CardLabel: 'Applications',
                CardValue: 120,
                Decrease: undefined, 
                Increase: undefined, 
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'In Process',
                CardValue: 134,
                Decrease: undefined, 
                Increase: undefined, 
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Accepted',
                CardValue: 40,
                Decrease: undefined, 
                Increase: undefined, 
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Rejected',
                CardValue: 34,
                Decrease: undefined, 
                Increase: undefined, 
                icon : StatCardVisitorIcon
            },
        ] 
    },
    { 
        label: "Leads", 
        value: "Leads",
        Data: [
            {
                CardLabel: 'Office Visit',
                CardValue: 120,
                Decrease: undefined, 
                Increase: undefined, 
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Online',
                CardValue: 134,
                Decrease: undefined, 
                Increase: undefined, 
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Associates',
                CardValue: 40,
                Decrease: undefined, 
                Increase: undefined, 
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Events',
                CardValue: 34,
                Decrease: undefined, 
                Increase: undefined, 
                icon : StatCardVisitorIcon
            },
        ]
    },
    { 
        label: "HR", 
        value: "HR",
        Data: [
            {
                CardLabel: 'Total Employee',
                CardValue: 472,
                Decrease: undefined, 
                Increase: undefined,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Active Employee',
                CardValue: 454,
                Decrease: undefined, 
                Increase: undefined,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Total Salary',
                CardValue: 18767.67,
                Decrease: undefined, 
                Increase: undefined,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Recent Job Posts',
                CardValue: 12,
                Decrease: undefined, 
                Increase: undefined,
                icon : StatCardVisitorIcon
            },
        ] 
    },
    { 
        label: "Accounts",
        value: "Accounts",
        Data: [
            {
                CardLabel: 'Total Agencies',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Total Universities',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Agencies Students',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Agencies Applications',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Agencies VISA Success',
                CardValue: 200,
                Decrease: 2.2,
                icon : StatCardVisitorIcon
            },
        ]
    },
    { 
        label: 'Reports', 
        value: 'Reports',
        Data: [
            {
                CardLabel: 'Total Agencies',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Total Universities',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Agencies Students',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Agencies Applications',
                CardValue: 200,
                Increase: 2.2,
                icon : StatCardVisitorIcon
            },
            {
                CardLabel: 'Agencies VISA Success',
                CardValue: 200,
                Decrease: 2.2,
                icon : StatCardVisitorIcon
            },
        ]
    },
];

const chartConfig = {
    applications: { label: 'Accepted', color: '#1E62E0' },
    success: { label: 'Rejected', color: '#E3E7FC' },
};

const TopDestinationChartConfig = {
    agency: { 
        label: 'Total Student', 
        color: '#1E62E0', 
        name: 'Preferred Country Applications' 
    },
}
const TeamPerformanceChartConfig = {
    performance: { 
        label: 'Performance', 
        color: '#1E62E0', 
        name: 'Performance' 
    },
}

const ApplicationData = {
    'This Year': [
        { month: 'August', applications: 305, success: 300 },
        { month: 'September', applications: 137, success: 220 },
        { month: 'October', applications: 43, success: 390},
        { month: 'November', applications: 409, success: 330 },
        { month: 'December', applications: 214, success: 140 },
    ],
    'This Month': [
        { day: 'Week 1', applications: 14, success: 330 },
        { day: 'Week 2', applications: 300, success: 450 },
        { day: 'Week 3', applications: 430, success: 250 },
        { day: 'Week 4', applications: 100, success: 350 },
    ],
    'This Week': [
        { day: 'Monday', applications: 220, success: 30 },
        { day: 'Tuesday', applications: 600, success: 335 },
        { day: 'Wednesday', applications: 300, success: 40 },
        { day: 'Thursday', applications: 890, success: 225},
        { day: 'Friday', applications: 90, success: 50 },
        { day: 'Saturday', applications: 200, success: 55},
        { day: 'Sunday', applications: 110, success: 60 },
    ],
    Today: [
        { hour: '4 PM', applications: 30, success: 25 },
    ],
};

const revenueData =  [
        { category: "Overall Revenue", value: 1000, fill: "#E3E7FC" },
        { category: "Net Profit", value: 600, fill: "#1E62E0" },
        // { category: "Service Charge", value: 400, fill: "#FF6384" },
];

const revenueChartConfig = {
    value: {
        label: 'Value',
    },
    'Overall Revenue': {
        label: 'Overall Revenue',
        color: '#D2E3FC',
    },
    'Net Profit': {
        label: 'Net Profit',
        color: '#1E62E0',
    },
    // 'Service Charge': {
    //     label: 'Service Charge',
    //     color: '#dd3384',
    // },
} satisfies ChartConfig;

const options = ['This Year', 'This Month'];

const performanceStyles: Record<string, string> = {
    Good: 'bg-[#ECFDF3] text-[#027A48]', 
    Avarage: 'bg-[#F7E1C140] text-[#F2A735]', 
    Weak: 'bg-[#FF3B301A] text-[#FF3B30]', 
};

const visaSuccessTableHead = [
    'ID',
    'Name',
    'Intake',
    'Program',
    'Country'
];

const employeePerformanceTableHead = [
    'ID',
    'Name',
    'Designation',
    'Performance',
    'Progress'
]; 

const ProgramLevelData = [
    { label: "2 year Undergraduate Diploma", value: 536 },
    { label: "3 year Bachelor’s Degree", value: 786 },
    { label: "4 year Bachelor’s Degree", value: 852 },
    { label: "1 year Master’s Degree", value: 333 },
];

const LanguagePreferenceData = [
        { category: "IELTS", value: 1000, fill: "#1E62E0" },
        { category: "Duolingo", value: 100, fill: "#144296" },
        { category: "TOEFL", value: 300, fill: "#E3E7FC" },
        { category: "MOI", value: 200, fill: "#80ACFF" },
        { category: "SAT/ GRE/ GMAT", value: 100, fill: "#EFF0F0" },
    ];

const SocialLeadsData = [
    { value: 336, Icon: linkedin},
    { value: 786, Icon: linkedin},
    { value: 852, Icon: linkedin},
    { value: 333, Icon: linkedin},
    { value: 213, Icon: linkedin},
]

const page = () => {
    return (
        <DashboardLayout>
            <div className='flex flex-col gap-10 py-5'>
                <div className='sticky top-0 z-20 bg-primaryOne py-4 w-full flex justify-between items-center'>
                    <Heading level='h1'>
                        Welcome Michael!
                    </Heading>
                    <div className='text-primaryColor md:text-sm text-[10px] md:leading-[17px] leading-3 font-semibold bg-white rounded-[50px]'>
                        <Select>
                            <SelectTrigger className='h-fit py-2.5 px-5 shadow-none drop-shadow-[0px_1px_4px_0px_rgba(0,0,0,0.05)] border-none space-x-2 active:border-none focus:ring-0 bg-primaryColor text-white rounded-full'>
                                <SelectValue placeholder='All Branch' />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value='all-branch'>
                                    All Branch
                                </SelectItem>
                                <SelectItem value='gulshan'>Gulshan</SelectItem>
                                <SelectItem value='motijheel'>
                                    Motijheel
                                </SelectItem>
                                <SelectItem value='banani'>Banani</SelectItem>
                            </SelectContent>
                        </Select>
                    </div>
                </div>

                <Tabs defaultValue="General" className="w-full">
                    <TabsList className="sticky top-[70px] z-10 bg-primaryOne flex flex-col md:flex-row gap-2.5 md:items-center items-start md:justify-between justify-start md:mb-0 mb-28 pb-6  rounded-none border-b px-0">
                        <div className="grid md:grid-cols-8 grid-cols-2 gap-2.5 place-items-start text-grayFive">
                            {tabData.map((item, index) => (
                                <TabsTrigger
                                    key={index}
                                    value={item.label}
                                    className="py-4 data-[state=active]:text-primaryColor data-[state=active]:shadow-none data-[state=active]:rounded-none rounded-none data-[state=active]:bg-transparent data-[state=active]:border-b-2 -mb-1 border-primaryColor px-0 mr-8"
                                >
                                    {item.label}
                                </TabsTrigger>
                            ))}
                        </div>
                    </TabsList>
                    <TabsContent value="General">
                        <div className="flex flex-col gap-10 py-10">
                            <div className="grid md:grid-cols-4 gap-6">
                                {tabData[0].Data.map((item, index) => (
                                    <StatCardLayout className='min-w-[298px]' key={index}>
                                        <StatInfoCardSmall
                                            imageSrc={item.icon}
                                            description={item.CardLabel}
                                            number={item.CardValue}
                                            decrease={item.Decrease}
                                            increase={item.Increase}
                                        />
                                    </StatCardLayout>
                                ))}
                            </div>
                            <div className='grid md:grid-cols-2 grid-cols-1 gap-6'>
                                <StatCardLayout className='h-full'>
                                    <MultipleBarChart 
                                        title='Applications'
                                        chartConfig={chartConfig}
                                        data={ApplicationData}
                                        deltaValue={0}
                                        chartHeightClass='max-h-[320px]'
                                    />
                                </StatCardLayout>
                                <StatCardLayout className='h-full'>
                                    <DonutChart
                                        data={revenueData}
                                        chartConfig={revenueChartConfig}
                                        title="Revenue Breakdown"
                                        legendPosition='bottom'
                                        chartClassName='max-h-[320px]'
                                        innerCircleRedius={80}
                                    />
                                </StatCardLayout>
                            </div>
                            
                            <div className="grid grid-cols-3 gap-6 my-10">
                                <div className="bg-white rounded-[20px] drop-shadow-[0_1px_4px_rgba(0,0,0,0.05)]">
                                    <div className="gap-6 rounded-tl-[20px] flex justify-between p-5 bg-gradient-to-r from-[#DCE8FF] to-[#E9F0FF38]">
                                        <div>
                                            <span className='font-semibold text-lg leading-[100%] text-graySix'>
                                                Hi, FICC
                                            </span>
                                            <p className='font-normal text-sm leading-[100%] text-graySix mt-1'>
                                                Incredible performance! You rank
                                                among our top performing
                                                partners.
                                            </p>
                                        </div>

                                        <GoldStar />
                                    </div>
                                    <div className='p-5'>
                                        <span className='font-semibold text-lg leading-[100%] text-graySix'>
                                            Your Progress So far
                                        </span>
                                        <div className='flex justify-between mt-3'>
                                            <span className='font-medium text-base leading-[100%] text-graySix'>
                                                Unique Submitted Applicants
                                            </span>
                                            <span className='font-medium text-base leading-[100%] text-grayFive'>
                                                10
                                            </span>
                                        </div>
                                        <div className='py-3'>
                                            <ProgressBar progressValue={20} />
                                        </div>
                                        <p className='font-normal text-base leading-6 text-grayFour'>
                                            You're just 15 unique submitted
                                            applicants away from keeping the
                                            benefits of Gold status, come
                                            January 1st 2025. If you do not meet
                                            the target by 2025 you will be
                                            downgraded to Silver status.
                                        </p>
                                        <button className='mt-[76px] font-medium text-base leading-5 w-full text-primaryColor rounded-[50px] py-3 px-2.5 bg-[#E7EFFF]'>
                                            Review Your Comprehensive Benefits
                                        </button>
                                        <div className='w-full border-b border-[#1E62E066] my-5'></div>
                                        <div className='flex justify-between mt-2'>
                                            <span className='font-semibold text-base leading-5 text-primaryColor'>
                                                Preferred Partners
                                            </span>
                                            <span className='font-normal italic text-xs text-primaryColor'>
                                                Brought to you by ApplyGoal
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div className='space-y-6'>
                                    <div className='bg-white rounded-[20px] drop-shadow-[0_1px_4px_rgba(0,0,0,0.05)] p-5'>
                                        <h4 className='font-bold text-lg leading-[100%] text-grayFive'>
                                            Your Balance
                                        </h4>
                                        <div className='flex justify-between mt-11'>
                                            <span className='font-semibold text-base leading-[100%] text-grayFive'>
                                                Commissions
                                            </span>
                                            <span className='font-bold text-lg leading-[100%] text-grayFive'>
                                                $6,077.65 CAD
                                            </span>
                                        </div>
                                        <div className='flex justify-between pt-6'>
                                            <span className='font-semibold text-base leading-[100%] text-grayFive'>
                                                Commissions
                                            </span>
                                            <span className='font-bold text-lg leading-[100%] text-grayFive'>
                                                $6,077.65 CAD
                                            </span>
                                        </div>
                                        <Link
                                            href={''}
                                            className='mt-[30px] bg-primaryOne rounded-[50px] py-3 px-2.5 flex items-center justify-center gap-3.5 font-medium text-base leading-[100%] text-primaryColor'
                                        >
                                            Request Commission Withdrawal
                                            <Arrow_outward />
                                        </Link>
                                    </div>
                                    <div className='bg-white rounded-[20px] drop-shadow-[0_1px_4px_rgba(0,0,0,0.05)] p-5'>
                                        <h4 className='font-bold text-lg leading-[100%] text-grayFive'>
                                            Popular Links
                                        </h4>
                                        <div className='space-y-4 mt-6'>
                                            <div className='flex gap-3 items-center'>
                                                <ApplygoalCertificate />
                                                <span className='font-normal text-lg leading-6 text-tertiary'>
                                                    ApplyGoal Certificate
                                                </span>
                                            </div>
                                            <div className='flex gap-3 items-center'>
                                                <AssistKnowledge />
                                                <span className='font-normal text-lg leading-6 text-tertiary'>
                                                    Assist Knowledge Base
                                                </span>
                                            </div>
                                            <div className='flex gap-3 items-center'>
                                                <SignedAgreement />
                                                <span className='font-normal text-lg leading-6 text-tertiary'>
                                                    Signed Agreement
                                                </span>
                                            </div>
                                            <div className='flex gap-3 items-center'>
                                                <SignedAgreement />
                                                <span className='font-normal text-lg leading-6 text-tertiary'>
                                                    Signed Agreement
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div className='bg-white rounded-[20px] drop-shadow-[0_1px_4px_rgba(0,0,0,0.05)] p-5 h-1/2'>
                                    <h4 className='font-bold text-lg leading-[100%] text-grayFive'>
                                        Your Account Manager
                                    </h4>
                                    <Image
                                        src={JaneRafayel}
                                        alt='Avatar'
                                        width={56}
                                        height={56}
                                        className='mt-[22px]'
                                    />
                                    <p className='py-4 font-semibold text-lg leading-7 text-grayFive'>
                                        Jane Rafayel
                                    </p>
                                    <div className='flex gap-3 items-center'>
                                        <EmailInbox />
                                        <span className='font-normal text-lg leading-[100%] text-tertiary'>
                                            <EMAIL>
                                        </span>
                                    </div>
                                    <div className='flex gap-3 items-center mt-3.5'>
                                        <Call />
                                        <span className='font-normal text-lg leading-[100%] text-tertiary'>
                                            +8801952-654654
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </TabsContent>
                    <TabsContent value="Students">
                        <div className="flex flex-col gap-10 py-10">
                            <div className="grid md:grid-cols-5 gap-6">
                                {tabData[1].Data.map((item, index) => (
                                    <StatCardLayout className='min-w-[236px]' key={index}>
                                        <StatInfoCardSmall
                                            imageSrc={item.icon}
                                            description={item.CardLabel}
                                            number={item.CardValue}
                                        />
                                    </StatCardLayout>
                                ))}
                            </div>
                            <div className='grid md:grid-cols-2 grid-cols-1 gap-6'>
                                <StatCardLayout className='h-full'>
                                    <SingleBarChart
                                        title='Top Destinations'
                                        chartConfig={TopDestinationChartConfig}
                                        data={CounselorBarChartData}
                                        chartHeightClass='h-[525px]'
                                        textAnchor='middle'
                                    />
                                </StatCardLayout>
                                <StatCardLayout className='h-full'>
                                    <DonutChart
                                        data={LanguagePreferenceData}
                                        chartConfig={revenueChartConfig}
                                        title="Revenue Breakdown"
                                        legendPosition='bottom'
                                        chartClassName='max-h-[525px]'
                                        innerCircleRedius={100}
                                    />
                                </StatCardLayout>
                            </div>
                            <div className='grid grid-cols-2 gap-6'>
                                <StatCardLayout className='p-5'>
                                    <div className='flex justify-between items-center'>
                                        <span className='font-bold text-lg text-grayFive'>Most Recent Visa Success</span>
                                        <Link           
                                            href={'view-all'} 
                                            className='flex gap-1 items-center font-medium text-[10px] text-secondaryColor rounded-[16px] py-2.5 px-4 bg-primaryOne'
                                        >
                                            View All
                                            <ArrowForward className='w-3 h-3 text-secondaryColor' />
                                        </Link>
                                    </div>
                                    <div className='overflow-x-auto border-[0.5px] border-grayOne rounded-[20px] mt-[22px]'>
                                        <table className='bg-white min-w-full text-sm text-left'>
                                            <thead>
                                                <tr>
                                                    {visaSuccessTableHead.map((thead, index) => (
                                                        <th 
                                                            key={index}
                                                            className='p-2.5 font-bold text-xs tracking-[0.4px] leading-5 text-grayFive'
                                                        >
                                                            {thead}
                                                        </th>
                                                    ))}
                                                </tr>
                                            </thead>
                                            <tbody className='border-t border-grayOne divide-y divide-grayOne'>
                                                {mostRecentVisaSuccess.map((success, index) => (
                                                    <tr key={index}>
                                                        <td className='text-xs leading-5 text-graySix p-2.5 font-normal'>{success.id}</td>
                                                        <td className='text-xs leading-5 text-graySix p-2.5 font-normal flex gap-2.5'>{success.name}</td>
                                                        <td className='text-xs leading-5 text-graySix p-2.5 font-normal'>{success.intake}</td>
                                                        <td className='text-xs leading-5 text-graySix p-2.5 font-normal'>{success.program}</td>
                                                        <td className='text-xs leading-5 text-graySix p-2.5 flex items-center gap-2 font-normal'>
                                                            <TooltipCountry 
                                                                logo={<success.country.logo />} 
                                                                label={success.country.label} 
                                                            />
                                                        </td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>
                                </StatCardLayout>
                                <StatCardLayout className='h-full'>
                                    {/* <HorizontalSingleBarChart /> */}
                                    <HorizontalChart title='Program Level' data={ProgramLevelData} />
                                </StatCardLayout>
                            </div>
                        </div>
                    </TabsContent>
                    <TabsContent value='Applications'>
                        <div className="flex flex-col gap-10 py-10">
                            <div className="grid md:grid-cols-4 gap-6">
                                {tabData[2].Data.map((item, index) => (
                                    <StatCardLayout className='min-w-[236px]' key={index}>
                                        <StatInfoCardSmall
                                            imageSrc={item.icon}
                                            description={item.CardLabel}
                                            number={item.CardValue}
                                        />
                                    </StatCardLayout>
                                ))}
                            </div>
                            <div className='grid grid-cols-2 gap-6'>        
                                <StatCardLayout className='p-5'>
                                    <div className='flex justify-between items-center'>
                                        <span className='font-bold text-lg text-grayFive'>Unpaid Applications</span>
                                        <Link           
                                            href={'view-all'} 
                                            className='flex gap-1 items-center font-medium text-[10px] text-secondaryColor rounded-[16px] py-2.5 px-4 bg-primaryOne'
                                        >
                                            View All
                                            <ArrowForward className='w-3 h-3 text-secondaryColor' />
                                        </Link>
                                    </div>
                                    <div className='border border-[#1E62E01A] rounded-[10px] mt-[22px]'>
                                        <div className='flex items-center gap-3 border-b border-[#1E62E01A] p-2.5'>
                                            <span className='font-semibold text-xs leading-4 text-primaryColor'>216</span>
                                            <span className='font-medium text-xs leading-5 tracking-[0.4px] text-graySix'>MD. Moniruzzaman Monir</span>
                                        </div>
                                        <div className='flex items-center justify-between pr-5'>
                                            <div className='flex items-center gap-3 p-2.5'>
                                                <div>
                                                    <Image 
                                                        src={Chicago}
                                                        alt='the university of chicago'
                                                    />
                                                </div>
                                                
                                                <div className='font-medium text-xs text-grayFour space-y-1.5'>
                                                    <div className='flex gap-1.5 items-center'>
                                                        <Universities className='w-3 h-3' />
                                                        <span className=''>New York Institute of Technology - Vancouver (NYIT)</span>
                                                    </div>
                                                    <div className='flex gap-1.5 items-center'>
                                                        <Bookmark className='w-3 h-3' />
                                                        <span>2 year Undergraduate Diploma in CSE</span>
                                                    </div>
                                                    <div className='flex gap-1.5 items-center'>
                                                        <CalendarSmall className='w-3 h-3' />
                                                        <span>Mar 2025</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div>
                                                <span className='font-medium text-sm text-primaryColor'>$ 120.00</span>
                                            </div>
                                            <div>
                                                <Trash />
                                            </div>
                                        </div>
                                    </div>
                                    <div className='border border-[#1E62E01A] rounded-[10px] mt-[22px]'>
                                        <div className='flex justify-between border-b border-[#1E62E01A] p-2.5'>
                                            <div className='flex items-center gap-3'>
                                                <span className='font-semibold text-xs leading-4 text-primaryColor'>369</span>
                                                <span className='font-medium text-xs leading-5 tracking-[0.4px] text-graySix'>Abdullah Nadeem</span>
                                            </div>
                                            <div className='flex -space-x-2'>
                                                <Image
                                                    src={Chicago}
                                                    alt='enroller profile'
                                                    width={20}
                                                    height={20}
                                                    className='rounded-full border-2 border-white'
                                                />
                                                <Image
                                                    src={Chicago}
                                                    alt='enroller profile'
                                                    width={20}
                                                    height={20}
                                                    className='rounded-full border-2 border-white'
                                                />
                                                <Image
                                                    src={Chicago}
                                                    alt='enroller profile'
                                                    width={20}
                                                    height={20}
                                                    className='rounded-full border-2 border-white'
                                                />
                                            </div>
                                        </div>
                                        
                                        <div className='flex items-center justify-between pr-5'>
                                            <div className='flex items-center gap-3 p-2.5'>
                                                <div>
                                                    <Image 
                                                        src={Chicago}
                                                        alt='the university of chicago'
                                                    />
                                                </div>
                                                
                                                <div className='font-medium text-xs text-grayFour space-y-1.5'>
                                                    <div className='flex gap-1.5 items-center'>
                                                        <Universities className='w-3 h-3' />
                                                        <span className=''>New York Institute of Technology - Vancouver (NYIT)</span>
                                                    </div>
                                                    <div className='flex gap-1.5 items-center'>
                                                        <Bookmark className='w-3 h-3' />
                                                        <span>2 year Undergraduate Diploma in CSE</span>
                                                    </div>
                                                    <div className='flex gap-1.5 items-center'>
                                                        <CalendarSmall className='w-3 h-3' />
                                                        <span>Mar 2025</span>
                                                    </div>
                                                </div>
                                            </div>
                                            <div>
                                                <span className='font-medium text-sm text-primaryColor'>$ 120.00</span>
                                            </div>
                                            <div>
                                                <Trash />
                                            </div>
                                        </div>
                                    </div>
                                </StatCardLayout>
                            </div>
                        </div>
                    </TabsContent>
                    <TabsContent value='Leads'>
                        <div className="flex flex-col gap-10 py-10">
                            <div className="grid md:grid-cols-4 gap-6">
                                {tabData[3].Data.map((item, index) => (
                                    <StatCardLayout className='min-w-[236px]' key={index}>
                                        <StatInfoCardSmall
                                            imageSrc={item.icon}
                                            description={item.CardLabel}
                                            number={item.CardValue}
                                        />
                                    </StatCardLayout>
                                ))}
                            </div>
                            <div className='grid grid-cols-2 gap-6'>
                                <StatCardLayout className='h-full'>
                                    <HorizontalChart title='Social Leads' data={SocialLeadsData} />
                                </StatCardLayout>
                                <StatCardLayout className='h-full'>
                                <AreaChartGradient
                                    title="University Onboarding"
                                    data={UniversityOnboardingData}
                                    lines={lines}
                                    xAxisKey="month"
                                />
                                </StatCardLayout>
                            </div>
                        </div>
                    </TabsContent>
                    <TabsContent value='HR' >
                        <div className="flex flex-col gap-10 py-10">
                            <div className="grid md:grid-cols-4 gap-6">
                                {tabData[4].Data.map((item, index) => (
                                    <StatCardLayout className='min-w-[236px]' key={index}>
                                        <StatInfoCardSmall
                                            imageSrc={item.icon}
                                            description={item.CardLabel}
                                            number={item.CardValue}
                                        />
                                    </StatCardLayout>
                                ))}
                            </div>
                            <div className='grid md:grid-cols-2 grid-cols-1 gap-6'>
                                <StatCardLayout className='h-full'>
                                    <SingleBarChart
                                        title='Top Destinations'
                                        chartConfig={TeamPerformanceChartConfig}
                                        data={TeamPerformanceData}
                                        chartHeightClass='max-h-[343px]'
                                        textAnchor='end'
                                        xAxisLabelAngle={-30}
                                        xAxisHeight={80}
                                        xAxisTickMargin={10}
                                    />
                                </StatCardLayout>
                                {/* <StatCardLayout className='h-full'>
                                    <PieCharts />
                                </StatCardLayout> */}
                            </div>
                            <div className='grid grid-cols-3 gap-6'>
                                <StatCardLayout className='p-5 col-span-2'>
                                    <div className='flex justify-between items-center'>
                                        <span className='font-bold text-lg text-grayFive'>Employee Performance</span>
                                        <Link           
                                            href={'view-all'} 
                                            className='flex gap-1 items-center font-medium text-[10px] text-secondaryColor rounded-[16px] py-2.5 px-4 bg-primaryOne'
                                        >
                                            View All
                                            <ArrowForward className='w-3 h-3 text-secondaryColor' />
                                        </Link>
                                    </div>
                                    <div className='overflow-x-auto border-[0.5px] border-grayOne rounded-[20px] mt-[22px]'>
                                        <table className='bg-white min-w-full text-sm text-left'>
                                            <thead>
                                                <tr>
                                                    {employeePerformanceTableHead.map((thead, index) => (
                                                        <th 
                                                            key={index}
                                                            className='p-2.5 font-bold text-xs tracking-[0.4px] leading-5 text-grayFive'
                                                        >
                                                            {thead}
                                                        </th>
                                                    ))}
                                                </tr>
                                            </thead>
                                            <tbody className='border-t border-grayOne divide-y divide-grayOne'>
                                                {employeePerformanceList.map((success, index) => (
                                                    <tr key={index}>
                                                        <td className='text-xs leading-5 text-graySix p-2.5 font-normal'>{success.id}</td>
                                                        <td className='text-xs leading-5 text-graySix p-2.5 font-normal'>{success.name}</td>
                                                        <td className='text-xs leading-5 text-graySix p-2.5 font-normal'>{success.designation}</td>
                                                        <td className='text-xs leading-5 text-graySix p-2.5 font-normal'>
                                                            <span className={`rounded-[16px] ${performanceStyles[success.performance]} inline-block py-0.5 px-2`}>{success.performance}</span>
                                                        </td>
                                                        <td className='text-xs leading-5 text-graySix p-2.5 flex items-center gap-2 font-normal'>
                                                            <CircularProgressBar 
                                                                value={success.progress} 
                                                                size={32} 
                                                                strokeWidth={2} 
                                                            />
                                                        </td>
                                                    </tr>
                                                ))}
                                            </tbody>
                                        </table>
                                    </div>
                                </StatCardLayout>
                            </div>
                        </div>
                    </TabsContent>
                    <TabsContent value='Accounts' >
                        Accounts
                    </TabsContent>
                    <TabsContent value='Reports' >
                        Reports
                    </TabsContent>
                </Tabs>
            </div>
        </DashboardLayout>
    );
};

export default page;
